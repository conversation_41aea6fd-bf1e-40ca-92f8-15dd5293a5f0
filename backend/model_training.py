# FastAPI框架导入所需的类和函数，用于构建API路由、处理文件上传、表单数据和依赖注入
from fastapi import APIRouter, UploadFile, File, HTTPException, Form, Depends, BackgroundTasks
# 从typing模块导入类型提示，增强代码可读性
from typing import List, Optional, Dict, Any
# 导入pandas库，用于数据处理和分析，特别是DataFrame操作
import pandas as pd
# 导入numpy库，用于高性能的科学计算和多维数组操作
import numpy as np
# 导入PyTorch主库，用于构建和训练神经网络
import torch
import torch.nn as nn
import torch.optim as optim
# 从scikit-learn库导入数据预处理和评估指标函数
from sklearn.preprocessing import StandardScaler, RobustScaler
from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error
from sklearn.model_selection import train_test_split
# 导入其他必要的库
import joblib  # 用于高效地保存和加载Python对象
import requests  # 用于调用模型仓库API
import os  # 用于与操作系统进行交互，如文件路径操作、创建目录等
import logging  # 用于记录程序运行时的信息、警告和错误
import gc  # 用于垃圾回收和内存管理
from collections import Counter  # 用于方便地计数可哈希对象
from datetime import datetime  # 用于处理日期和时间
import shutil  # 提供高级文件操作功能，如复制文件
import tempfile  # 用于创建临时文件和目录
import json  # 用于解析和生成JSON格式的数据
import time  # 用于时间相关的操作
import psutil  # 用于监控系统资源
# 从本地模块导入
from .auth import get_current_user
from .task_manager import task_storage, TaskStatus, TaskType

# 尝试导入 pynvml 用于GPU监控
try:
    import pynvml
    pynvml_available = True
except ImportError:
    pynvml_available = False

# 配置日志记录器，设置级别为INFO，并定义日志输出格式
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s"
)

# 创建一个FastAPI的APIRouter实例，用于组织和管理相关的API路由
router = APIRouter()

# ==================== 内存清理函数 ====================

def cleanup_after_data_source(task_id: str, source_id: str):
    """数据源训练完成后的内存清理"""
    try:
        logging.info(f"任务 {task_id}: 开始清理数据源 {source_id} 的内存")

        # 获取清理前的内存信息
        process = psutil.Process(os.getpid())
        memory_before = process.memory_info().rss / 1024 / 1024  # MB

        # 强制垃圾回收
        collected = gc.collect()

        # 获取清理后的内存信息
        memory_after = process.memory_info().rss / 1024 / 1024  # MB
        memory_freed = memory_before - memory_after

        logging.info(f"任务 {task_id}: 数据源 {source_id} 内存清理完成")
        logging.info(f"  清理前内存: {memory_before:.1f}MB")
        logging.info(f"  清理后内存: {memory_after:.1f}MB")
        logging.info(f"  释放内存: {memory_freed:.1f}MB")
        logging.info(f"  回收对象数: {collected}")

    except Exception as e:
        logging.warning(f"任务 {task_id}: 数据源 {source_id} 内存清理时发生错误: {e}")

def cleanup_training_memory(task_id: str, csv_file=None):
    """训练完成后的深度内存清理"""
    try:
        logging.info(f"任务 {task_id}: 开始深度内存清理")

        # 获取清理前的内存信息
        process = psutil.Process(os.getpid())
        memory_before = process.memory_info().rss / 1024 / 1024  # MB

        # 显式删除大对象
        if csv_file is not None:
            try:
                del csv_file
                logging.info(f"任务 {task_id}: 已删除 csv_file 对象")
            except Exception as e:
                logging.warning(f"任务 {task_id}: 删除 csv_file 对象时发生错误: {e}")

        # 清理PyTorch缓存（如果使用GPU）
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
            logging.info(f"任务 {task_id}: 已清理CUDA缓存")

        # 多次强制垃圾回收
        collected_total = 0
        for i in range(3):
            collected = gc.collect()
            collected_total += collected
            if collected == 0:
                break

        # 获取清理后的内存信息
        memory_after = process.memory_info().rss / 1024 / 1024  # MB
        memory_freed = memory_before - memory_after

        logging.info(f"任务 {task_id}: 深度内存清理完成")
        logging.info(f"  清理前内存: {memory_before:.1f}MB")
        logging.info(f"  清理后内存: {memory_after:.1f}MB")
        logging.info(f"  释放内存: {memory_freed:.1f}MB")
        logging.info(f"  回收对象数: {collected_total}")

    except Exception as e:
        logging.warning(f"任务 {task_id}: 深度内存清理时发生错误: {e}")

# ==================== 智能插值函数 ====================

def smart_gap_interpolation(series, max_gap_size=3):
    """
    智能间隙插值：只对小间隙进行线性插值
    """
    series = series.copy()

    # 找到所有NaN的连续区间
    nan_mask = series.isna()
    nan_groups = []
    start = None

    for i, is_nan in enumerate(nan_mask):
        if is_nan and start is None:
            start = i
        elif not is_nan and start is not None:
            nan_groups.append((start, i-1))
            start = None

    # 处理最后一个组
    if start is not None:
        nan_groups.append((start, len(series)-1))

    # 只对小间隙进行插值
    for start_idx, end_idx in nan_groups:
        gap_size = end_idx - start_idx + 1

        if gap_size <= max_gap_size:
            # 小间隙：使用线性插值
            if start_idx > 0 and end_idx < len(series) - 1:
                start_val = series.iloc[start_idx - 1]
                end_val = series.iloc[end_idx + 1]

                # 线性插值
                for i in range(start_idx, end_idx + 1):
                    ratio = (i - start_idx + 1) / (gap_size + 1)
                    series.iloc[i] = start_val + (end_val - start_val) * ratio

    return series

def statistical_interpolation(series, window_size=10):
    """
    基于统计特征的插值：用于长间隙
    """
    series = series.copy()

    for i in range(len(series)):
        if pd.isna(series.iloc[i]):
            # 寻找窗口内的非NaN值
            start_idx = max(0, i - window_size // 2)
            end_idx = min(len(series), i + window_size // 2 + 1)

            window_values = series.iloc[start_idx:end_idx].dropna()

            if len(window_values) > 0:
                # 使用中位数而不是均值，更鲁棒
                series.iloc[i] = window_values.median()

    return series

def boundary_gradient_fill(series):
    """
    边界渐变填充：避免突变
    """
    series = series.copy()

    # 前向填充的改进版本
    first_valid_idx = series.first_valid_index()
    last_valid_idx = series.last_valid_index()

    if first_valid_idx is not None:
        # 获取位置索引
        first_valid_pos = series.index.get_loc(first_valid_idx)
        first_value = series.loc[first_valid_idx]

        # 处理开头的NaN：使用渐变到第一个有效值
        for pos in range(first_valid_pos):
            if pd.isna(series.iloc[pos]):
                # 渐变系数
                gradient_factor = (pos + 1) / (first_valid_pos + 1)
                series.iloc[pos] = first_value * gradient_factor

    if last_valid_idx is not None:
        # 获取位置索引
        last_valid_pos = series.index.get_loc(last_valid_idx)
        last_value = series.loc[last_valid_idx]

        # 处理结尾的NaN：使用渐变从最后一个有效值
        for pos in range(last_valid_pos + 1, len(series)):
            if pd.isna(series.iloc[pos]):
                # 渐变系数
                distance = pos - last_valid_pos
                gradient_factor = max(0.1, 1.0 / (distance + 1))
                series.iloc[pos] = last_value * gradient_factor

    return series

def aggressive_zero_replacement(series):
    """
    积极的零值替换策略
    """
    series = series.copy()

    # 找到所有剩余的零值
    zero_mask = (series == 0)
    zero_count = zero_mask.sum()

    if zero_count == 0:
        return series

    logging.info(f"积极零值替换: 发现 {zero_count} 个零值需要替换")

    # 策略1：使用邻近非零值的最小值作为基准
    non_zero_values = series[series > 0]
    if len(non_zero_values) > 0:
        min_positive = non_zero_values.min()
        median_positive = non_zero_values.median()

        # 为零值生成合理的替换值
        for i in range(len(series)):
            if series.iloc[i] == 0:
                # 查找邻近的非零值
                window_start = max(0, i - 10)
                window_end = min(len(series), i + 11)
                window_values = series.iloc[window_start:window_end]
                nearby_non_zero = window_values[window_values > 0]

                if len(nearby_non_zero) > 0:
                    # 使用邻近非零值的平均值的一个比例
                    replacement_value = nearby_non_zero.mean() * 0.1
                else:
                    # 使用全局最小正值的一个比例
                    replacement_value = min_positive * 0.05

                # 确保替换值不会太小
                replacement_value = max(replacement_value, min_positive * 0.01)
                series.iloc[i] = replacement_value

    else:
        # 如果没有非零值，使用一个小的正数
        replacement_value = 0.001
        series[zero_mask] = replacement_value
        logging.info(f"积极零值替换: 没有非零值参考，使用固定值 {replacement_value}")

    replaced_count = zero_mask.sum()
    logging.info(f"积极零值替换: 成功替换 {replaced_count} 个零值")

    return series

def intelligent_interpolation(resampled_data, target_col='packets_per_sec'):
    """
    智能插值策略：区分真实零值和缺失值
    """
    # 记录原始状态
    total_points = len(resampled_data)
    missing_points = resampled_data[target_col].isna().sum()
    zero_points = (resampled_data[target_col] == 0).sum()

    logging.info(f"智能插值前: 总数据点 {total_points}, 缺失 {missing_points}, 零值 {zero_points}")

    # 步骤1：识别真实零值 vs 缺失值
    # 检查是否有count列来帮助判断
    has_count_col = 'count' in resampled_data.columns
    has_total_col = 'total_packetssam' in resampled_data.columns

    if has_count_col and has_total_col:
        # 情况A：count>0 但 packets_per_sec=NaN，说明是计算问题
        calc_error_mask = (resampled_data['count'] > 0) & resampled_data[target_col].isna()
        if calc_error_mask.any():
            # 重新计算这些点
            for idx in resampled_data[calc_error_mask].index:
                if not pd.isna(resampled_data.loc[idx, 'total_packetssam']):
                    resampled_data.loc[idx, target_col] = resampled_data.loc[idx, 'total_packetssam'] / 4800

        # 情况B：count=0 的零值标记为NaN，让后续插值处理
        # 这是积极插值策略：不保留零值，而是用插值替换
        zero_value_mask = (resampled_data['count'] == 0) & (resampled_data[target_col] == 0)
        resampled_data.loc[zero_value_mask, target_col] = np.nan

        logging.info(f"计算错误修复点数: {calc_error_mask.sum()}")
        logging.info(f"零值转换为待插值点数: {zero_value_mask.sum()}")

    # 步骤2：积极的智能插值策略
    # 2.1 短间隙线性插值（<=5个连续缺失点，增加覆盖范围）
    resampled_data[target_col] = smart_gap_interpolation(
        resampled_data[target_col],
        max_gap_size=5
    )

    # 2.2 长间隙使用邻近非零值的统计特征（增大窗口）
    resampled_data[target_col] = statistical_interpolation(
        resampled_data[target_col],
        window_size=20
    )

    # 2.3 边界处理：使用渐变策略而非直接填充
    resampled_data[target_col] = boundary_gradient_fill(resampled_data[target_col])

    # 2.4 积极的零值替换策略
    resampled_data[target_col] = aggressive_zero_replacement(resampled_data[target_col])

    # 步骤3：最后的保底策略（避免强制填充0）
    remaining_nan = resampled_data[target_col].isna().sum()
    if remaining_nan > 0:
        # 使用全局最小非零值的一个小比例
        non_zero_values = resampled_data[resampled_data[target_col] > 0][target_col]
        if len(non_zero_values) > 0:
            min_positive = non_zero_values.min()
            fallback_value = min_positive * 0.01  # 1%的最小非零值
        else:
            fallback_value = 0.001  # 极小的正数

        resampled_data[target_col].fillna(fallback_value, inplace=True)
        logging.info(f"使用fallback值 {fallback_value} 填充剩余 {remaining_nan} 个NaN")

    # 记录优化后状态
    final_missing = resampled_data[target_col].isna().sum()
    final_zero = (resampled_data[target_col] == 0).sum()
    final_small_positive = ((resampled_data[target_col] > 0) & (resampled_data[target_col] < 0.01)).sum()

    logging.info(f"智能插值后: 剩余缺失 {final_missing}, 零值 {final_zero}, 小正值 {final_small_positive}")

    # 计算零值比例改善
    if zero_points > 0:
        original_zero_ratio = zero_points / total_points
        final_zero_ratio = final_zero / total_points
        improvement = (original_zero_ratio - final_zero_ratio) / original_zero_ratio * 100
        logging.info(f"零值比例改善: {original_zero_ratio:.2%} → {final_zero_ratio:.2%} (改善 {improvement:.1f}%)")

    return resampled_data

def optimized_interpolation_pipeline(resampled, task_id=None):
    """
    优化的插值管道，替换原有的简单插值
    """
    log_prefix = f"任务 {task_id}: " if task_id else ""
    logging.info(f"{log_prefix}开始智能插值处理")

    # 应用智能插值
    resampled = intelligent_interpolation(resampled, 'packets_per_sec')

    logging.info(f"{log_prefix}智能插值处理完成")
    return resampled

# 定义一个能够处理序列数据的双向GRU模型
class GRUModel(nn.Module):
    def __init__(self, input_size, hidden_size, num_layers, output_size=1, dropout=0.2):
        super(GRUModel, self).__init__()
        self.hidden_size = hidden_size
        self.num_layers = num_layers
        # 定义GRU层, 设置为双向
        self.gru = nn.GRU(input_size, hidden_size, num_layers, batch_first=True, dropout=dropout if num_layers > 1 else 0, bidirectional=True)
        
        # 使用一个简单的Dropout和线性层进行预测
        # 因为是双向, 所以GRU的输出维度是 hidden_size * 2
        self.dropout = nn.Dropout(dropout)
        self.fc = nn.Linear(hidden_size * 2, output_size)

    def forward(self, x):
        # 初始化GRU的隐藏状态
        # 因为是双向, 所以第一维是 num_layers * 2
        h0 = torch.zeros(self.num_layers * 2, x.size(0), self.hidden_size).to(x.device)
        
        # GRU前向传播
        out, _ = self.gru(x, h0)
        
        # 我们只取序列中最后一个时间步的输出
        last_step_out = out[:, -1, :]
        last_step_out = self.dropout(last_step_out)
        out = self.fc(last_step_out)
        return out

# 辅助函数，用于将时间序列数据转换为监督学习所需的序列格式
def create_sequences(features, target, sequence_length):
    X, y = [], []
    for i in range(len(features) - sequence_length):
        # 提取长度为sequence_length的特征序列
        X.append(features[i:(i + sequence_length)])
        # 提取该序列对应的下一个目标值
        y.append(target[i + sequence_length])
    return np.array(X), np.array(y)

# 定义一个接收POST请求的/train路由，用于触发模型训练
@router.post("/train")
async def train_model(
    # 文件上传参数
    file: UploadFile = File(None),  # 用于接收上传的文件，默认为None
    csv_dir: Optional[str] = Form(None),  # 本地CSV文件的目录路径，默认为None
    selected_file: Optional[str] = Form(None),  # 所选的本地文件名，默认为None
    # 训练配置参数
    selected_prots_json: str = Form(..., alias="selected_prots"),  # 所选协议的JSON字符串
    selected_datatypes_json: str = Form(..., alias="selected_datatypes"),  # 所选数据类型的JSON字符串
    learning_rate: float = Form(...),  # 学习率
    batch_size: int = Form(...),  # 批量大小
    epochs: int = Form(...),  # 训练轮数
    # 模型结构参数
    sequence_length: int = Form(10),  # 序列长度
    hidden_size: int = Form(50),  # 隐藏层大小
    num_layers: int = Form(2),  # 网络层数
    dropout: float = Form(0.2),  # Dropout比例
    # 输出配置参数
    output_folder: str = Form("/data/output"),  # 输出文件夹路径
    auto_generate_template: bool = Form(False),  # 是否自动生成清洗模板
    # 用户认证参数
    current_user: str = Depends(get_current_user)  # 当前登录的用户
):
    # 检查设备
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    num_gpus = torch.cuda.device_count() if torch.cuda.is_available() else 0
    logging.info(f"用户 {current_user} 使用设备: {device}, 可用GPU数量: {num_gpus}")

    # 解析 JSON 字符串
    try:
        selected_prots: List[str] = json.loads(selected_prots_json)
        selected_datatypes: dict[str, list] = json.loads(selected_datatypes_json)
    except json.JSONDecodeError as e:
        raise HTTPException(status_code=400, detail=f"无效的 JSON 参数: {e}")

    # 验证输入
    if not file and not (csv_dir and selected_file):
        raise HTTPException(status_code=400, detail="请提供CSV文件或选择本地文件！")
    if not selected_prots or not any(selected_datatypes.get(prot, []) for prot in selected_prots):
        raise HTTPException(status_code=400, detail="请至少选择一种协议和数据类型！")

    # CSV文件读取与预处理
    temp_file_path = None
    csv_path = None
    csv_filename = "unknown"

    try:
        if file and file.filename:
            # 处理上传文件
            csv_filename = file.filename.split(".")[0]
            with tempfile.NamedTemporaryFile(delete=False, suffix=".csv") as temp_file:
                temp_file_path = temp_file.name
                # 以1MB的块大小流式读取上传文件，避免一次性加载大文件到内存
                while content := await file.read(1024 * 1024):
                    temp_file.write(content)
            csv_path = temp_file_path
        elif csv_dir and selected_file:
            # 处理本地文件
            if not os.path.exists(csv_dir):
                raise HTTPException(status_code=400, detail="CSV目录路径不存在！")
            csv_path = os.path.join(csv_dir, selected_file)
            if not os.path.exists(csv_path):
                raise HTTPException(status_code=400, detail=f"文件 {selected_file} 不存在！")
            csv_filename = selected_file.split(".")[0]
        else:
            raise HTTPException(
                status_code=400,
                detail="缺少有效的文件来源（上传文件或本地文件路径）。"
            )

        # 分块读取，并只保留选定协议的数据
        logging.info(f"用户 {current_user}：开始从 {csv_path} 分块读取并筛选协议 {selected_prots}...")
        chunk_iterator = pd.read_csv(
            csv_path,
            na_values=["", "NA"],
            keep_default_na=True,
            chunksize=100000,
            low_memory=False
        )

        filtered_chunks = []
        for chunk in chunk_iterator:
            protocol_filtered_chunk = chunk[chunk["protocol"].isin(selected_prots)]
            if not protocol_filtered_chunk.empty:
                filtered_chunks.append(protocol_filtered_chunk)

        if not filtered_chunks:
            raise HTTPException(
                status_code=400,
                detail=f"在文件中找不到任何选定协议的数据: {selected_prots}"
            )

        logging.info(f"用户 {current_user}：开始合并筛选后的数据块...")
        csv_file = pd.concat(filtered_chunks, ignore_index=True)
        del filtered_chunks
        memory_usage = csv_file.memory_usage(deep=True).sum() / 1024**2
        logging.info(f"用户 {current_user}：数据块合并完成。DataFrame形状: {csv_file.shape}, 内存占用: {memory_usage:.2f} MB")

        # 内存与性能优化：转换数据类型
        logging.info(f"用户 {current_user}: 开始优化数据类型以减少内存占用...")
        potential_groupby_cols = ["srcaddress", "dstaddress", "srcport", "dstport"]
        for col in potential_groupby_cols:
            if col in csv_file.columns:
                csv_file[col] = csv_file[col].astype("category")

        if "tcpflags" in csv_file.columns:
            csv_file["tcpflags"] = pd.to_numeric(csv_file["tcpflags"], downcast="unsigned")

        optimized_memory_usage = csv_file.memory_usage(deep=True).sum() / 1024**2
        logging.info(f"用户 {current_user}：数据类型优化完成。优化后内存占用: {optimized_memory_usage:.2f} MB")

    except Exception as e:
        if temp_file_path and os.path.exists(temp_file_path):
            os.remove(temp_file_path)
        raise HTTPException(status_code=500, detail=f"读取或预处理CSV文件失败: {e}")

    # 确保输出目录存在
    os.makedirs(output_folder, exist_ok=True)
    result_filename = f"{csv_filename}_results.txt"
    result_path = os.path.join(output_folder, result_filename)

    # 初始化训练相关变量
    results_dict = {}
    total_combinations = sum(len(selected_datatypes.get(prot, [])) for prot in selected_prots)
    combination_count = 0
    process = psutil.Process(os.getpid())
    cpu_cores = psutil.cpu_count()

    for selected_prot in selected_prots: # 遍历用户选择的每一种协议
        for selected_datatype in selected_datatypes.get(selected_prot, []): # 遍历该协议下用户选择的每一种数据类型
            # --- 资源监控开始 ---
            start_time = time.time() # 记录开始时间
            process.cpu_percent() # 初始化CPU使用率计算
            cpu_samples = []  # 存储CPU使用率样本

            combination_count += 1 # 组合计数器加一
            logging.info(f"用户 {current_user} 正在处理 {combination_count}/{total_combinations}: {selected_prot} - {selected_datatype}") # 记录当前处理的组合和进度

            # 数据过滤
            df_data_pre = csv_file[csv_file['protocol'] == selected_prot] # 从合并后的数据中筛选出当前协议的数据
            if df_data_pre.empty: # 如果该协议的数据为空
                logging.warning(f"用户 {current_user} 协议 {selected_prot} 数据为空，跳过 {selected_prot}/{selected_datatype}") # 记录警告并跳过
                continue # 继续下一个数据类型

            if 'timestamp' not in df_data_pre.columns or 'packetssam' not in df_data_pre.columns: # 检查关键列是否存在
                raise HTTPException(status_code=400, detail=f"{selected_prot} - {selected_datatype} 数据缺少 'timestamp' 或 'packetssam' 列！") # 如果缺少，则抛出错误

            # 根据协议和数据类型过滤（重构优化版）
            try: # 使用try-except块来处理单个组合的训练过程，防止一个失败导致整个任务中断
                filter_config = { # 定义一个配置字典，用于不同协议和数据类型的筛选逻辑
                    "TCP": { # TCP协议的配置
                        "spt_sip_dip": {'groupby_keys': ['srcaddress', 'dstaddress', 'srcport']}, # 按源IP、目的IP、源端口分组
                        "dpt_sip_dip": {'groupby_keys': ['srcaddress', 'dstaddress', 'dstport']}, # 按源IP、目的IP、目的端口分组
                        "len_dpt_syn": {'groupby_keys': ['dstaddress', 'dstport'], 'pre_filter': lambda d: d[d['tcpflags'] == 2]}, # 按目的地址、目的端口分组，并预先筛选出SYN包
                        "seq_ack_dip": {'groupby_keys': ['dstaddress', 'dstport'], 'pre_filter': lambda d: d[d['tcpflags'] == 16]} # 按目的地址、目的端口分组，并预先筛选出ACK包
                    },
                    "UDP": { # UDP协议的配置
                        "spt_sip_dip": {'groupby_keys': ['srcaddress', 'dstaddress', 'srcport']}, # 按源IP、目的IP、源端口分组
                        "dpt_sip_dip": {'groupby_keys': ['srcaddress', 'dstaddress', 'dstport']}  # 按源IP、目的IP、目的端口分组
                    },
                    "ICMP": { # ICMP协议的配置
                        "dip": {'groupby_keys': ['dstaddress']} # 按目的IP分组
                    }
                }
                
                prot_config = filter_config.get(selected_prot, {}) # 获取当前协议的配置
                datatype_config = prot_config.get(selected_datatype) # 获取当前数据类型的配置

                if not datatype_config: # 如果找不到对应的配置
                    raise HTTPException(status_code=400, detail=f"无效的协议和数据类型组合: {selected_prot} - {selected_datatype}") # 抛出HTTP 400错误

                df_data = df_data_pre # 将预筛选的数据赋给df_data
                if 'pre_filter' in datatype_config: # 如果配置中有预筛选函数
                    df_data = datatype_config['pre_filter'](df_data) # 应用预筛选函数
                
                groupby_keys = datatype_config['groupby_keys'] # 获取用于分组的键

                # 修改逻辑：只保留记录数最多的组
                if not df_data.empty:
                    # 计算每个分组的大小
                    group_counts = df_data.groupby(groupby_keys).size()

                    # 找到记录数最多的组
                    max_count = group_counts.max()
                    largest_groups = group_counts[group_counts == max_count].index

                    # 如果有多个组都是最大记录数，选择第一个
                    if len(largest_groups) > 1:
                        largest_group = largest_groups[0]
                        logging.info(f"用户 {current_user}: {selected_prot} - {selected_datatype} 发现 {len(largest_groups)} 个最大组（记录数={max_count}），选择第一个: {largest_group}")
                    else:
                        largest_group = largest_groups[0]
                        logging.info(f"用户 {current_user}: {selected_prot} - {selected_datatype} 选择最大组（记录数={max_count}）: {largest_group}")

                    # 只保留最大组的数据
                    if isinstance(largest_group, tuple):
                        # 多列分组的情况
                        mask = True
                        for i, key in enumerate(groupby_keys):
                            mask = mask & (df_data[key] == largest_group[i])
                        df_data = df_data[mask]
                    else:
                        # 单列分组的情况
                        df_data = df_data[df_data[groupby_keys[0]] == largest_group]
                
                if df_data.empty: # 如果经过上述筛选后数据为空
                    logging.warning(f"用户 {current_user}: {selected_prot} - {selected_datatype} 筛选后数据集为空，跳过。") # 记录警告并跳过
                    continue # 继续下一个组合

                # 时间处理和重采样（优化版）
                df = df_data.copy() # 创建一个数据的副本进行操作，避免SettingWithCopyWarning
                del df_data # 删除原始的df_data以释放内存
                df['timestamp'] = pd.to_datetime(df['timestamp'], unit='s') # 将Unix时间戳（秒）转换为pandas的datetime对象
                
                df = df[df['timestamp'] >= '2025-03-01'] # 筛选出指定日期之后的数据
                if df.empty: # 如果筛选后数据为空
                    logging.warning(f"用户 {current_user}: {selected_prot} - {selected_datatype} 在 2025-03-01 之后无数据，跳过。") # 记录警告并跳过
                    continue # 继续下一个组合

                df = df.sort_values('timestamp') # 按时间戳对数据进行排序
                resampled = df.resample('120s', on='timestamp').agg( # 以120秒为时间窗口进行重采样
                    total_packetssam=('packetssam', 'sum'), # 对每个窗口内的'packetssam'求和
                    count=('packetssam', 'count') # 对每个窗口内的'packetssam'计数
                ) # 不再立即填充0，以便进行插值
                resampled['packets_per_sec'] = resampled['total_packetssam'] / 960 # 正确计算每秒的数据包速率

                # --- 改进：使用智能插值策略，区分真实零值和缺失值 ---
                resampled = optimized_interpolation_pipeline(resampled)

                resampled = resampled.reset_index() # 将时间戳索引重置为列
                resampled = resampled[resampled['packets_per_sec'] <= 5000000] # 过滤掉速率异常高的值

                # --- 核心策略调整：对极度噪声的数据进行平滑处理，让模型学习趋势而非噪声 ---
                smoothing_window = 5  # 5个点 (10分钟) 的滑动窗口
                nf_data = resampled[['timestamp', 'packets_per_sec']].copy()
                nf_data['packets_per_sec_smooth'] = nf_data['packets_per_sec'].rolling(window=smoothing_window, min_periods=1).mean()
                
                if nf_data.empty or len(nf_data) < sequence_length * 2: # 如果数据不足以创建至少两个序列
                    logging.warning(f"用户 {current_user}: {selected_prot} - {selected_datatype} 数据不足以创建序列，跳过。")
                    continue

                # --- 特征工程 ---
                nf_data = nf_data.sort_values('timestamp').reset_index(drop=True)

                # --- 对目标变量（平滑后的数据）进行对数变换和差分 ---
                nf_data['packets_per_sec_log'] = np.log1p(nf_data['packets_per_sec_smooth'])
                nf_data['packets_per_sec_log_diff'] = nf_data['packets_per_sec_log'].diff().fillna(0)
                
                # --- 数据准备 ---
                # 策略调整：移除所有外生时间特征，让模型专注于时间序列自身的动态（纯自回归
                target_col = 'packets_per_sec_log_diff'
                
                # 1. 按时间顺序划分数据集
                split_idx = int(len(nf_data) * 0.8)
                train_df = nf_data.iloc[:split_idx].copy()
                test_df = nf_data.iloc[split_idx:].copy()

                # 2. 只为目标（现在也是唯一的特征）创建并拟合Scaler
                scaler_y = RobustScaler()

                train_df[[target_col]] = scaler_y.fit_transform(train_df[[target_col]])
                test_df[[target_col]] = scaler_y.transform(test_df[[target_col]])

                # 3. 创建序列
                X_train, y_train_scaled_log = create_sequences(train_df[[target_col]].values, train_df[target_col].values, sequence_length)
                X_test, y_test_scaled_log = create_sequences(test_df[[target_col]].values, test_df[target_col].values, sequence_length)

                if X_train.shape[0] == 0 or X_test.shape[0] == 0:
                    logging.warning(f"用户 {current_user}: {selected_prot} - {selected_datatype} 划分后数据不足以创建序列，跳过。")
                    continue
                
                # 将数据转换为PyTorch张量
                X_train_tensor = torch.tensor(X_train, dtype=torch.float32).to(device)
                y_train_tensor = torch.tensor(y_train_scaled_log, dtype=torch.float32).view(-1, 1).to(device)
                X_test_tensor = torch.tensor(X_test, dtype=torch.float32).to(device)
                y_test_tensor = torch.tensor(y_test_scaled_log, dtype=torch.float32).view(-1, 1).to(device)

                # 初始化模型
                model = GRUModel(
                    input_size=1,
                    hidden_size=hidden_size,
                    num_layers=num_layers,
                    dropout=dropout
                ).to(device)

                if num_gpus > 1: # 如果有多个GPU可用
                    model = nn.DataParallel(model) # 使用DataParallel将模型分布到多个GPU上进行训练
                
                # --- 更换损失函数为SmoothL1Loss，它对异常值（流量尖峰）不那么敏感 ---
                criterion = nn.SmoothL1Loss() # 定义损失函数为平滑L1损失
                optimizer = optim.AdamW(model.parameters(), lr=learning_rate, weight_decay=1e-5) # 定义优化器为AdamW，并设置学习率和权重衰减
                
                # --- 更换为StepLR调度器，以固定周期强制降低学习率，帮助模型跳出局部最优 ---
                scheduler = optim.lr_scheduler.StepLR(optimizer, step_size=30, gamma=0.1)

                # 文件名 - 添加时间戳确保唯一性
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                base_filename = f"{csv_filename}_{selected_prot}_{selected_datatype}_{timestamp}" # 构建用于保存文件的基本文件名
                model_filename = f"{base_filename}_model_best.pth" # 最优模型的文件名
                params_filename = f"{base_filename}_params.json" # 模型参数文件名
                scaler_y_filename = f"{base_filename}_scaler_y_best.pkl" # 目标scaler的文件名
                test_filename = f"{base_filename}_test.csv" # 测试集数据的文件名
                predictions_filename = f"{base_filename}_predictions.csv" # 预测结果的文件名
                model_save_path = os.path.join(output_folder, model_filename) # 构建模型的完整保存路径
                params_save_path = os.path.join(output_folder, params_filename) # 构建模型参数的完整保存路径
                scaler_y_save_path = os.path.join(output_folder, scaler_y_filename) # 构建目标scaler的完整保存路径
                test_save_path = os.path.join(output_folder, test_filename) # 构建测试集文件的完整保存路径
                output_csv_path = os.path.join(output_folder, predictions_filename) # 构建预测结果CSV文件的完整保存路径

                # 训练循环
                early_stopping_patience = 50 # 设置一个更合理的早停耐心值
                best_val_loss = float('inf') # 初始化最佳验证损失为正无穷大
                patience_counter = 0 # 初始化耐心计数器为0
                train_losses = [] # 创建一个列表用于存储每个epoch的训练损失
                val_losses = [] # 创建一个列表用于存储每个epoch的验证损失

                for epoch in range(epochs): # 开始训练循环，共进行`epochs`轮
                    # 定期采样CPU使用率（每10个epoch采样一次）
                    if epoch % 10 == 0:
                        current_cpu = process.cpu_percent()
                        if current_cpu > 0:  # 只记录有效的CPU使用率
                            cpu_samples.append(current_cpu)

                    model.train() # 将模型设置为训练模式
                    epoch_loss = 0 # 初始化当前epoch的总损失为0
                    permutation = torch.randperm(X_train_tensor.size(0)) # 生成一个随机的索引排列，用于打乱数据
                    for i in range(0, X_train_tensor.size(0), batch_size): # 按`batch_size`大小遍历训练数据
                        indices = permutation[i:i + batch_size] # 获取当前批次的索引
                        batch_X, batch_y = X_train_tensor[indices], y_train_tensor[indices] # 根据索引获取批次数据
                        optimizer.zero_grad() # 清除之前的梯度
                        outputs = model(batch_X) # 将批次数据输入模型进行前向传播
                        loss = criterion(outputs, batch_y) # 计算模型输出和真实标签之间的损失
                        loss.backward() # 进行反向传播，计算梯度
                        
                        # --- 新增：梯度裁剪，防止梯度爆炸，稳定长序列训练 ---
                        torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
                        
                        optimizer.step() # 使用优化器更新模型的权重
                        epoch_loss += loss.item() # 累加当前批次的损失

                    avg_train_loss = epoch_loss / (len(permutation) // batch_size) if (len(permutation) // batch_size) > 0 else epoch_loss
                    train_losses.append(float(avg_train_loss)) # 将平均训练损失添加到列表中

                    model.eval() # 将模型设置为评估模式
                    with torch.no_grad(): # 在此代码块中不计算梯度，以节省计算资源
                        val_loss = criterion(model(X_test_tensor), y_test_tensor).item() # 计算在整个测试集上的验证损失
                        val_losses.append(float(val_loss)) # 将验证损失添加到列表中
                    
                    # --- 在每个epoch后，根据固定的策略调整学习率 ---
                    scheduler.step()

                    if val_loss < best_val_loss: # 如果当前验证损失低于历史最佳验证损失
                        best_val_loss = val_loss # 更新最佳验证损失
                        patience_counter = 0 # 重置耐心计数器
                        torch.save(model.module.state_dict() if num_gpus > 1 else model.state_dict(), model_save_path) # 保存当前模型的状态字典为最佳模型
                    else: # 如果验证损失没有改善
                        patience_counter += 1 # 耐心计数器加一
                        if patience_counter >= early_stopping_patience: # 如果耐心计数器达到阈值
                            break # 提前终止训练循环

                # 评估模型
                model_to_load = GRUModel(
                    input_size=1,
                    hidden_size=hidden_size,
                    num_layers=num_layers,
                    dropout=dropout
                ).to(device)
                model_to_load.load_state_dict(torch.load(model_save_path, map_location=device, weights_only=True)) # 加载之前保存的最佳模型的权重
                model_to_load.eval() # 将模型设置为评估模式
                with torch.no_grad(): # 不计算梯度
                    y_pred_scaled_log_tensor = model_to_load(X_test_tensor) # 预测值是在缩放和对数变换后的尺度上
                    y_pred_scaled_log = y_pred_scaled_log_tensor.cpu().numpy().flatten()

                # --- 反转缩放和对数变换以在原始尺度上进行评估 ---
                # y_pred_scaled_log 是模型的直接输出，形状为 (n_samples,) 或 (n_samples, 1)
                # scaler_y.inverse_transform 需要 (n_samples, 1) 的形状
                y_pred_scaled_log_reshaped = y_pred_scaled_log.reshape(-1, 1)

                # 1. 使用目标 scaler (scaler_y) 对预测的差分值进行反向缩放
                y_pred_diff_log = scaler_y.inverse_transform(y_pred_scaled_log_reshaped).flatten()

                # 2. "逆差分"操作：将预测的对数差分值加回到上一个时间点的真实对数流量值上
                # 获取用于逆差分的、测试集开始前的最后一个对数流量值序列
                last_log_values = nf_data['packets_per_sec_log'].iloc[split_idx + sequence_length - 1 : -1].values
                
                # 确保长度匹配
                min_len_inv = min(len(y_pred_diff_log), len(last_log_values))
                y_pred_diff_log = y_pred_diff_log[:min_len_inv]
                last_log_values = last_log_values[:min_len_inv]

                y_pred_log = last_log_values + y_pred_diff_log

                # 3. 对预测值进行反向对数变换，得到平滑值的预测
                y_pred_smooth = np.expm1(y_pred_log)
                
                # 4. 从数据中获取真实的平滑值用于比较
                y_test_actual_smooth = nf_data['packets_per_sec_smooth'].iloc[split_idx + sequence_length:].values

                # 确保两个数组的长度一致
                min_len = min(len(y_pred_smooth), len(y_test_actual_smooth))
                y_pred = y_pred_smooth[:min_len]
                y_test_actual = y_test_actual_smooth[:min_len]

                if len(y_pred) == 0:
                    logging.error(f"用户 {current_user}: {selected_prot}/{selected_datatype} 无法生成有效预测，跳过评估。")
                    continue

                # 在平滑数据的尺度上计算所有评估指标
                mse = float(mean_squared_error(y_test_actual, y_pred))
                r2 = float(r2_score(y_test_actual, y_pred))
                mae = float(mean_absolute_error(y_test_actual, y_pred))

                # --- 新增: 将最终的真实值和预测值四舍五入为整数，用于输出 ---
                y_test_actual_rounded = np.round(y_test_actual).astype(int)
                y_pred_rounded = np.round(y_pred).astype(int)

                # --- 新增：计算用于异常检测的动态阈值参数 ---
                prediction_errors = y_test_actual - y_pred
                error_mean = float(np.mean(prediction_errors))
                error_std = float(np.std(prediction_errors))
                
                # 定义一个建议的静态阈值用于展示，基于测试集的平均预测加上3个标准差的误差
                k_sigma = 3.0
                static_anomaly_threshold = float(np.mean(y_pred) + error_mean + k_sigma * error_std)

                # --- 资源监控结束 ---
                duration = time.time() - start_time

                # 计算CPU使用率 - 使用训练过程中采样的平均值
                if cpu_samples:
                    cpu_usage = sum(cpu_samples) / len(cpu_samples)
                    logging.info(f"用户 {current_user}: CPU采样数量: {len(cpu_samples)}, 平均使用率: {cpu_usage:.2f}%")
                else:
                    # 如果没有采样数据，尝试获取当前CPU使用率
                    cpu_usage = process.cpu_percent(interval=1.0)
                    logging.info(f"用户 {current_user}: 使用当前CPU使用率: {cpu_usage:.2f}%")

                # --- 修改: 将CPU使用率转换为单核标准化值 (占总体资源的百分比) ---
                cpu_usage_normalized = cpu_usage / cpu_cores if cpu_cores else cpu_usage
                memory_mb = process.memory_info().rss / (1024 * 1024) # 获取内存占用并转换为MB

                # --- 新增: GPU资源监控 ---
                gpu_memory_mb = 0
                gpu_utilization_percent = 0
                if pynvml_available and device.type == 'cuda':
                    try:
                        pynvml.nvmlInit()
                        num_gpus = torch.cuda.device_count()
                        total_mem_used = 0
                        total_util = 0
                        for i in range(num_gpus):
                            handle = pynvml.nvmlDeviceGetHandleByIndex(i)
                            mem_info = pynvml.nvmlDeviceGetMemoryInfo(handle)
                            util_rates = pynvml.nvmlDeviceGetUtilizationRates(handle)
                            total_mem_used += mem_info.used / (1024 * 1024)
                            total_util += util_rates.gpu
                        gpu_memory_mb = total_mem_used
                        gpu_utilization_percent = total_util / num_gpus if num_gpus > 0 else 0
                        pynvml.nvmlShutdown()
                    except pynvml.NVMLError:
                        pass # 如果GPU监控失败，则静默处理

                # 保存模型、Scaler和包含预测结果的测试集
                torch.save(model_to_load.state_dict(), model_save_path)
                joblib.dump(scaler_y, scaler_y_save_path)
                
                # 保存模型超参数
                model_params = {
                    "input_size": 1,
                    "hidden_size": hidden_size,
                    "num_layers": num_layers,
                    "dropout": dropout,
                    "sequence_length": sequence_length,
                    "anomaly_threshold_mean": error_mean, # 保存误差均值
                    "anomaly_threshold_std": error_std   # 保存误差标准差
                }
                with open(params_save_path, 'w') as f:
                    json.dump(model_params, f)

                # 创建用于保存的结果DataFrame
                result_df = test_df.iloc[sequence_length:].copy()
                result_df['packets_per_sec'] = y_test_actual_rounded # 使用反标准化后的真实值
                result_df['pred'] = y_pred_rounded # 使用反标准化后的预测值
                result_df[['timestamp', 'packets_per_sec', 'pred']].to_csv(output_csv_path, index=False)
                test_df.iloc[sequence_length:].to_csv(test_save_path, index=False)

                # 计算加权平均值
                value_counts = Counter(y_test_actual_rounded)
                total_samples = len(y_test_actual_rounded)
                y_actual_weights = {value: count / total_samples for value, count in value_counts.items()}
                
                try:
                    pred_weights = np.array([y_actual_weights.get(act, 0) for act in y_test_actual_rounded])
                    weight_avg = float(np.sum(y_pred_rounded * pred_weights) / np.sum(pred_weights) if np.sum(pred_weights) > 0 else np.mean(y_pred_rounded))
                except Exception as e:
                    weight_avg = float(np.mean(y_pred_rounded))

                # 更新结果文件
                try: # 尝试更新总的结果文件
                    updated_results = [] # 初始化一个列表用于存放更新后的结果行
                    if os.path.exists(result_path): # 如果结果文件已存在
                        with open(result_path, 'r', encoding='utf-8') as f: # 打开文件
                            updated_results = f.readlines() # 读取所有行到列表中

                    # --- 修改：将要写入文件的结果变更为流量清洗阈值 ---
                    threshold_to_write = np.round(static_anomaly_threshold).astype(int)
                    new_result = f"{selected_prot} {selected_datatype} {threshold_to_write}\n"
                    
                    replaced = False # 初始化一个标志，表示是否已替换旧结果
                    for i, line in enumerate(updated_results): # 遍历已有的结果行
                        parts = line.strip().split() # 分割每一行
                        if len(parts) >= 2 and parts[0] == selected_prot and parts[1] == selected_datatype: # 如果找到了当前组合的旧结果
                            updated_results[i] = new_result # 替换为新结果
                            replaced = True # 将标志设为True
                            break # 退出循环
                    if not replaced: # 如果没有找到旧结果（即第一次运行）
                        updated_results.append(new_result) # 将新结果追加到列表末尾

                    with open(result_path, 'w', encoding='utf-8') as f: # 以写入模式打开结果文件
                        f.writelines(updated_results) # 将更新后的所有行写回文件
                except Exception as e: # 如果写入失败
                    raise HTTPException(status_code=500, detail=f"无法写入结果文件 {result_path}: {e}") # 抛出HTTP 500错误

                finished_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f") # 获取当前完成时间
                result_entry = { # 创建一个字典，用于存放当前组合的详细训练结果
                    "train_shape": list(train_df.shape), # 训练集形状
                    "test_shape": list(test_df.shape), # 测试集形状
                    "train_losses": [float(x) for x in train_losses], # 训练损失历史
                    "val_losses": [float(x) for x in val_losses], # 验证损失历史
                    "r2": r2, # R2分数
                    "y_test_actual": y_test_actual_rounded.tolist(), # 测试集的真实值
                    "y_pred": y_pred_rounded.tolist(), # 测试集的预测值
                    "output_csv_path": output_csv_path, # 预测结果CSV路径
                    "predictions_filename": predictions_filename, # 预测结果文件名
                    "weight_avg": weight_avg, # 加权平均值
                    "r2_score": r2, # 最终的R2分数
                    "finished_time": finished_time, # 完成时间
                    "model_save_path": model_save_path, # 模型保存路径
                    "params_save_path": params_save_path, # 参数保存路径
                    "test_save_path": test_save_path, # 测试集保存路径
                    "scaler_y_save_path": scaler_y_save_path,
                    "static_anomaly_threshold": static_anomaly_threshold, # 将建议阈值添加到结果中
                    "duration_seconds": duration,
                    "cpu_percent": cpu_usage_normalized,
                    "memory_mb": memory_mb,
                    "gpu_memory_mb": gpu_memory_mb,
                    "gpu_utilization_percent": gpu_utilization_percent
                }
                results_dict[f"{selected_prot}_{selected_datatype}"] = result_entry # 将当前组合的结果字典添加到总结果字典中

                # 注册模型到模型仓库
                try:
                    model_info = {
                        "model_name": f"{selected_prot}_{selected_datatype}",
                        "model_type": "GRU",
                        "training_time": finished_time,
                        "data_file": csv_filename,
                        "protocol": selected_prot,
                        "data_type": selected_datatype,
                        "r2_score": r2,
                        "cleaning_threshold": static_anomaly_threshold,
                        "duration_seconds": duration,
                        "cpu_percent": cpu_usage_normalized,
                        "memory_mb": memory_mb,
                        "gpu_memory_mb": gpu_memory_mb,
                        "gpu_utilization_percent": gpu_utilization_percent,
                        "hidden_size": hidden_size,
                        "num_layers": num_layers,
                        "sequence_length": sequence_length,
                        "dropout": dropout,
                        "learning_rate": learning_rate,
                        "batch_size": batch_size,
                        "epochs": epochs,
                        "model_path": model_save_path,
                        "params_path": params_save_path,
                        "scaler_path": scaler_y_save_path,
                        "test_data_path": test_save_path,
                        "train_shape": list(train_df.shape),
                        "test_shape": list(test_df.shape)
                    }

                    # 调用模型仓库注册API
                    from .model_registry import register_model as registry_register_model

                    # 直接调用内部函数
                    registry_result = await registry_register_model(model_info, current_user)

                    if registry_result.get("success"):
                        model_id = registry_result.get("model_id")
                        logging.info(f"用户 {current_user}: 模型 {selected_prot}_{selected_datatype} 已注册到仓库，ID: {model_id}")
                        # 将模型ID添加到结果中
                        result_entry["model_id"] = model_id
                    else:
                        logging.warning(f"用户 {current_user}: 模型 {selected_prot}_{selected_datatype} 注册到仓库失败")

                except Exception as registry_error:
                    logging.warning(f"用户 {current_user}: 模型 {selected_prot}_{selected_datatype} 注册到仓库时发生异常: {registry_error}")

            except Exception as e: # 捕获处理当前组合时发生的任何异常
                logging.error(f"用户 {current_user}: 处理 {selected_prot}/{selected_datatype} 失败: {e}") # 记录错误日志
                # Don't re-raise, just log and continue to the next combination
                continue # 继续处理下一个组合，不中断整个任务

    if temp_file_path and os.path.exists(temp_file_path): # 在所有组合处理完毕后，如果存在临时文件
        os.remove(temp_file_path) # 删除它以进行清理

    # 自动生成清洗模板（如果用户选择了该选项）
    template_info = None
    if auto_generate_template and result_path and os.path.exists(result_path):
        try:
            from .clean_template import generate_clean_template

            # 调用清洗模板生成功能
            template_result = await generate_clean_template(
                results_file=result_path,
                output_folder=output_folder,
                template_name=None,  # 使用默认命名规则
                current_user=current_user
            )

            template_info = {
                "template_generated": True,
                "template_path": template_result.get("template_path"),
                "updated_thresholds": template_result.get("updated_thresholds")
            }
            logging.info(f"用户 {current_user}: 自动生成清洗模板成功 - {template_result.get('template_path')}")

        except Exception as template_error:
            logging.warning(f"用户 {current_user}: 自动生成清洗模板失败: {template_error}")
            template_info = {
                "template_generated": False,
                "error": str(template_error)
            }

    # 构建返回结果
    result = {"results": results_dict, "result_path": result_path}
    if template_info:
        result["template_info"] = template_info

    return result # 返回包含所有成功组合的详细结果和结果文件的路径

# 定义一个GET请求的/list_csv_files路由
@router.get("/list_csv_files")
async def list_csv_files(
    csv_dir: str = "/data/output",
    current_user: str = Depends(get_current_user)
):
    """列出指定目录下的CSV文件"""
    if not os.path.exists(csv_dir):
        raise HTTPException(status_code=400, detail="CSV目录路径不存在！")

    # 列出目录下所有以.csv结尾，且不以_test.csv或_predictions.csv结尾的文件
    csv_files = [
        f for f in os.listdir(csv_dir)
        if f.endswith(".csv")
        and not f.endswith("_test.csv")
        and not f.endswith("_predictions.csv")
    ]
    csv_files.sort()
    return {"files": csv_files}

# 异步训练接口
@router.post("/train_async")
async def train_model_async(
    background_tasks: BackgroundTasks,
    # 文件上传参数
    file: UploadFile = File(None),
    csv_dir: Optional[str] = Form(None),
    selected_file: Optional[str] = Form(None),
    # 训练配置参数
    selected_prots_json: str = Form(..., alias="selected_prots"),
    selected_datatypes_json: str = Form(..., alias="selected_datatypes"),
    learning_rate: float = Form(...),
    batch_size: int = Form(...),
    epochs: int = Form(...),
    # 模型结构参数
    sequence_length: int = Form(10),
    hidden_size: int = Form(50),
    num_layers: int = Form(2),
    dropout: float = Form(0.2),
    # 输出配置参数
    output_folder: str = Form("/data/output"),
    auto_generate_template: bool = Form(False),  # 是否自动生成清洗模板
    # 用户认证参数
    current_user: str = Depends(get_current_user)
):
    """异步启动模型训练任务"""
    try:
        # 解析 JSON 字符串
        selected_prots: List[str] = json.loads(selected_prots_json)
        selected_datatypes: dict[str, list] = json.loads(selected_datatypes_json)
    except json.JSONDecodeError as e:
        raise HTTPException(status_code=400, detail=f"无效的 JSON 参数: {e}")

    # 验证输入
    if not file and not (csv_dir and selected_file):
        raise HTTPException(status_code=400, detail="请提供CSV文件或选择本地文件！")
    if not selected_prots or not any(selected_datatypes.get(prot, []) for prot in selected_prots):
        raise HTTPException(status_code=400, detail="请至少选择一种协议和数据类型！")

    # 准备文件内容
    file_content = None
    csv_filename = "unknown"

    if file and file.filename:
        csv_filename = file.filename.split(".")[0]
        file_content = await file.read()
    else:
        csv_filename = selected_file.split(".")[0] if selected_file else "unknown"

    # 创建任务
    task_params = {
        "csv_filename": csv_filename,
        "selected_prots": selected_prots,
        "selected_datatypes": selected_datatypes,
        "learning_rate": learning_rate,
        "batch_size": batch_size,
        "epochs": epochs,
        "sequence_length": sequence_length,
        "hidden_size": hidden_size,
        "num_layers": num_layers,
        "dropout": dropout,
        "output_folder": output_folder,
        "auto_generate_template": auto_generate_template,
        "user": current_user
    }

    # 使用CSV文件名作为任务ID前缀
    task_id = task_storage.create_task(TaskType.TRAINING, task_params, prefix=csv_filename)

    # 启动后台任务
    background_tasks.add_task(
        train_model_background,
        task_id, file_content, csv_filename, csv_dir, selected_file,
        selected_prots, selected_datatypes, learning_rate, batch_size, epochs,
        sequence_length, hidden_size, num_layers, dropout, output_folder,
        auto_generate_template, current_user
    )

    logging.info(f"用户 {current_user} 启动了异步训练任务 {task_id}")

    return {
        "success": True,
        "task_id": task_id,
        "message": "训练任务已启动，请通过任务ID查询进度"
    }

def train_model_background(
    task_id: str,
    file_content: bytes,
    csv_filename: str,
    csv_dir: str,
    selected_file: str,
    selected_prots: List[str],
    selected_datatypes: dict,
    learning_rate: float,
    batch_size: int,
    epochs: int,
    sequence_length: int,
    hidden_size: int,
    num_layers: int,
    dropout: float,
    output_folder: str,
    auto_generate_template: bool,
    current_user: str
):
    """后台执行模型训练的函数"""
    try:
        # 更新任务状态为运行中
        task_storage.update_task_status(task_id, TaskStatus.RUNNING, current_step="初始化训练环境")

        # 检查设备
        device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        num_gpus = torch.cuda.device_count() if torch.cuda.is_available() else 0
        logging.info(f"任务 {task_id}: 用户 {current_user} 使用设备: {device}, 可用GPU数量: {num_gpus}")

        # 更新进度
        task_storage.update_task_progress(task_id, 5, "准备数据文件")

        # 处理文件
        temp_file_path = None
        csv_path = None

        if file_content:
            # 处理上传的文件
            with tempfile.NamedTemporaryFile(delete=False, suffix=".csv") as temp_file:
                temp_file_path = temp_file.name
                temp_file.write(file_content)
            csv_path = temp_file_path
        else:
            # 处理本地文件
            csv_path = os.path.join(csv_dir, selected_file)
            csv_filename = selected_file.split(".")[0]

        # 更新进度
        task_storage.update_task_progress(task_id, 10, "读取和预处理数据")

        # 读取CSV文件
        df = pd.read_csv(csv_path)
        logging.info(f"任务 {task_id}: 成功读取CSV文件，形状: {df.shape}")

        # 计算总的训练组合数
        total_combinations = sum(len(datatypes) for datatypes in selected_datatypes.values() if datatypes)
        current_combination = 0

        # 存储结果
        results_dict = {}

        # 构建结果文件路径 - 使用与模型文件一致的命名规则
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        result_filename = f"{csv_filename}_{timestamp}_results.txt"
        result_path = os.path.join(output_folder, result_filename)

        # 确保输出目录存在
        os.makedirs(output_folder, exist_ok=True)

        # 遍历每个协议和数据类型组合
        for selected_prot in selected_prots:
            if selected_prot not in selected_datatypes or not selected_datatypes[selected_prot]:
                continue

            for selected_datatype in selected_datatypes[selected_prot]:
                current_combination += 1
                combination_progress = 10 + int((current_combination / total_combinations) * 80)

                task_storage.update_task_progress(
                    task_id,
                    combination_progress,
                    f"训练 {selected_prot}/{selected_datatype} ({current_combination}/{total_combinations})"
                )

                try:
                    # 执行训练逻辑
                    result = _train_single_model(
                        task_id, df, selected_prot, selected_datatype,
                        learning_rate, batch_size, epochs,
                        sequence_length, hidden_size, num_layers, dropout,
                        output_folder, csv_filename, current_user, device
                    )

                    if result:
                        results_dict[f"{selected_prot}_{selected_datatype}"] = result
                        logging.info(f"任务 {task_id}: {selected_prot}/{selected_datatype} 训练完成")

                        # 按照同步训练的逻辑，立即写入结果文件
                        if 'static_anomaly_threshold' in result:
                            try: # 尝试更新总的结果文件
                                updated_results = [] # 初始化一个列表用于存放更新后的结果行
                                if os.path.exists(result_path): # 如果结果文件已存在
                                    with open(result_path, 'r', encoding='utf-8') as f: # 打开文件
                                        updated_results = f.readlines() # 读取所有行到列表中

                                # --- 修改：将要写入文件的结果变更为流量清洗阈值 ---
                                threshold_to_write = np.round(result['static_anomaly_threshold']).astype(int)
                                new_result = f"{selected_prot} {selected_datatype} {threshold_to_write}\n"

                                replaced = False # 初始化一个标志，表示是否已替换旧结果
                                for i, line in enumerate(updated_results): # 遍历已有的结果行
                                    parts = line.strip().split() # 分割每一行
                                    if len(parts) >= 2 and parts[0] == selected_prot and parts[1] == selected_datatype: # 如果找到了当前组合的旧结果
                                        updated_results[i] = new_result # 替换为新结果
                                        replaced = True # 将标志设为True
                                        break # 退出循环
                                if not replaced: # 如果没有找到旧结果（即第一次运行）
                                    updated_results.append(new_result) # 将新结果追加到列表末尾

                                with open(result_path, 'w', encoding='utf-8') as f: # 以写入模式打开结果文件
                                    f.writelines(updated_results) # 将更新后的所有行写回文件

                                logging.info(f"任务 {task_id}: 已更新结果文件 {result_path}: {selected_prot} {selected_datatype} {threshold_to_write}")

                            except Exception as e: # 如果写入失败
                                logging.error(f"任务 {task_id}: 无法写入结果文件 {result_path}: {e}")
                                # 不抛出异常，继续训练其他模型

                except Exception as e:
                    logging.error(f"任务 {task_id}: 处理 {selected_prot}/{selected_datatype} 失败: {e}")
                    continue

        # 注意：结果文件的写入逻辑已移动到单个模型训练完成后立即执行

        # 清理临时文件
        if temp_file_path and os.path.exists(temp_file_path):
            os.remove(temp_file_path)

        # 自动生成清洗模板（如果用户选择了该选项）
        template_info = None
        if auto_generate_template and result_path and os.path.exists(result_path):
            try:
                task_storage.update_task_progress(task_id, 95, "生成清洗模板")

                from .clean_template import generate_clean_template
                import asyncio

                # 在同步函数中调用异步函数
                try:
                    loop = asyncio.get_event_loop()
                    template_result = loop.run_until_complete(generate_clean_template(
                        results_file=result_path,
                        output_folder=output_folder,
                        template_name=None,  # 使用默认命名规则
                        current_user=current_user
                    ))
                except RuntimeError:
                    # 如果没有事件循环，创建一个新的
                    template_result = asyncio.run(generate_clean_template(
                        results_file=result_path,
                        output_folder=output_folder,
                        template_name=None,  # 使用默认命名规则
                        current_user=current_user
                    ))

                template_info = {
                    "template_generated": True,
                    "template_path": template_result.get("template_path"),
                    "updated_thresholds": template_result.get("updated_thresholds")
                }
                logging.info(f"任务 {task_id}: 自动生成清洗模板成功 - {template_result.get('template_path')}")

            except Exception as template_error:
                logging.warning(f"任务 {task_id}: 自动生成清洗模板失败: {template_error}")
                template_info = {
                    "template_generated": False,
                    "error": str(template_error)
                }

        # 更新任务状态为完成
        task_storage.update_task_status(task_id, TaskStatus.COMPLETED)

        # 构建任务结果
        task_result = {
            "results": results_dict,
            "result_path": result_path,
            "total_models": len(results_dict)
        }
        if template_info:
            task_result["template_info"] = template_info

        task_storage.set_task_result(task_id, task_result)

        logging.info(f"任务 {task_id}: 训练完成，共训练了 {len(results_dict)} 个模型")

    except Exception as e:
        logging.error(f"任务 {task_id}: 训练失败: {e}")
        task_storage.update_task_status(task_id, TaskStatus.FAILED, error=str(e))

def _train_single_model(task_id, df, selected_prot, selected_datatype, learning_rate, batch_size, epochs,
                       sequence_length, hidden_size, num_layers, dropout, output_folder,
                       csv_filename, current_user, device):
    """训练单个模型的函数 - 使用与主训练函数完全相同的逻辑"""
    import time
    import psutil
    from datetime import datetime

    try:
        # 开始时间和资源监控
        start_time = time.time()
        training_start_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        process = psutil.Process()
        cpu_cores = psutil.cpu_count()

        # 初始化CPU监控 - 第一次调用建立基线
        process.cpu_percent()
        cpu_samples = []  # 存储CPU使用率样本

        # 检查任务是否被取消
        task_info = task_storage.get_task(task_id)
        if task_info and task_info["status"] == TaskStatus.CANCELLED:
            return None

        # 使用与主训练函数完全相同的数据预处理逻辑
        # 1. 根据协议筛选
        df_data_pre = df[df['protocol'] == selected_prot]
        if df_data_pre.empty:
            logging.warning(f"任务 {task_id}: 协议 {selected_prot} 数据为空")
            return None

        if 'timestamp' not in df_data_pre.columns or 'packetssam' not in df_data_pre.columns:
            logging.error(f"任务 {task_id}: {selected_prot} - {selected_datatype} 数据缺少 'timestamp' 或 'packetssam' 列！")
            return None

        # 2. 应用分组过滤逻辑（与主训练函数完全一致）
        filter_config = {
            "TCP": {
                "spt_sip_dip": {'groupby_keys': ['srcaddress', 'dstaddress', 'srcport']},
                "dpt_sip_dip": {'groupby_keys': ['srcaddress', 'dstaddress', 'dstport']},
                "len_dpt_syn": {'groupby_keys': ['dstaddress', 'dstport'], 'pre_filter': lambda d: d[d['tcpflags'] == 2]},
                "seq_ack_dip": {'groupby_keys': ['dstaddress', 'dstport'], 'pre_filter': lambda d: d[d['tcpflags'] == 16]}
            },
            "UDP": {
                "spt_sip_dip": {'groupby_keys': ['srcaddress', 'dstaddress', 'srcport']},
                "dpt_sip_dip": {'groupby_keys': ['srcaddress', 'dstaddress', 'dstport']}
            },
            "ICMP": {
                "dip": {'groupby_keys': ['dstaddress']}
            }
        }

        prot_config = filter_config.get(selected_prot, {})
        datatype_config = prot_config.get(selected_datatype)

        if not datatype_config:
            logging.warning(f"任务 {task_id}: 无效的协议和数据类型组合: {selected_prot} - {selected_datatype}")
            return None

        df_data = df_data_pre
        if 'pre_filter' in datatype_config:
            df_data = datatype_config['pre_filter'](df_data)

        groupby_keys = datatype_config['groupby_keys']

        # 应用分组过滤：只保留记录数最多的组
        if not df_data.empty:
            # 计算每个分组的大小
            group_counts = df_data.groupby(groupby_keys).size()

            # 找到记录数最多的组
            max_count = group_counts.max()
            largest_groups = group_counts[group_counts == max_count].index

            # 如果有多个组都是最大记录数，选择第一个
            if len(largest_groups) > 1:
                largest_group = largest_groups[0]
                logging.info(f"任务 {task_id}: {selected_prot} - {selected_datatype} 发现 {len(largest_groups)} 个最大组（记录数={max_count}），选择第一个: {largest_group}")
            else:
                largest_group = largest_groups[0]
                logging.info(f"任务 {task_id}: {selected_prot} - {selected_datatype} 选择最大组（记录数={max_count}）: {largest_group}")

            # 只保留最大组的数据
            if isinstance(largest_group, tuple):
                # 多列分组的情况
                mask = True
                for i, key in enumerate(groupby_keys):
                    mask = mask & (df_data[key] == largest_group[i])
                df_data = df_data[mask]
            else:
                # 单列分组的情况
                df_data = df_data[df_data[groupby_keys[0]] == largest_group]

        if df_data.empty:
            logging.warning(f"任务 {task_id}: {selected_prot} - {selected_datatype} 筛选后数据集为空")
            return None

        # 时间处理和重采样（与主训练函数完全一致）
        df = df_data.copy()
        del df_data
        df['timestamp'] = pd.to_datetime(df['timestamp'], unit='s')

        df = df[df['timestamp'] >= '2025-03-01']
        if df.empty:
            logging.warning(f"任务 {task_id}: {selected_prot} - {selected_datatype} 在 2025-03-01 之后无数据")
            return None

        df = df.sort_values('timestamp')
        resampled = df.resample('120s', on='timestamp').agg(
            total_packetssam=('packetssam', 'sum'),
            count=('packetssam', 'count')
        )
        resampled['packets_per_sec'] = resampled['total_packetssam'] / 960

        # 使用智能插值策略，区分真实零值和缺失值
        resampled = optimized_interpolation_pipeline(resampled, task_id)

        resampled = resampled.reset_index()
        resampled = resampled[resampled['packets_per_sec'] <= 5000000]

        # 核心策略调整：对极度噪声的数据进行平滑处理
        smoothing_window = 5
        nf_data = resampled[['timestamp', 'packets_per_sec']].copy()
        nf_data['packets_per_sec_smooth'] = nf_data['packets_per_sec'].rolling(window=smoothing_window, min_periods=1).mean()

        if nf_data.empty or len(nf_data) < sequence_length * 2:
            logging.warning(f"任务 {task_id}: {selected_prot} - {selected_datatype} 数据不足以创建序列")
            return None

        # 特征工程
        nf_data = nf_data.sort_values('timestamp').reset_index(drop=True)

        # 对目标变量（平滑后的数据）进行对数变换和差分
        nf_data['packets_per_sec_log'] = np.log1p(nf_data['packets_per_sec_smooth'])
        nf_data['packets_per_sec_log_diff'] = nf_data['packets_per_sec_log'].diff().fillna(0)

        # 数据准备
        target_col = 'packets_per_sec_log_diff'

        # 1. 按时间顺序划分数据集
        split_idx = int(len(nf_data) * 0.8)
        train_df = nf_data.iloc[:split_idx].copy()
        test_df = nf_data.iloc[split_idx:].copy()

        # 2. 只为目标（现在也是唯一的特征）创建并拟合Scaler
        from sklearn.preprocessing import RobustScaler
        scaler_y = RobustScaler()

        train_df[[target_col]] = scaler_y.fit_transform(train_df[[target_col]])
        test_df[[target_col]] = scaler_y.transform(test_df[[target_col]])

        # 3. 创建序列
        X_train, y_train_scaled_log = create_sequences(train_df[[target_col]].values, train_df[target_col].values, sequence_length)
        X_test, y_test_scaled_log = create_sequences(test_df[[target_col]].values, test_df[target_col].values, sequence_length)

        if X_train.shape[0] == 0 or X_test.shape[0] == 0:
            logging.warning(f"任务 {task_id}: {selected_prot} - {selected_datatype} 划分后数据不足以创建序列")
            return None

        # 将数据转换为PyTorch张量
        X_train_tensor = torch.tensor(X_train, dtype=torch.float32).to(device)
        y_train_tensor = torch.tensor(y_train_scaled_log, dtype=torch.float32).view(-1, 1).to(device)
        X_test_tensor = torch.tensor(X_test, dtype=torch.float32).to(device)
        y_test_tensor = torch.tensor(y_test_scaled_log, dtype=torch.float32).view(-1, 1).to(device)

        # 初始化模型
        model = GRUModel(
            input_size=1,
            hidden_size=hidden_size,
            num_layers=num_layers,
            dropout=dropout
        ).to(device)

        num_gpus = torch.cuda.device_count() if torch.cuda.is_available() else 0
        if num_gpus > 1:
            model = nn.DataParallel(model)

        # 更换损失函数为SmoothL1Loss
        criterion = nn.SmoothL1Loss()
        optimizer = optim.AdamW(model.parameters(), lr=learning_rate, weight_decay=1e-5)

        # 更换为StepLR调度器
        scheduler = optim.lr_scheduler.StepLR(optimizer, step_size=30, gamma=0.1)

        # 文件名 - 添加时间戳确保唯一性
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        base_filename = f"{csv_filename}_{selected_prot}_{selected_datatype}_{timestamp}"
        model_filename = f"{base_filename}_model_best.pth"
        params_filename = f"{base_filename}_params.json"
        scaler_y_filename = f"{base_filename}_scaler_y_best.pkl"
        test_filename = f"{base_filename}_test.csv"
        predictions_filename = f"{base_filename}_predictions.csv"
        model_save_path = os.path.join(output_folder, model_filename)
        params_save_path = os.path.join(output_folder, params_filename)
        scaler_y_save_path = os.path.join(output_folder, scaler_y_filename)
        test_save_path = os.path.join(output_folder, test_filename)
        output_csv_path = os.path.join(output_folder, predictions_filename)

        # 训练循环
        early_stopping_patience = 50
        best_val_loss = float('inf')
        patience_counter = 0
        train_losses = []
        val_losses = []

        for epoch in range(epochs):
            # 检查任务是否被取消
            task_info = task_storage.get_task(task_id)
            if task_info and task_info["status"] == TaskStatus.CANCELLED:
                return None

            # 定期采样CPU使用率（每10个epoch采样一次）
            if epoch % 10 == 0:
                current_cpu = process.cpu_percent()
                if current_cpu > 0:  # 只记录有效的CPU使用率
                    cpu_samples.append(current_cpu)

            model.train()
            epoch_loss = 0
            permutation = torch.randperm(X_train_tensor.size(0))
            for i in range(0, X_train_tensor.size(0), batch_size):
                indices = permutation[i:i + batch_size]
                batch_X, batch_y = X_train_tensor[indices], y_train_tensor[indices]
                optimizer.zero_grad()
                outputs = model(batch_X)
                loss = criterion(outputs, batch_y)
                loss.backward()

                # 梯度裁剪
                torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)

                optimizer.step()
                epoch_loss += loss.item()

            avg_train_loss = epoch_loss / (len(permutation) // batch_size) if (len(permutation) // batch_size) > 0 else epoch_loss
            train_losses.append(float(avg_train_loss))

            model.eval()
            with torch.no_grad():
                val_loss = criterion(model(X_test_tensor), y_test_tensor).item()
                val_losses.append(float(val_loss))

            scheduler.step()

            if val_loss < best_val_loss:
                best_val_loss = val_loss
                patience_counter = 0
                torch.save(model.module.state_dict() if num_gpus > 1 else model.state_dict(), model_save_path)
            else:
                patience_counter += 1
                if patience_counter >= early_stopping_patience:
                    break

            # 每10个epoch记录一次
            if (epoch + 1) % 10 == 0:
                logging.info(f"任务 {task_id}: {selected_prot}/{selected_datatype} Epoch {epoch+1}/{epochs}, Train Loss: {avg_train_loss:.6f}, Val Loss: {val_loss:.6f}")

        # 评估模型
        model_to_load = GRUModel(
            input_size=1,
            hidden_size=hidden_size,
            num_layers=num_layers,
            dropout=dropout
        ).to(device)
        model_to_load.load_state_dict(torch.load(model_save_path, map_location=device, weights_only=True))
        model_to_load.eval()
        with torch.no_grad():
            y_pred_scaled_log_tensor = model_to_load(X_test_tensor)
            y_pred_scaled_log = y_pred_scaled_log_tensor.cpu().numpy().flatten()

        # 反转缩放和对数变换以在原始尺度上进行评估
        y_pred_scaled_log_reshaped = y_pred_scaled_log.reshape(-1, 1)

        # 1. 使用目标 scaler (scaler_y) 对预测的差分值进行反向缩放
        y_pred_diff_log = scaler_y.inverse_transform(y_pred_scaled_log_reshaped).flatten()

        # 2. "逆差分"操作：将预测的对数差分值加回到上一个时间点的真实对数流量值上
        last_log_values = nf_data['packets_per_sec_log'].iloc[split_idx + sequence_length - 1 : -1].values

        # 确保长度匹配
        min_len_inv = min(len(y_pred_diff_log), len(last_log_values))
        y_pred_diff_log = y_pred_diff_log[:min_len_inv]
        last_log_values = last_log_values[:min_len_inv]

        y_pred_log = last_log_values + y_pred_diff_log

        # 3. 对预测值进行反向对数变换，得到平滑值的预测
        y_pred_smooth = np.expm1(y_pred_log)

        # 4. 从数据中获取真实的平滑值用于比较
        y_test_actual_smooth = nf_data['packets_per_sec_smooth'].iloc[split_idx + sequence_length:].values

        # 确保两个数组的长度一致
        min_len = min(len(y_pred_smooth), len(y_test_actual_smooth))
        y_pred = y_pred_smooth[:min_len]
        y_test_actual = y_test_actual_smooth[:min_len]

        if len(y_pred) == 0:
            logging.error(f"任务 {task_id}: {selected_prot}/{selected_datatype} 无法生成有效预测")
            return None

        # 在平滑数据的尺度上计算所有评估指标
        mse = float(mean_squared_error(y_test_actual, y_pred))
        r2 = float(r2_score(y_test_actual, y_pred))
        mae = float(mean_absolute_error(y_test_actual, y_pred))

        # 将最终的真实值和预测值四舍五入为整数
        y_test_actual_rounded = np.round(y_test_actual).astype(int)
        y_pred_rounded = np.round(y_pred).astype(int)

        # 计算用于异常检测的动态阈值参数
        prediction_errors = y_test_actual - y_pred
        error_mean = float(np.mean(prediction_errors))
        error_std = float(np.std(prediction_errors))

        # 定义一个建议的静态阈值
        k_sigma = 3.0
        static_anomaly_threshold = float(np.mean(y_pred) + error_mean + k_sigma * error_std)

        # 保存模型、Scaler和包含预测结果的测试集
        torch.save(model_to_load.state_dict(), model_save_path)
        joblib.dump(scaler_y, scaler_y_save_path)

        # 保存模型超参数
        model_params = {
            "input_size": 1,
            "hidden_size": hidden_size,
            "num_layers": num_layers,
            "dropout": dropout,
            "sequence_length": sequence_length,
            "anomaly_threshold_mean": error_mean,
            "anomaly_threshold_std": error_std
        }
        with open(params_save_path, 'w') as f:
            json.dump(model_params, f)

        # 创建用于保存的结果DataFrame
        result_df = test_df.iloc[sequence_length:].copy()
        result_df['packets_per_sec'] = y_test_actual_rounded
        result_df['pred'] = y_pred_rounded
        result_df[['timestamp', 'packets_per_sec', 'pred']].to_csv(output_csv_path, index=False)
        test_df.iloc[sequence_length:].to_csv(test_save_path, index=False)

        # 计算加权平均值
        value_counts = Counter(y_test_actual_rounded)
        total_samples = len(y_test_actual_rounded)
        y_actual_weights = {value: count / total_samples for value, count in value_counts.items()}

        try:
            pred_weights = np.array([y_actual_weights.get(act, 0) for act in y_test_actual_rounded])
            weight_avg = float(np.sum(y_pred_rounded * pred_weights) / np.sum(pred_weights) if np.sum(pred_weights) > 0 else np.mean(y_pred_rounded))
        except Exception as e:
            weight_avg = float(np.mean(y_pred_rounded))

        finished_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")

        # 返回结果
        result = {
            "train_shape": list(train_df.shape),
            "test_shape": list(test_df.shape),
            "train_losses": [float(x) for x in train_losses],
            "val_losses": [float(x) for x in val_losses],
            "r2": r2,
            "y_test_actual": y_test_actual_rounded.tolist(),
            "y_pred": y_pred_rounded.tolist(),
            "output_csv_path": output_csv_path,
            "predictions_filename": predictions_filename,
            "weight_avg": weight_avg,
            "r2_score": r2,
            "finished_time": finished_time,
            "model_save_path": model_save_path,
            "params_save_path": params_save_path,
            "test_save_path": test_save_path,
            "scaler_y_save_path": scaler_y_save_path,
            "static_anomaly_threshold": static_anomaly_threshold
        }

        # 添加资源监控信息
        duration = time.time() - start_time

        # 计算CPU使用率 - 使用训练过程中采样的平均值
        if cpu_samples:
            cpu_usage = sum(cpu_samples) / len(cpu_samples)
            logging.info(f"任务 {task_id}: CPU采样数量: {len(cpu_samples)}, 平均使用率: {cpu_usage:.2f}%")
        else:
            # 如果没有采样数据，尝试获取当前CPU使用率
            cpu_usage = process.cpu_percent(interval=1.0)  # 使用1秒间隔获取更准确的值
            logging.info(f"任务 {task_id}: 使用当前CPU使用率: {cpu_usage:.2f}%")

        cpu_usage_normalized = cpu_usage / cpu_cores if cpu_cores else cpu_usage
        memory_mb = process.memory_info().rss / (1024 * 1024)

        # GPU资源监控
        gpu_memory_mb = 0
        gpu_utilization_percent = 0
        try:
            import pynvml
            if device.type == 'cuda':
                pynvml.nvmlInit()
                handle = pynvml.nvmlDeviceGetHandleByIndex(0)
                mem_info = pynvml.nvmlDeviceGetMemoryInfo(handle)
                util_rates = pynvml.nvmlDeviceGetUtilizationRates(handle)
                gpu_memory_mb = mem_info.used / (1024 * 1024)
                gpu_utilization_percent = util_rates.gpu
                pynvml.nvmlShutdown()
        except:
            pass  # 如果GPU监控失败，则静默处理

        # 添加资源监控字段到结果
        result.update({
            "duration_seconds": duration,
            "cpu_percent": cpu_usage_normalized,
            "memory_mb": memory_mb,
            "gpu_memory_mb": gpu_memory_mb,
            "gpu_utilization_percent": gpu_utilization_percent
        })

        # 注册模型到仓库
        try:
            model_info = {
                "model_name": f"{selected_prot}_{selected_datatype}",
                "model_type": "GRU",
                "training_time": training_start_time,
                "protocol": selected_prot,
                "data_type": selected_datatype,
                "r2_score": r2,
                "cleaning_threshold": static_anomaly_threshold,
                "hidden_size": hidden_size,
                "num_layers": num_layers,
                "sequence_length": sequence_length,
                "dropout": dropout,
                "learning_rate": learning_rate,
                "batch_size": batch_size,
                "epochs": epochs,
                "model_path": model_save_path,
                "params_path": params_save_path,
                "scaler_path": scaler_y_save_path,
                "test_data_path": test_save_path,
                "train_shape": list(train_df.shape),
                "test_shape": list(test_df.shape),
                "data_file": csv_filename,
                "duration_seconds": duration,
                "cpu_percent": cpu_usage_normalized,
                "memory_mb": memory_mb,
                "gpu_memory_mb": gpu_memory_mb,
                "gpu_utilization_percent": gpu_utilization_percent
            }

            # 调用模型仓库注册API
            from .model_registry import register_model as registry_register_model
            import asyncio

            # 在同步函数中调用异步函数
            try:
                loop = asyncio.get_event_loop()
                registry_result = loop.run_until_complete(registry_register_model(model_info, current_user))
            except RuntimeError:
                # 如果没有事件循环，创建一个新的
                registry_result = asyncio.run(registry_register_model(model_info, current_user))

            if registry_result.get("success"):
                model_id = registry_result.get("model_id")
                logging.info(f"任务 {task_id}: 模型 {selected_prot}_{selected_datatype} 已注册到仓库，ID: {model_id}")
                # 将模型ID添加到结果中
                result["model_id"] = model_id
            else:
                logging.warning(f"任务 {task_id}: 模型 {selected_prot}_{selected_datatype} 注册到仓库失败: {registry_result.get('message', '未知错误')}")

        except Exception as e:
            logging.error(f"任务 {task_id}: 注册模型到仓库时发生错误: {e}")

        return result

    except Exception as e:
        logging.error(f"任务 {task_id}: 训练 {selected_prot}/{selected_datatype} 时发生错误: {e}")
        return None

# ==================== 多CSV文件训练接口 ====================

@router.post("/train_multi") # 定义一个接收POST请求的/train_multi路由，用于触发多CSV文件模型训练
async def train_model_multi( # 定义一个异步的多文件训练函数
    files: List[UploadFile] = File(None), # 定义一个参数`files`，用于接收上传的多个文件
    data_sources_json: str = Form(..., alias="data_sources"), # 定义数据源配置的JSON字符串
    selected_prots_json: str = Form(..., alias="selected_prots"), # 定义一个必需的参数`selected_prots`，接收所选协议的JSON字符串
    selected_datatypes_json: str = Form(..., alias="selected_datatypes"), # 定义一个必需的参数`selected_datatypes`，接收所选数据类型的JSON字符串
    learning_rate: float = Form(...), # 定义一个必需的参数`learning_rate`，接收浮点型的学习率
    batch_size: int = Form(...), # 定义一个必需的参数`batch_size`，接收整型的批量大小
    epochs: int = Form(...), # 定义一个必需的参数`epochs`，接收整型的训练轮数
    sequence_length: int = Form(10),
    hidden_size: int = Form(50),
    num_layers: int = Form(2),
    dropout: float = Form(0.2),
    auto_generate_template: bool = Form(False), # 新增参数：是否自动生成清洗模板
    current_user: str = Depends(get_current_user) # 使用Depends进行依赖注入，获取当前登录的用户
):
    """多CSV文件同步训练接口"""
    # 检查设备
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    num_gpus = torch.cuda.device_count() if torch.cuda.is_available() else 0
    logging.info(f"用户 {current_user} 开始多文件训练，使用设备: {device}, 可用GPU数量: {num_gpus}")

    # 解析 JSON 字符串
    try:
        data_sources: List[Dict[str, Any]] = json.loads(data_sources_json)
        selected_prots: List[str] = json.loads(selected_prots_json)
        selected_datatypes: Dict[str, List[str]] = json.loads(selected_datatypes_json)
    except json.JSONDecodeError as e:
        raise HTTPException(status_code=400, detail=f"无效的 JSON 参数: {e}")

    # 验证输入
    if not data_sources:
        raise HTTPException(status_code=400, detail="请至少提供一个数据源！")
    if not selected_prots or not any(selected_datatypes.get(prot, []) for prot in selected_prots):
        raise HTTPException(status_code=400, detail="请至少选择一种协议和数据类型！")

    # 验证数据源配置
    for i, source in enumerate(data_sources):
        if source.get('type') == 'upload':
            if not files or i >= len(files):
                raise HTTPException(status_code=400, detail=f"数据源 {i+1} 缺少上传文件！")
        elif source.get('type') == 'local':
            if not source.get('csvDir') or not source.get('selectedFile'):
                raise HTTPException(status_code=400, detail=f"数据源 {i+1} 缺少本地文件路径！")
        if not source.get('outputFolder'):
            raise HTTPException(status_code=400, detail=f"数据源 {i+1} 缺少输出路径！")

    # 检查输出路径是否重复
    output_folders = [source['outputFolder'] for source in data_sources]
    if len(output_folders) != len(set(output_folders)):
        raise HTTPException(status_code=400, detail="输出路径不能重复！")

    results_dict = {}
    total_sources = len(data_sources)

    for i, source in enumerate(data_sources):
        try:
            logging.info(f"用户 {current_user} 正在处理数据源 {i+1}/{total_sources}: {source.get('id', f'source_{i+1}')}")

            # 准备单个数据源的训练参数
            source_result = await train_single_data_source(
                source=source,
                file=files[i] if source.get('type') == 'upload' and files and i < len(files) else None,
                selected_prots=selected_prots,
                selected_datatypes=selected_datatypes,
                learning_rate=learning_rate,
                batch_size=batch_size,
                epochs=epochs,
                sequence_length=sequence_length,
                hidden_size=hidden_size,
                num_layers=num_layers,
                dropout=dropout,
                auto_generate_template=auto_generate_template,
                current_user=current_user
            )

            results_dict[source.get('id', f'source_{i+1}')] = source_result

        except Exception as e:
            logging.error(f"用户 {current_user}: 数据源 {source.get('id', f'source_{i+1}')} 训练失败: {e}")
            results_dict[source.get('id', f'source_{i+1}')] = {"error": str(e)}
            continue

    return {"results": results_dict, "message": f"多文件训练完成，共处理 {total_sources} 个数据源"}

async def train_single_data_source(
    source: Dict[str, Any],
    file: Optional[UploadFile],
    selected_prots: List[str],
    selected_datatypes: Dict[str, List[str]],
    learning_rate: float,
    batch_size: int,
    epochs: int,
    sequence_length: int,
    hidden_size: int,
    num_layers: int,
    dropout: float,
    auto_generate_template: bool,
    current_user: str
) -> Dict[str, Any]:
    """训练单个数据源的辅助函数"""

    # 构建训练参数，复用现有的train_model逻辑
    if source.get('type') == 'upload' and file:
        # 上传文件模式
        return await train_model(
            file=file,
            csv_dir=None,
            selected_file=None,
            selected_prots_json=json.dumps(selected_prots),
            selected_datatypes_json=json.dumps(selected_datatypes),
            learning_rate=learning_rate,
            batch_size=batch_size,
            epochs=epochs,
            sequence_length=sequence_length,
            hidden_size=hidden_size,
            num_layers=num_layers,
            dropout=dropout,
            output_folder=source['outputFolder'],
            auto_generate_template=auto_generate_template,
            current_user=current_user
        )
    elif source.get('type') == 'local':
        # 本地文件模式
        return await train_model(
            file=None,
            csv_dir=source['csvDir'],
            selected_file=source['selectedFile'],
            selected_prots_json=json.dumps(selected_prots),
            selected_datatypes_json=json.dumps(selected_datatypes),
            learning_rate=learning_rate,
            batch_size=batch_size,
            epochs=epochs,
            sequence_length=sequence_length,
            hidden_size=hidden_size,
            num_layers=num_layers,
            dropout=dropout,
            output_folder=source['outputFolder'],
            auto_generate_template=auto_generate_template,
            current_user=current_user
        )
    else:
        raise ValueError(f"无效的数据源配置: {source}")

@router.post("/train_multi_async")
async def train_model_multi_async(
    background_tasks: BackgroundTasks,
    files: List[UploadFile] = File(None),
    data_sources_json: str = Form(..., alias="data_sources"),
    selected_prots_json: str = Form(..., alias="selected_prots"),
    selected_datatypes_json: str = Form(..., alias="selected_datatypes"),
    learning_rate: float = Form(...),
    batch_size: int = Form(...),
    epochs: int = Form(...),
    sequence_length: int = Form(10),
    hidden_size: int = Form(50),
    num_layers: int = Form(2),
    dropout: float = Form(0.2),
    auto_generate_template: bool = Form(False),
    current_user: str = Depends(get_current_user)
):
    """多CSV文件异步训练接口"""
    try:
        # 解析 JSON 字符串
        data_sources: List[Dict[str, Any]] = json.loads(data_sources_json)
        selected_prots: List[str] = json.loads(selected_prots_json)
        selected_datatypes: Dict[str, List[str]] = json.loads(selected_datatypes_json)
    except json.JSONDecodeError as e:
        raise HTTPException(status_code=400, detail=f"无效的 JSON 参数: {e}")

    # 验证输入
    if not data_sources:
        raise HTTPException(status_code=400, detail="请至少提供一个数据源！")
    if not selected_prots or not any(selected_datatypes.get(prot, []) for prot in selected_prots):
        raise HTTPException(status_code=400, detail="请至少选择一种协议和数据类型！")

    # 验证数据源配置
    for i, source in enumerate(data_sources):
        if source.get('type') == 'upload':
            if not files or i >= len(files):
                raise HTTPException(status_code=400, detail=f"数据源 {i+1} 缺少上传文件！")
        elif source.get('type') == 'local':
            if not source.get('csvDir') or not source.get('selectedFile'):
                raise HTTPException(status_code=400, detail=f"数据源 {i+1} 缺少本地文件路径！")
        if not source.get('outputFolder'):
            raise HTTPException(status_code=400, detail=f"数据源 {i+1} 缺少输出路径！")

    # 检查输出路径是否重复
    output_folders = [source['outputFolder'] for source in data_sources]
    if len(output_folders) != len(set(output_folders)):
        raise HTTPException(status_code=400, detail="输出路径不能重复！")

    # 准备文件内容
    files_content = []
    if files:
        for file in files:
            if file and file.filename:
                content = await file.read()
                files_content.append(content)
            else:
                files_content.append(None)

    # 创建任务参数
    task_params = {
        "data_sources": data_sources,
        "files_content": files_content,
        "selected_prots": selected_prots,
        "selected_datatypes": selected_datatypes,
        "learning_rate": learning_rate,
        "batch_size": batch_size,
        "epochs": epochs,
        "sequence_length": sequence_length,
        "hidden_size": hidden_size,
        "num_layers": num_layers,
        "dropout": dropout,
        "auto_generate_template": auto_generate_template,
        "user": current_user
    }

    # 使用第一个数据源的ID作为任务ID前缀
    first_source_id = data_sources[0].get('id', 'multi_training')
    task_id = task_storage.create_task(TaskType.TRAINING, task_params, prefix=f"multi_{first_source_id}")

    # 启动后台任务
    background_tasks.add_task(
        train_model_multi_background,
        task_id, data_sources, files_content, selected_prots, selected_datatypes,
        learning_rate, batch_size, epochs, sequence_length, hidden_size, num_layers,
        dropout, auto_generate_template, current_user
    )

    logging.info(f"用户 {current_user} 启动了多文件异步训练任务 {task_id}")

    return {
        "success": True,
        "task_id": task_id,
        "message": f"多文件训练任务已启动，共 {len(data_sources)} 个数据源，请通过任务ID查询进度"
    }

# 多文件训练后台执行函数
def train_model_multi_background(
    task_id: str,
    data_sources: List[Dict[str, Any]],
    files_content: List[Optional[bytes]],
    selected_prots: List[str],
    selected_datatypes: Dict[str, List[str]],
    learning_rate: float,
    batch_size: int,
    epochs: int,
    sequence_length: int,
    hidden_size: int,
    num_layers: int,
    dropout: float,
    auto_generate_template: bool,
    current_user: str
):
    """后台执行多文件模型训练的函数"""
    try:
        # 更新任务状态为运行中
        task_storage.update_task_status(task_id, TaskStatus.RUNNING, current_step="初始化多文件训练环境")

        # 检查设备
        device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        num_gpus = torch.cuda.device_count() if torch.cuda.is_available() else 0
        logging.info(f"任务 {task_id}: 用户 {current_user} 使用设备: {device}, 可用GPU数量: {num_gpus}")

        total_sources = len(data_sources)
        results_dict = {}

        for i, source in enumerate(data_sources):
            try:
                # 更新总体进度
                overall_progress = (i / total_sources) * 90  # 留10%给最终处理
                source_id = source.get('id', f'source_{i+1}')
                task_storage.update_task_progress(
                    task_id,
                    overall_progress,
                    f"正在训练数据源 {i+1}/{total_sources}: {source_id}"
                )

                logging.info(f"任务 {task_id}: 开始处理数据源 {i+1}/{total_sources}: {source_id}")

                # 为当前数据源执行训练
                source_result = train_single_data_source_background(
                    source=source,
                    file_content=files_content[i] if i < len(files_content) else None,
                    selected_prots=selected_prots,
                    selected_datatypes=selected_datatypes,
                    learning_rate=learning_rate,
                    batch_size=batch_size,
                    epochs=epochs,
                    sequence_length=sequence_length,
                    hidden_size=hidden_size,
                    num_layers=num_layers,
                    dropout=dropout,
                    auto_generate_template=auto_generate_template,
                    current_user=current_user,
                    parent_task_id=task_id
                )

                # 保存结果
                results_dict[source_id] = source_result
                logging.info(f"任务 {task_id}: 数据源 {source_id} 训练完成")

                # 数据源完成后的内存清理
                cleanup_after_data_source(task_id, source_id)

            except Exception as e:
                logging.error(f"任务 {task_id}: 数据源 {source.get('id', f'source_{i+1}')} 训练失败: {e}")
                results_dict[source.get('id', f'source_{i+1}')] = {"error": str(e)}
                continue

        # 更新最终进度
        task_storage.update_task_progress(task_id, 95, "正在整理训练结果")

        # 生成汇总信息
        successful_sources = sum(1 for result in results_dict.values() if "error" not in result)
        failed_sources = total_sources - successful_sources

        summary = {
            "total_sources": total_sources,
            "successful_sources": successful_sources,
            "failed_sources": failed_sources,
            "results": results_dict
        }

        # 更新任务完成状态
        task_storage.update_task_status(
            task_id,
            TaskStatus.COMPLETED,
            result=summary,
            current_step=f"多文件训练完成: 成功 {successful_sources}/{total_sources}"
        )

        logging.info(f"任务 {task_id}: 多文件训练任务完成，成功 {successful_sources}/{total_sources} 个数据源")

    except Exception as e:
        logging.error(f"任务 {task_id}: 多文件训练任务失败: {e}")
        task_storage.update_task_status(
            task_id,
            TaskStatus.FAILED,
            error_message=str(e),
            current_step="多文件训练失败"
        )

def train_single_data_source_background(
    source: Dict[str, Any],
    file_content: Optional[bytes],
    selected_prots: List[str],
    selected_datatypes: Dict[str, List[str]],
    learning_rate: float,
    batch_size: int,
    epochs: int,
    sequence_length: int,
    hidden_size: int,
    num_layers: int,
    dropout: float,
    auto_generate_template: bool,
    current_user: str,
    parent_task_id: str
) -> Dict[str, Any]:
    """后台训练单个数据源的辅助函数"""

    try:
        # 构建完整的训练参数，模拟train_model_background的完整逻辑
        # 但是返回与train_model相同的格式（包含results字典）

        # 创建子任务ID
        sub_task_id = f"{parent_task_id}_{source.get('id', 'source')}"

        # 调用完整的训练逻辑
        if source.get('type') == 'upload' and file_content:
            # 上传文件模式 - 创建临时文件
            import tempfile
            csv_filename = source.get('filename', 'uploaded_file')

            with tempfile.NamedTemporaryFile(delete=False, suffix=".csv", mode='wb') as temp_file:
                temp_file.write(file_content)
                temp_file.flush()  # 确保数据写入磁盘
                temp_file_path = temp_file.name

            # 验证临时文件是否正确创建
            if not os.path.exists(temp_file_path):
                raise ValueError(f"临时文件创建失败: {temp_file_path}")

            file_size = os.path.getsize(temp_file_path)
            logging.info(f"任务 {sub_task_id}: 临时文件创建成功，大小: {file_size} bytes")

            try:
                # 调用完整的训练逻辑，返回与原始train_model相同的格式
                result = train_model_background_complete(
                    task_id=sub_task_id,
                    csv_path=temp_file_path,
                    csv_filename=csv_filename,
                    selected_prots=selected_prots,
                    selected_datatypes=selected_datatypes,
                    learning_rate=learning_rate,
                    batch_size=batch_size,
                    epochs=epochs,
                    sequence_length=sequence_length,
                    hidden_size=hidden_size,
                    num_layers=num_layers,
                    dropout=dropout,
                    output_folder=source['outputFolder'],
                    auto_generate_template=auto_generate_template,
                    current_user=current_user
                )
                return result
            finally:
                # 清理临时文件
                if os.path.exists(temp_file_path):
                    os.remove(temp_file_path)

        elif source.get('type') == 'local':
            # 本地文件模式
            csv_filename = source['selectedFile'].split('.')[0] if source.get('selectedFile') else 'local_file'
            csv_path = os.path.join(source['csvDir'], source['selectedFile'])

            if not os.path.exists(csv_path):
                raise FileNotFoundError(f"文件不存在: {csv_path}")

            # 调用完整的训练逻辑
            result = train_model_background_complete(
                task_id=sub_task_id,
                csv_path=csv_path,
                csv_filename=csv_filename,
                selected_prots=selected_prots,
                selected_datatypes=selected_datatypes,
                learning_rate=learning_rate,
                batch_size=batch_size,
                epochs=epochs,
                sequence_length=sequence_length,
                hidden_size=hidden_size,
                num_layers=num_layers,
                dropout=dropout,
                output_folder=source['outputFolder'],
                auto_generate_template=auto_generate_template,
                current_user=current_user
            )

            # 清理临时文件
            if source.get('type') == 'upload' and 'temp_file_path' in locals():
                try:
                    if os.path.exists(temp_file_path):
                        os.unlink(temp_file_path)
                        logging.info(f"任务 {sub_task_id}: 已清理临时文件 {temp_file_path}")
                except Exception as cleanup_error:
                    logging.warning(f"任务 {sub_task_id}: 清理临时文件失败: {cleanup_error}")

            return result
        else:
            raise ValueError(f"无效的数据源配置: {source}")

    except Exception as e:
        logging.error(f"训练数据源 {source.get('id', 'unknown')} 时发生错误: {e}")

        # 异常情况下也要清理临时文件
        if source.get('type') == 'upload' and 'temp_file_path' in locals():
            try:
                if os.path.exists(temp_file_path):
                    os.unlink(temp_file_path)
                    logging.info(f"异常清理: 已删除临时文件 {temp_file_path}")
            except:
                pass

        return {"error": str(e)}

def train_model_background_complete(
    task_id: str,
    csv_path: str,
    csv_filename: str,
    selected_prots: List[str],
    selected_datatypes: Dict[str, List[str]],
    learning_rate: float,
    batch_size: int,
    epochs: int,
    sequence_length: int,
    hidden_size: int,
    num_layers: int,
    dropout: float,
    output_folder: str,
    auto_generate_template: bool,
    current_user: str
) -> Dict[str, Any]:
    """完整的训练逻辑，返回与train_model相同的格式"""

    try:
        # 确保输出目录存在
        os.makedirs(output_folder, exist_ok=True)
        result_filename = f"{csv_filename}_results.txt"
        result_path = os.path.join(output_folder, result_filename)

        results_dict = {}
        total_combinations = sum(len(selected_datatypes.get(prot, [])) for prot in selected_prots)
        combination_count = 0

        # 读取CSV文件
        logging.info(f"任务 {task_id}: 开始从 {csv_path} 分块读取并筛选协议 {selected_prots}...")
        chunk_iterator = pd.read_csv(csv_path, na_values=['', 'NA'], keep_default_na=True, chunksize=100000, low_memory=False)

        filtered_chunks = []
        for chunk in chunk_iterator:
            protocol_filtered_chunk = chunk[chunk['protocol'].isin(selected_prots)]
            if not protocol_filtered_chunk.empty:
                filtered_chunks.append(protocol_filtered_chunk)

        if not filtered_chunks:
            raise HTTPException(status_code=400, detail=f"在文件中找不到任何选定协议的数据: {selected_prots}")

        logging.info(f"任务 {task_id}: 开始合并筛选后的数据块...")
        csv_file = pd.concat(filtered_chunks, ignore_index=True)
        del filtered_chunks
        logging.info(f"任务 {task_id}: 数据块合并完成。DataFrame形状: {csv_file.shape}")

        # 内存与性能优化：转换数据类型（暂时移除category转换以避免多文件训练中的问题）
        # potential_groupby_cols = ['srcaddress', 'dstaddress', 'srcport', 'dstport']
        # for col in potential_groupby_cols:
        #     if col in csv_file.columns:
        #         csv_file[col] = csv_file[col].astype('category')

        if 'tcpflags' in csv_file.columns:
            csv_file['tcpflags'] = pd.to_numeric(csv_file['tcpflags'], downcast='unsigned')

        logging.info(f"任务 {task_id}: 数据类型优化完成（跳过category转换）")

        # 训练每个协议和数据类型组合（直接复制单文件训练的逻辑）
        for selected_prot in selected_prots:
            for selected_datatype in selected_datatypes.get(selected_prot, []):
                # --- 资源监控开始 ---
                start_time = time.time()
                process = psutil.Process(os.getpid())
                cpu_cores = psutil.cpu_count()
                process.cpu_percent()
                cpu_samples = []

                combination_count += 1
                logging.info(f"任务 {task_id}: 正在处理 {combination_count}/{total_combinations}: {selected_prot} - {selected_datatype}")

                # 数据过滤
                df_data_pre = csv_file[csv_file['protocol'] == selected_prot]
                if df_data_pre.empty:
                    logging.warning(f"任务 {task_id}: 协议 {selected_prot} 数据为空，跳过 {selected_prot}/{selected_datatype}")
                    continue

                if 'timestamp' not in df_data_pre.columns or 'packetssam' not in df_data_pre.columns:
                    raise ValueError(f"{selected_prot} - {selected_datatype} 数据缺少 'timestamp' 或 'packetssam' 列！")

                # 根据协议和数据类型过滤（重构优化版）
                result_entry = None  # 初始化result_entry变量
                try:
                    filter_config = {
                        "TCP": {
                            "spt_sip_dip": {'groupby_keys': ['srcaddress', 'dstaddress', 'srcport']},
                            "dpt_sip_dip": {'groupby_keys': ['srcaddress', 'dstaddress', 'dstport']},
                            "len_dpt_syn": {'groupby_keys': ['dstaddress', 'dstport'], 'pre_filter': lambda d: d[d['tcpflags'] == 2]},
                            "seq_ack_dip": {'groupby_keys': ['dstaddress', 'dstport'], 'pre_filter': lambda d: d[d['tcpflags'] == 16]}
                        },
                        "UDP": {
                            "spt_sip_dip": {'groupby_keys': ['srcaddress', 'dstaddress', 'srcport']},
                            "dpt_sip_dip": {'groupby_keys': ['srcaddress', 'dstaddress', 'dstport']}
                        },
                        "ICMP": {
                            "dip": {'groupby_keys': ['dstaddress']}
                        }
                    }

                    prot_config = filter_config.get(selected_prot, {})
                    datatype_config = prot_config.get(selected_datatype)

                    if not datatype_config:
                        raise ValueError(f"无效的协议和数据类型组合: {selected_prot} - {selected_datatype}")

                    df_data = df_data_pre
                    if 'pre_filter' in datatype_config:
                        df_data = datatype_config['pre_filter'](df_data)

                    groupby_keys = datatype_config['groupby_keys']

                    # 修改逻辑：只保留记录数最多的组
                    if not df_data.empty:
                        # 计算每个分组的大小（添加异常处理）
                        try:
                            logging.info(f"任务 {task_id}: 开始执行 groupby 操作，分组键: {groupby_keys}")
                            group_counts = df_data.groupby(groupby_keys).size()
                            logging.info(f"任务 {task_id}: groupby 操作成功，产生 {len(group_counts)} 个分组")
                        except Exception as groupby_error:
                            logging.error(f"任务 {task_id}: groupby 操作失败: {groupby_error}")
                            logging.error(f"任务 {task_id}: 数据形状: {df_data.shape}")
                            logging.error(f"任务 {task_id}: 分组键: {groupby_keys}")
                            for key in groupby_keys:
                                if key in df_data.columns:
                                    logging.error(f"任务 {task_id}: {key} - dtype: {df_data[key].dtype}, nunique: {df_data[key].nunique()}")
                            raise groupby_error

                        # 找到记录数最多的组
                        max_count = group_counts.max()
                        largest_groups = group_counts[group_counts == max_count].index

                        # 如果有多个组都是最大记录数，选择第一个
                        if len(largest_groups) > 1:
                            largest_group = largest_groups[0]
                            logging.info(f"任务 {task_id}: {selected_prot} - {selected_datatype} 发现 {len(largest_groups)} 个最大组（记录数={max_count}），选择第一个: {largest_group}")
                        else:
                            largest_group = largest_groups[0]
                            logging.info(f"任务 {task_id}: {selected_prot} - {selected_datatype} 选择最大组（记录数={max_count}）: {largest_group}")

                        # 只保留最大组的数据
                        if isinstance(largest_group, tuple):
                            # 多列分组的情况
                            mask = True
                            for i, key in enumerate(groupby_keys):
                                mask = mask & (df_data[key] == largest_group[i])
                            df_data = df_data[mask]
                        else:
                            # 单列分组的情况
                            df_data = df_data[df_data[groupby_keys[0]] == largest_group]

                    if df_data.empty:
                        logging.warning(f"任务 {task_id}: {selected_prot} - {selected_datatype} 筛选后数据集为空，跳过。")
                        continue

                    # 时间处理和重采样（优化版）
                    df = df_data.copy()
                    del df_data
                    df['timestamp'] = pd.to_datetime(df['timestamp'], unit='s')

                    df = df[df['timestamp'] >= '2025-03-01']
                    if df.empty:
                        logging.warning(f"任务 {task_id}: {selected_prot} - {selected_datatype} 在 2025-03-01 之后无数据，跳过。")
                        continue

                    df = df.sort_values('timestamp')
                    resampled = df.resample('120s', on='timestamp').agg(
                        total_packetssam=('packetssam', 'sum'),
                        count=('packetssam', 'count')
                    )
                    resampled['packets_per_sec'] = resampled['total_packetssam'] / 960

                    # --- 改进：使用智能插值策略，区分真实零值和缺失值 ---
                    resampled = optimized_interpolation_pipeline(resampled)

                    resampled = resampled.reset_index()
                    resampled = resampled[resampled['packets_per_sec'] <= 5000000]

                    # --- 核心策略调整：对极度噪声的数据进行平滑处理，让模型学习趋势而非噪声 ---
                    smoothing_window = 5
                    nf_data = resampled[['timestamp', 'packets_per_sec']].copy()
                    nf_data['packets_per_sec_smooth'] = nf_data['packets_per_sec'].rolling(window=smoothing_window, min_periods=1).mean()

                    if nf_data.empty or len(nf_data) < sequence_length * 2:
                        logging.warning(f"任务 {task_id}: {selected_prot} - {selected_datatype} 数据不足以创建序列，跳过。")
                        continue

                    # --- 特征工程 ---
                    nf_data = nf_data.sort_values('timestamp').reset_index(drop=True)

                    # --- 对目标变量（平滑后的数据）进行对数变换和差分 ---
                    nf_data['packets_per_sec_log'] = np.log1p(nf_data['packets_per_sec_smooth'])
                    nf_data['packets_per_sec_log_diff'] = nf_data['packets_per_sec_log'].diff().fillna(0)

                    # --- 数据准备 ---
                    target_col = 'packets_per_sec_log_diff'

                    # 1. 按时间顺序划分数据集
                    split_idx = int(len(nf_data) * 0.8)
                    train_df = nf_data.iloc[:split_idx].copy()
                    test_df = nf_data.iloc[split_idx:].copy()

                    # 2. 只为目标（现在也是唯一的特征）创建并拟合Scaler
                    scaler_y = RobustScaler()

                    train_df[[target_col]] = scaler_y.fit_transform(train_df[[target_col]])
                    test_df[[target_col]] = scaler_y.transform(test_df[[target_col]])

                    # 3. 创建序列
                    X_train, y_train_scaled_log = create_sequences(train_df[[target_col]].values, train_df[target_col].values, sequence_length)
                    X_test, y_test_scaled_log = create_sequences(test_df[[target_col]].values, test_df[target_col].values, sequence_length)

                    if X_train.shape[0] == 0 or X_test.shape[0] == 0:
                        logging.warning(f"任务 {task_id}: {selected_prot} - {selected_datatype} 划分后数据不足以创建序列，跳过。")
                        continue

                    # 将数据转换为PyTorch张量
                    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
                    num_gpus = torch.cuda.device_count() if torch.cuda.is_available() else 0

                    X_train_tensor = torch.tensor(X_train, dtype=torch.float32).to(device)
                    y_train_tensor = torch.tensor(y_train_scaled_log, dtype=torch.float32).view(-1, 1).to(device)
                    X_test_tensor = torch.tensor(X_test, dtype=torch.float32).to(device)
                    y_test_tensor = torch.tensor(y_test_scaled_log, dtype=torch.float32).view(-1, 1).to(device)

                    # 初始化模型
                    model = GRUModel(
                        input_size=1,
                        hidden_size=hidden_size,
                        num_layers=num_layers,
                        dropout=dropout
                    ).to(device)

                    if num_gpus > 1:
                        model = nn.DataParallel(model)

                    # --- 更换损失函数为SmoothL1Loss，它对异常值（流量尖峰）不那么敏感 ---
                    criterion = nn.SmoothL1Loss()
                    optimizer = optim.AdamW(model.parameters(), lr=learning_rate, weight_decay=1e-5)

                    # --- 更换为StepLR调度器，以固定周期强制降低学习率，帮助模型跳出局部最优 ---
                    scheduler = optim.lr_scheduler.StepLR(optimizer, step_size=30, gamma=0.1)

                    # 文件名 - 添加时间戳确保唯一性
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    base_filename = f"{csv_filename}_{selected_prot}_{selected_datatype}_{timestamp}"
                    model_filename = f"{base_filename}_model_best.pth"
                    params_filename = f"{base_filename}_params.json"
                    scaler_y_filename = f"{base_filename}_scaler_y_best.pkl"
                    test_filename = f"{base_filename}_test.csv"
                    predictions_filename = f"{base_filename}_predictions.csv"
                    model_save_path = os.path.join(output_folder, model_filename)
                    params_save_path = os.path.join(output_folder, params_filename)
                    scaler_y_save_path = os.path.join(output_folder, scaler_y_filename)
                    test_save_path = os.path.join(output_folder, test_filename)
                    output_csv_path = os.path.join(output_folder, predictions_filename)

                    # 训练循环
                    early_stopping_patience = 50
                    best_val_loss = float('inf')
                    patience_counter = 0
                    train_losses = []
                    val_losses = []

                    for epoch in range(epochs):
                        # 定期采样CPU使用率（每10个epoch采样一次）
                        if epoch % 10 == 0:
                            current_cpu = process.cpu_percent()
                            if current_cpu > 0:
                                cpu_samples.append(current_cpu)

                        model.train()
                        epoch_loss = 0
                        permutation = torch.randperm(X_train_tensor.size(0))
                        for i in range(0, X_train_tensor.size(0), batch_size):
                            indices = permutation[i:i + batch_size]
                            batch_X, batch_y = X_train_tensor[indices], y_train_tensor[indices]
                            optimizer.zero_grad()
                            outputs = model(batch_X)
                            loss = criterion(outputs, batch_y)
                            loss.backward()

                            # --- 新增：梯度裁剪，防止梯度爆炸，稳定长序列训练 ---
                            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)

                            optimizer.step()
                            epoch_loss += loss.item()

                        avg_train_loss = epoch_loss / (len(permutation) // batch_size) if (len(permutation) // batch_size) > 0 else epoch_loss
                        train_losses.append(float(avg_train_loss))

                        model.eval()
                        with torch.no_grad():
                            val_loss = criterion(model(X_test_tensor), y_test_tensor).item()
                            val_losses.append(float(val_loss))

                        # --- 在每个epoch后，根据固定的策略调整学习率 ---
                        scheduler.step()

                        if val_loss < best_val_loss:
                            best_val_loss = val_loss
                            patience_counter = 0
                            torch.save(model.module.state_dict() if num_gpus > 1 else model.state_dict(), model_save_path)
                        else:
                            patience_counter += 1
                            if patience_counter >= early_stopping_patience:
                                break

                    # 评估模型
                    model_to_load = GRUModel(
                        input_size=1,
                        hidden_size=hidden_size,
                        num_layers=num_layers,
                        dropout=dropout
                    ).to(device)
                    model_to_load.load_state_dict(torch.load(model_save_path, map_location=device, weights_only=True))
                    model_to_load.eval()

                    with torch.no_grad():
                        y_pred_scaled_log_tensor = model_to_load(X_test_tensor)
                        y_pred_scaled_log = y_pred_scaled_log_tensor.cpu().numpy().flatten()

                    # 反转缩放和对数变换
                    y_pred_scaled_log_reshaped = y_pred_scaled_log.reshape(-1, 1)
                    y_pred_diff_log = scaler_y.inverse_transform(y_pred_scaled_log_reshaped).flatten()

                    last_log_values = nf_data['packets_per_sec_log'].iloc[split_idx + sequence_length - 1 : -1].values
                    min_len_inv = min(len(y_pred_diff_log), len(last_log_values))
                    y_pred_diff_log = y_pred_diff_log[:min_len_inv]
                    last_log_values = last_log_values[:min_len_inv]
                    y_pred_log = last_log_values + y_pred_diff_log
                    y_pred_smooth = np.expm1(y_pred_log)

                    y_test_actual_smooth = nf_data['packets_per_sec_smooth'].iloc[split_idx + sequence_length:].values
                    min_len = min(len(y_pred_smooth), len(y_test_actual_smooth))
                    y_pred = y_pred_smooth[:min_len]
                    y_test_actual = y_test_actual_smooth[:min_len]

                    if len(y_pred) == 0:
                        logging.error(f"任务 {task_id}: {selected_prot}/{selected_datatype} 无法生成有效预测，跳过评估")
                        continue

                    # 计算评估指标
                    mse = float(mean_squared_error(y_test_actual, y_pred))
                    r2 = float(r2_score(y_test_actual, y_pred))
                    mae = float(mean_absolute_error(y_test_actual, y_pred))

                    y_test_actual_rounded = np.round(y_test_actual).astype(int)
                    y_pred_rounded = np.round(y_pred).astype(int)

                    # 计算异常检测阈值
                    prediction_errors = y_test_actual - y_pred
                    error_mean = float(np.mean(prediction_errors))
                    error_std = float(np.std(prediction_errors))
                    k_sigma = 3.0
                    static_anomaly_threshold = float(np.mean(y_pred) + error_mean + k_sigma * error_std)

                    # 资源监控结束
                    duration = time.time() - start_time
                    if cpu_samples:
                        cpu_usage = sum(cpu_samples) / len(cpu_samples)
                    else:
                        cpu_usage = process.cpu_percent(interval=1.0)

                    cpu_usage_normalized = cpu_usage / cpu_cores if cpu_cores else cpu_usage
                    memory_mb = process.memory_info().rss / (1024 * 1024)

                    # GPU资源监控
                    gpu_memory_mb = 0
                    gpu_utilization_percent = 0
                    if pynvml_available and device.type == 'cuda':
                        try:
                            pynvml.nvmlInit()
                            num_gpus = torch.cuda.device_count()
                            total_mem_used = 0
                            total_util = 0
                            for i in range(num_gpus):
                                handle = pynvml.nvmlDeviceGetHandleByIndex(i)
                                mem_info = pynvml.nvmlDeviceGetMemoryInfo(handle)
                                util_rates = pynvml.nvmlDeviceGetUtilizationRates(handle)
                                total_mem_used += mem_info.used / (1024 * 1024)
                                total_util += util_rates.gpu
                            gpu_memory_mb = total_mem_used
                            gpu_utilization_percent = total_util / num_gpus if num_gpus > 0 else 0
                            pynvml.nvmlShutdown()
                        except pynvml.NVMLError:
                            pass

                    # 保存模型和相关文件
                    torch.save(model_to_load.state_dict(), model_save_path)
                    joblib.dump(scaler_y, scaler_y_save_path)

                    model_params = {
                        "input_size": 1,
                        "hidden_size": hidden_size,
                        "num_layers": num_layers,
                        "dropout": dropout,
                        "sequence_length": sequence_length,
                        "anomaly_threshold_mean": error_mean,
                        "anomaly_threshold_std": error_std
                    }
                    with open(params_save_path, 'w') as f:
                        json.dump(model_params, f)

                    # 保存结果
                    result_df = test_df.iloc[sequence_length:].copy()
                    result_df['packets_per_sec'] = y_test_actual_rounded
                    result_df['pred'] = y_pred_rounded
                    result_df[['timestamp', 'packets_per_sec', 'pred']].to_csv(output_csv_path, index=False)
                    test_df.iloc[sequence_length:].to_csv(test_save_path, index=False)

                    # 计算加权平均值
                    value_counts = Counter(y_test_actual_rounded)
                    total_samples = len(y_test_actual_rounded)
                    y_actual_weights = {value: count / total_samples for value, count in value_counts.items()}

                    try:
                        pred_weights = np.array([y_actual_weights.get(act, 0) for act in y_test_actual_rounded])
                        weight_avg = float(np.sum(y_pred_rounded * pred_weights) / np.sum(pred_weights) if np.sum(pred_weights) > 0 else np.mean(y_pred_rounded))
                    except Exception as e:
                        weight_avg = float(np.mean(y_pred_rounded))

                    finished_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")
                    result_entry = {
                        "train_shape": list(train_df.shape),
                        "test_shape": list(test_df.shape),
                        "train_losses": [float(x) for x in train_losses],
                        "val_losses": [float(x) for x in val_losses],
                        "r2": r2,
                        "y_test_actual": y_test_actual_rounded.tolist(),
                        "y_pred": y_pred_rounded.tolist(),
                        "output_csv_path": output_csv_path,
                        "predictions_filename": predictions_filename,
                        "weight_avg": weight_avg,
                        "r2_score": r2,
                        "finished_time": finished_time,
                        "model_save_path": model_save_path,
                        "params_save_path": params_save_path,
                        "test_save_path": test_save_path,
                        "scaler_y_save_path": scaler_y_save_path,
                        "static_anomaly_threshold": static_anomaly_threshold,
                        "duration_seconds": duration,
                        "cpu_percent": cpu_usage_normalized,
                        "memory_mb": memory_mb,
                        "gpu_memory_mb": gpu_memory_mb,
                        "gpu_utilization_percent": gpu_utilization_percent
                    }

                    # 注册模型到模型仓库
                    try:
                        model_info = {
                            "model_name": f"{selected_prot}_{selected_datatype}",
                            "model_type": "GRU",
                            "training_time": finished_time,
                            "data_file": csv_filename,
                            "protocol": selected_prot,
                            "data_type": selected_datatype,
                            "r2_score": r2,
                            "cleaning_threshold": static_anomaly_threshold,
                            "duration_seconds": duration,
                            "cpu_percent": cpu_usage_normalized,
                            "memory_mb": memory_mb,
                            "gpu_memory_mb": gpu_memory_mb,
                            "gpu_utilization_percent": gpu_utilization_percent,
                            "hidden_size": hidden_size,
                            "num_layers": num_layers,
                            "sequence_length": sequence_length,
                            "dropout": dropout,
                            "learning_rate": learning_rate,
                            "batch_size": batch_size,
                            "epochs": epochs,
                            "model_path": model_save_path,
                            "params_path": params_save_path,
                            "scaler_path": scaler_y_save_path,
                            "test_data_path": test_save_path,
                            "train_shape": list(train_df.shape),
                            "test_shape": list(test_df.shape)
                        }

                        from .model_registry import register_model as registry_register_model
                        import asyncio

                        try:
                            loop = asyncio.get_event_loop()
                            registry_result = loop.run_until_complete(registry_register_model(model_info, current_user))
                        except RuntimeError:
                            registry_result = asyncio.run(registry_register_model(model_info, current_user))

                        if registry_result.get("success"):
                            model_id = registry_result.get("model_id")
                            logging.info(f"任务 {task_id}: 模型 {selected_prot}_{selected_datatype} 已注册到仓库，ID: {model_id}")
                            result_entry["model_id"] = model_id
                        else:
                            logging.warning(f"任务 {task_id}: 模型 {selected_prot}_{selected_datatype} 注册到仓库失败")

                    except Exception as registry_error:
                        logging.warning(f"任务 {task_id}: 模型 {selected_prot}_{selected_datatype} 注册到仓库时发生异常: {registry_error}")

                except Exception as e:
                    logging.error(f"任务 {task_id}: 处理 {selected_prot}/{selected_datatype} 失败: {e}")
                    continue

                # 只有当训练成功时才处理结果
                if result_entry is not None:
                    # 将成功的结果添加到结果字典中
                    results_dict[f"{selected_prot}_{selected_datatype}"] = result_entry

                    # 更新结果文件
                    try:
                        updated_results = []
                        if os.path.exists(result_path):
                            with open(result_path, 'r', encoding='utf-8') as f:
                                updated_results = f.readlines()

                        threshold_to_write = np.round(result_entry.get('static_anomaly_threshold', 0)).astype(int)
                        new_result = f"{selected_prot} {selected_datatype} {threshold_to_write}\n"

                        replaced = False
                        for i, line in enumerate(updated_results):
                            parts = line.strip().split()
                            if len(parts) >= 2 and parts[0] == selected_prot and parts[1] == selected_datatype:
                                updated_results[i] = new_result
                                replaced = True
                                break
                        if not replaced:
                            updated_results.append(new_result)

                        with open(result_path, 'w', encoding='utf-8') as f:
                            f.writelines(updated_results)
                    except Exception as e:
                        logging.error(f"任务 {task_id}: 无法写入结果文件 {result_path}: {e}")
                else:
                    logging.warning(f"任务 {task_id}: {selected_prot} - {selected_datatype} 训练失败，跳过结果处理")

        # 自动生成清洗模板（如果用户选择了该选项）
        template_info = None
        if auto_generate_template and result_path and os.path.exists(result_path):
            try:
                from .clean_template import generate_clean_template
                import asyncio

                # 调用清洗模板生成功能
                try:
                    loop = asyncio.get_event_loop()
                    template_result = loop.run_until_complete(generate_clean_template(
                        results_file=result_path,
                        output_folder=output_folder,
                        template_name=None,
                        current_user=current_user
                    ))
                except RuntimeError:
                    template_result = asyncio.run(generate_clean_template(
                        results_file=result_path,
                        output_folder=output_folder,
                        template_name=None,
                        current_user=current_user
                    ))

                template_info = {
                    "template_generated": True,
                    "template_path": template_result.get("template_path"),
                    "updated_thresholds": template_result.get("updated_thresholds")
                }
                logging.info(f"任务 {task_id}: 自动生成清洗模板成功 - {template_result.get('template_path')}")

            except Exception as template_error:
                logging.warning(f"任务 {task_id}: 自动生成清洗模板失败: {template_error}")
                template_info = {
                    "template_generated": False,
                    "error": str(template_error)
                }

        # 构建返回结果
        result = {"results": results_dict, "result_path": result_path}
        if template_info:
            result["template_info"] = template_info

        logging.info(f"任务 {task_id}: 完整训练过程完成，处理了 {len(results_dict)} 个组合")

        # 训练完成后的内存清理
        cleanup_training_memory(task_id, csv_file)

        return result

    except Exception as e:
        logging.error(f"任务 {task_id}: 完整训练过程失败: {e}")
        return {"error": str(e)}


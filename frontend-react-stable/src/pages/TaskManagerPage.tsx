import React, { useState, useEffect } from 'react';
import {
  Card,
  Row,
  Col,
  Statistic,
  List,
  Button,
  Space,
  Typography,
  Tag,
  Progress,
  Modal,
  Empty,
  message,
  Spin,
  Tooltip,
  Popconfirm,
  Pagination
} from 'antd';
import {
  SyncOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  StopOutlined,
  ClockCircleOutlined,
  EyeOutlined,
  DeleteOutlined,
  ReloadOutlined,
  PlayCircleOutlined,
  MinusCircleOutlined,
  ThunderboltOutlined
} from '@ant-design/icons';
import useTaskManager from '../hooks/useTaskManager';
import { Task } from '../services/taskApi';

const { Title, Text } = Typography;

const TaskManagerPage: React.FC = () => {
  const {
    runningTasks,
    completedTasks,
    loading,
    fetchRunningTasks,
    fetchCompletedTasks,
    cancelTask,
    deleteSingleTask,
    clearCompletedTasks,
    formatTaskType,
    TASK_STATUS
  } = useTaskManager();

  const [selectedTask, setSelectedTask] = useState<Task | null>(null);
  const [taskDetailVisible, setTaskDetailVisible] = useState(false);
  const [refreshing, setRefreshing] = useState(false);

  // 分页相关状态
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);

  // 页面加载时获取任务数据
  useEffect(() => {
    fetchRunningTasks();
    fetchCompletedTasks();
  }, [fetchRunningTasks, fetchCompletedTasks]);

  // 自动刷新运行中的任务
  useEffect(() => {
    const interval = setInterval(() => {
      fetchRunningTasks(false); // 静默刷新
    }, 5000); // 每5秒刷新一次

    return () => clearInterval(interval);
  }, [fetchRunningTasks]);

  // 手动刷新
  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      await Promise.all([
        fetchRunningTasks(),
        fetchCompletedTasks()
      ]);
      message.success('刷新成功');
    } catch (error) {
      message.error('刷新失败');
    } finally {
      setRefreshing(false);
    }
  };

  // 删除单个任务
  const handleDeleteSingleTask = (taskId: string) => {
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除任务 ${taskId.substring(0, 8)}... 吗？`,
      icon: <MinusCircleOutlined style={{ color: '#ff4d4f' }} />,
      okText: '删除',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        const success = await deleteSingleTask(taskId);
        if (success) {
          // 刷新任务列表
          await Promise.all([
            fetchRunningTasks(),
            fetchCompletedTasks()
          ]);
        }
      }
    });
  };

  // 查看任务详情
  const handleViewTaskDetail = (task: Task) => {
    setSelectedTask(task);
    setTaskDetailVisible(true);
  };

  // 取消任务
  const handleCancelTask = async (taskId: string) => {
    try {
      await cancelTask(taskId);
      await fetchRunningTasks(); // 刷新运行中任务列表
    } catch (error) {
      console.error('取消任务失败:', error);
    }
  };

  // 清空所有已完成任务
  const handleClearCompleted = () => {
    const completedCount = completedTasks.length;
    if (completedCount === 0) {
      message.info('没有已完成的任务需要清空');
      return;
    }

    Modal.confirm({
      title: '确认清空所有已完成任务',
      content: `确定要清空所有 ${completedCount} 个已完成的任务吗？此操作不可撤销。`,
      icon: <DeleteOutlined style={{ color: '#ff4d4f' }} />,
      okText: '清空',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        try {
          await clearCompletedTasks();
          // 刷新任务列表
          await Promise.all([
            fetchRunningTasks(),
            fetchCompletedTasks()
          ]);
        } catch (error) {
          console.error('清空任务失败:', error);
        }
      }
    });
  };

  // 获取任务状态图标
  const getTaskStatusIcon = (status: string) => {
    switch (status) {
      case TASK_STATUS.RUNNING:
        return <SyncOutlined spin style={{ color: '#1890ff' }} />;
      case TASK_STATUS.COMPLETED:
        return <CheckCircleOutlined style={{ color: '#52c41a' }} />;
      case TASK_STATUS.FAILED:
        return <CloseCircleOutlined style={{ color: '#ff4d4f' }} />;
      case TASK_STATUS.CANCELLED:
        return <StopOutlined style={{ color: '#faad14' }} />;
      default:
        return <ClockCircleOutlined style={{ color: '#d9d9d9' }} />;
    }
  };

  // 获取任务状态标签
  const getTaskStatusTag = (status: string) => {
    switch (status) {
      case TASK_STATUS.RUNNING:
        return <Tag color="processing" icon={<SyncOutlined spin />}>运行中</Tag>;
      case TASK_STATUS.COMPLETED:
        return <Tag color="success" icon={<CheckCircleOutlined />}>已完成</Tag>;
      case TASK_STATUS.FAILED:
        return <Tag color="error" icon={<CloseCircleOutlined />}>失败</Tag>;
      case TASK_STATUS.CANCELLED:
        return <Tag color="warning" icon={<StopOutlined />}>已取消</Tag>;
      default:
        return <Tag color="default" icon={<ClockCircleOutlined />}>等待中</Tag>;
    }
  };

  // 格式化时间
  const formatTime = (timeString?: string) => {
    if (!timeString) return '未知';
    return new Date(timeString).toLocaleString('zh-CN');
  };

  // 计算当前页的已完成任务数据
  const getCurrentPageCompletedTasks = () => {
    const startIndex = (currentPage - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    return completedTasks.slice(startIndex, endIndex);
  };

  // 分页变化处理
  const handlePageChange = (page: number, size?: number) => {
    setCurrentPage(page);
    if (size && size !== pageSize) {
      setPageSize(size);
      setCurrentPage(1); // 改变页面大小时重置到第一页
    }
  };

  return (
    <div>
      <Title level={3} style={{ fontSize: '20px', fontWeight: 600, marginBottom: '8px' }}>任务管理</Title>
      <Text type="secondary" style={{ marginBottom: '24px', display: 'block' }}>
        查看和管理异步训练、预测任务的状态和结果
      </Text>

      <div style={{ marginBottom: '24px', display: 'flex', justifyContent: 'flex-end' }}>
        <Button
          type="primary"
          icon={<ReloadOutlined />}
          onClick={handleRefresh}
          loading={refreshing}
        >
          刷新
        </Button>
      </div>

      {/* 任务统计 */}
      <Card className="function-card" title="任务统计">
        <Row gutter={32} justify="space-between">
          <Col flex="1">
            <Statistic
              title="运行中任务"
              value={runningTasks.length}
              prefix={<ThunderboltOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Col>
          <Col flex="1">
            <Statistic
              title="已完成任务"
              value={completedTasks.filter(t => t.status === TASK_STATUS.COMPLETED).length}
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Col>
          <Col flex="1">
            <Statistic
              title="失败任务"
              value={completedTasks.filter(t => t.status === TASK_STATUS.FAILED).length}
              prefix={<CloseCircleOutlined />}
              valueStyle={{ color: '#ff4d4f' }}
            />
          </Col>
          <Col flex="1">
            <Statistic
              title="已取消任务"
              value={completedTasks.filter(t => t.status === TASK_STATUS.CANCELLED).length}
              prefix={<MinusCircleOutlined />}
              valueStyle={{ color: '#faad14' }}
            />
          </Col>
          <Col flex="1">
            <Statistic
              title="总任务数"
              value={runningTasks.length + completedTasks.length}
              prefix={<PlayCircleOutlined />}
            />
          </Col>
        </Row>
      </Card>

      <Row gutter={[24, 24]}>
        {/* 运行中任务 */}
        <Col span={12}>
          <Card
            className="function-card"
            title={
              <Space>
                <ThunderboltOutlined />
                运行中任务
                <Tag color="processing">{runningTasks.length}</Tag>
              </Space>
            }
            extra={
              <Tooltip title="点击刷新">
                <Button
                  type="text"
                  icon={<ReloadOutlined />}
                  onClick={handleRefresh}
                  loading={refreshing}
                  size="small"
                />
              </Tooltip>
            }
          >
            <Spin spinning={loading}>
              <List
                dataSource={runningTasks}
                locale={{ emptyText: <Empty description="暂无运行中的任务" /> }}
                renderItem={(task) => (
                  <List.Item
                    actions={[
                      <Button
                        type="link"
                        icon={<EyeOutlined />}
                        onClick={() => handleViewTaskDetail(task)}
                      >
                        详情
                      </Button>,
                      <Popconfirm
                        title="确定要取消这个任务吗？"
                        onConfirm={() => handleCancelTask(task.task_id)}
                        okText="确定"
                        cancelText="取消"
                      >
                        <Button
                          type="link"
                          danger
                          icon={<StopOutlined />}
                        >
                          取消
                        </Button>
                      </Popconfirm>
                    ]}
                  >
                    <List.Item.Meta
                      avatar={getTaskStatusIcon(task.status)}
                      title={
                        <Space>
                          <Text strong>{formatTaskType(task.task_type)}</Text>
                          {getTaskStatusTag(task.status)}
                        </Space>
                      }
                      description={
                        <div>
                          <Text type="secondary" copyable={{ text: task.task_id }}>
                            ID: {task.task_id.includes('_') ?
                              `${task.task_id.split('_')[0]}...${task.task_id.split('_').slice(-1)[0].substring(0, 8)}` :
                              `${task.task_id.substring(0, 8)}...`
                            }
                          </Text>
                          <br />
                          <Text type="secondary">开始时间: {formatTime(task.created_at)}</Text>
                          {task.message && (
                            <>
                              <br />
                              <Text type="secondary">状态: {task.message}</Text>
                            </>
                          )}
                          {task.progress !== undefined && (
                            <div style={{ marginTop: 8 }}>
                              <Progress percent={task.progress} size="small" />
                            </div>
                          )}
                        </div>
                      }
                    />
                  </List.Item>
                )}
              />
            </Spin>
          </Card>
        </Col>

        {/* 已完成任务 */}
        <Col span={12}>
          <Card
            className="function-card"
            title={
              <Space>
                <CheckCircleOutlined />
                已完成任务
                <Tag color="success">{completedTasks.length}</Tag>
              </Space>
            }
            extra={
              <Button
                type="primary"
                danger
                size="small"
                icon={<DeleteOutlined />}
                disabled={completedTasks.length === 0}
                onClick={handleClearCompleted}
              >
                清空全部
              </Button>
            }
          >
            <List
              dataSource={getCurrentPageCompletedTasks()}
              locale={{
                emptyText: (
                  <Empty
                    description={
                      <div>
                        <div>暂无已完成的任务</div>
                        <Text type="secondary" style={{ fontSize: '12px' }}>
                          异步训练和预测完成后，结果会显示在这里
                        </Text>
                      </div>
                    }
                  />
                )
              }}
              renderItem={(task) => (
                <List.Item
                  actions={[
                    <Button
                      type="link"
                      icon={<EyeOutlined />}
                      onClick={() => handleViewTaskDetail(task)}
                    >
                      详情
                    </Button>,
                    <Button
                      type="link"
                      danger
                      icon={<MinusCircleOutlined />}
                      onClick={() => handleDeleteSingleTask(task.task_id)}
                    >
                      删除
                    </Button>
                  ]}
                >
                  <List.Item.Meta
                    avatar={getTaskStatusIcon(task.status)}
                    title={
                      <Space>
                        <Text strong>{formatTaskType(task.task_type)}</Text>
                        {getTaskStatusTag(task.status)}
                      </Space>
                    }
                    description={
                      <div>
                        <Text type="secondary" copyable={{ text: task.task_id }}>
                          ID: {task.task_id.includes('_') ?
                            `${task.task_id.split('_')[0]}...${task.task_id.split('_').slice(-1)[0].substring(0, 8)}` :
                            `${task.task_id.substring(0, 8)}...`
                          }
                        </Text>
                        <br />
                        <Text type="secondary">完成时间: {formatTime(task.updated_at)}</Text>
                        {task.status === TASK_STATUS.FAILED && task.error && (
                          <>
                            <br />
                            <Text type="danger">错误: {task.error}</Text>
                          </>
                        )}
                      </div>
                    }
                  />
                </List.Item>
              )}
            />

            {/* 分页组件 */}
            {completedTasks.length > 0 && (
              <div style={{ marginTop: 16, textAlign: 'center' }}>
                <Pagination
                  current={currentPage}
                  total={completedTasks.length}
                  pageSize={pageSize}
                  showSizeChanger
                  showQuickJumper
                  showTotal={(total, range) =>
                    `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
                  }
                  pageSizeOptions={['5', '10', '20', '50']}
                  onChange={handlePageChange}
                  onShowSizeChange={handlePageChange}
                />
              </div>
            )}
          </Card>
        </Col>
      </Row>

      {/* 任务详情模态框 */}
      <Modal
        title="任务详情"
        open={taskDetailVisible}
        onCancel={() => setTaskDetailVisible(false)}
        footer={[
          <Button key="close" onClick={() => setTaskDetailVisible(false)}>
            关闭
          </Button>
        ]}
        width={800}
      >
        {selectedTask && (
          <div>
            <Row gutter={[16, 16]}>
              <Col span={12}>
                <Text strong>任务ID:</Text>
                <br />
                <Text copyable>{selectedTask.task_id}</Text>
              </Col>
              <Col span={12}>
                <Text strong>任务类型:</Text>
                <br />
                <Text>{formatTaskType(selectedTask.task_type)}</Text>
              </Col>
              <Col span={12}>
                <Text strong>模型信息:</Text>
                <br />
                <Text>
                  {selectedTask.result?.is_multi_model || selectedTask.result?.multi_model_results
                    ? `多模型预测 (${selectedTask.result?.total_models || selectedTask.result?.summary?.total_models || 0}个模型)`
                    : (selectedTask.result?.model_name || '未知模型')
                  }
                </Text>
              </Col>
              <Col span={12}>
                <Text strong>状态:</Text>
                <br />
                {getTaskStatusTag(selectedTask.status)}
              </Col>
              <Col span={12}>
                <Text strong>进度:</Text>
                <br />
                {selectedTask.status === TASK_STATUS.COMPLETED ? (
                  <Progress percent={100} size="small" status="success" />
                ) : selectedTask.status === TASK_STATUS.FAILED ? (
                  <Progress percent={selectedTask.progress || 0} size="small" status="exception" />
                ) : selectedTask.status === TASK_STATUS.CANCELLED ? (
                  <Progress percent={selectedTask.progress || 0} size="small" status="exception" />
                ) : selectedTask.progress !== undefined ? (
                  <Progress percent={selectedTask.progress} size="small" />
                ) : (
                  <Text type="secondary">无进度信息</Text>
                )}
              </Col>
              <Col span={12}>
                <Text strong>创建时间:</Text>
                <br />
                <Text>{formatTime(selectedTask.created_at)}</Text>
              </Col>
              <Col span={12}>
                <Text strong>更新时间:</Text>
                <br />
                <Text>{formatTime(selectedTask.updated_at)}</Text>
              </Col>
              <Col span={12}>
                <Text strong>执行时长:</Text>
                <br />
                <Text>
                  {(() => {
                    // 如果任务已完成、失败或取消，且有开始时间和完成时间
                    if (selectedTask.started_at && selectedTask.completed_at) {
                      const duration = Math.round((new Date(selectedTask.completed_at).getTime() - new Date(selectedTask.started_at).getTime()) / 1000);
                      return `${duration}秒`;
                    }
                    // 如果任务正在运行，且有开始时间
                    else if (selectedTask.started_at && selectedTask.status === TASK_STATUS.RUNNING) {
                      const duration = Math.round((new Date().getTime() - new Date(selectedTask.started_at).getTime()) / 1000);
                      return `${duration}秒 (进行中)`;
                    }
                    // 如果任务正在运行但没有开始时间，说明任务还在等待开始
                    else if (selectedTask.status === TASK_STATUS.RUNNING && !selectedTask.started_at) {
                      return '准备中...';
                    }
                    // 如果任务已完成但没有开始时间，尝试从结果中获取时长
                    else if (selectedTask.status === TASK_STATUS.COMPLETED && selectedTask.result?.duration_seconds) {
                      return `${Math.round(selectedTask.result.duration_seconds)}秒`;
                    }
                    // 如果任务已完成但没有时间信息，显示已完成
                    else if (selectedTask.status === TASK_STATUS.COMPLETED) {
                      return '已完成';
                    }
                    // 如果任务失败或取消，显示相应状态
                    else if (selectedTask.status === TASK_STATUS.FAILED) {
                      return '执行失败';
                    }
                    else if (selectedTask.status === TASK_STATUS.CANCELLED) {
                      return '已取消';
                    }
                    // 其他情况显示等待开始
                    else {
                      return '等待开始';
                    }
                  })()}
                </Text>
              </Col>
              {selectedTask.current_step && selectedTask.status !== TASK_STATUS.COMPLETED && (
                <Col span={24}>
                  <Text strong>当前步骤:</Text>
                  <br />
                  <Text>{selectedTask.current_step}</Text>
                </Col>
              )}
              {selectedTask.message && (
                <Col span={24}>
                  <Text strong>消息:</Text>
                  <br />
                  <Text>{selectedTask.message}</Text>
                </Col>
              )}
              {selectedTask.error && (
                <Col span={24}>
                  <Text strong>错误信息:</Text>
                  <br />
                  <Text type="danger">{selectedTask.error}</Text>
                </Col>
              )}
              {selectedTask.params && (
                <Col span={24}>
                  <Text strong>任务参数:</Text>
                  <br />
                  <div style={{
                    background: '#f5f5f5',
                    padding: 12,
                    borderRadius: 4,
                    fontSize: 12,
                    maxHeight: 200,
                    overflow: 'auto',
                    marginTop: 8
                  }}>
                    <pre>{JSON.stringify(selectedTask.params, null, 2)}</pre>
                  </div>
                </Col>
              )}
              {selectedTask.result && selectedTask.task_type === 'training' && (
                <Col span={24}>
                  <Text strong>训练完成:</Text>
                  <br />
                  <Space wrap style={{ marginTop: 8 }}>
                    <Tag color="green" icon={<CheckCircleOutlined />}>
                      训练已完成
                    </Tag>
                    {selectedTask.result.duration_seconds && (
                      <Tag color="blue">
                        耗时: {Math.round(selectedTask.result.duration_seconds)}秒
                      </Tag>
                    )}
                    {selectedTask.result.results && (
                      <Tag color="purple">
                        模型数量: {Object.keys(selectedTask.result.results).length}
                      </Tag>
                    )}
                  </Space>
                  <div style={{ marginTop: 8 }}>
                    <Text type="secondary">
                      💡 训练结果详情请前往"模型训练"页面查看
                    </Text>
                  </div>
                </Col>
              )}
              {selectedTask.result && selectedTask.task_type === 'prediction' && (
                <Col span={24}>
                  <Text strong>预测完成:</Text>
                  <br />
                  <Space wrap style={{ marginTop: 8 }}>
                    <Tag color="green" icon={<CheckCircleOutlined />}>
                      {selectedTask.result.multi_model_results ? '多模型预测已完成' : '预测已完成'}
                    </Tag>

                    {/* 多模型预测结果显示 */}
                    {selectedTask.result.is_multi_model || selectedTask.result.multi_model_results ? (
                      <>
                        <Tag color="blue">
                          总模型数: {selectedTask.result.total_models || selectedTask.result.summary?.total_models || 0}
                        </Tag>
                        <Tag color="green">
                          成功: {selectedTask.result.successful_models || selectedTask.result.summary?.successful_models || 0}
                        </Tag>
                        {(selectedTask.result.failed_models > 0 || selectedTask.result.summary?.failed_models > 0) && (
                          <Tag color="red">
                            失败: {selectedTask.result.failed_models || selectedTask.result.summary?.failed_models || 0}
                          </Tag>
                        )}
                        <Tag color="cyan">
                          成功率: {Math.round(((selectedTask.result.successful_models || selectedTask.result.summary?.successful_models || 0) / (selectedTask.result.total_models || selectedTask.result.summary?.total_models || 1)) * 100)}%
                        </Tag>
                      </>
                    ) : (
                      /* 单模型预测结果显示 */
                      <>
                        {selectedTask.result.suggested_threshold !== undefined && (
                          <Tag color="purple">
                            建议阈值 (pps): {selectedTask.result.suggested_threshold}
                          </Tag>
                        )}
                        {selectedTask.result.anomaly_count !== undefined && (
                          <Tag color="red">异常数量: {selectedTask.result.anomaly_count}</Tag>
                        )}
                        {selectedTask.result.predictions?.length && (
                          <Tag color="blue">预测点数: {selectedTask.result.predictions.length}</Tag>
                        )}
                      </>
                    )}

                    {selectedTask.result.duration_seconds && (
                      <Tag color="orange">
                        耗时: {Math.round(selectedTask.result.duration_seconds)}秒
                      </Tag>
                    )}
                  </Space>
                  <div style={{ marginTop: 8 }}>
                    <Text type="secondary">
                      💡 预测结果详情请前往"模型预测"页面查看
                    </Text>
                  </div>
                </Col>
              )}
              {selectedTask.result && selectedTask.task_type === 'data_cleaning' && (
                <Col span={24}>
                  <Text strong>分析完成:</Text>
                  <br />
                  <Space wrap style={{ marginTop: 8 }}>
                    <Tag color="green" icon={<CheckCircleOutlined />}>
                      流量分析已完成
                    </Tag>
                    {selectedTask.result.total_tasks && (
                      <Tag color="blue">
                        处理任务数: {selectedTask.result.total_tasks}
                      </Tag>
                    )}
                    {selectedTask.result.successful_tasks !== undefined && (
                      <Tag color="green">
                        成功: {selectedTask.result.successful_tasks}
                      </Tag>
                    )}
                    {selectedTask.result.failed_tasks !== undefined && selectedTask.result.failed_tasks > 0 && (
                      <Tag color="red">
                        失败: {selectedTask.result.failed_tasks}
                      </Tag>
                    )}
                    {selectedTask.result.duration_seconds && (
                      <Tag color="orange">
                        耗时: {Math.round(selectedTask.result.duration_seconds)}秒
                      </Tag>
                    )}
                  </Space>
                  <div style={{ marginTop: 8 }}>
                    <Text type="secondary">
                      💡 分析结果详情请前往"流量分析"页面查看
                    </Text>
                  </div>
                </Col>
              )}
            </Row>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default TaskManagerPage;

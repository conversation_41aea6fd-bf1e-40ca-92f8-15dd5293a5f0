import React, { useState, useEffect, useCallback } from 'react';
import {
  Typography,
  Card,
  Input,
  Select,
  Button,
  message,
  Spin,
  Empty,
  Space,
  List,
  Tag,
  Tabs,
} from 'antd';
import {
  FolderOpenOutlined,
  ReloadOutlined,
  DownloadOutlined,
  FileTextOutlined,
  EyeOutlined,
  SearchOutlined
} from '@ant-design/icons';
import { dataQueryAPI } from '../services/api';
import TextArea from 'antd/es/input/TextArea';

const { Title, Text } = Typography;
const { Option } = Select;
const { TabPane } = Tabs;



const DataQueryPage: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [csvLoading, setCsvLoading] = useState(false);
  const [resultLoading, setResultLoading] = useState(false);

  // CSV文件查询相关状态
  const [csvDir, setCsvDir] = useState('');
  const [csvFiles, setCsvFiles] = useState<string[]>([]);
  const [selectedCsv, setSelectedCsv] = useState<string>('');

  // 结果文件查询相关状态
  const [resultDir, setResultDir] = useState('');
  const [resultFiles, setResultFiles] = useState<string[]>([]);
  const [selectedResult, setSelectedResult] = useState<string>('');
  const [resultContent, setResultContent] = useState<string>('');
  const [contentVisible, setContentVisible] = useState(false);

  // 获取CSV文件列表
  const fetchCsvFiles = useCallback(async () => {
    setCsvLoading(true);
    try {
      const response = await dataQueryAPI.listCsvFiles(csvDir);
      if (response.data.csv_files) {
        setCsvFiles(response.data.csv_files);
        setSelectedCsv(''); // 重置选择
        if (response.data.csv_files.length === 0) {
          message.info('📁 该目录下暂无CSV文件');
        } else {
          message.success(`📊 找到 ${response.data.csv_files.length} 个CSV文件`);
        }
      }
    } catch (error: any) {
      console.error('获取CSV文件列表失败:', error);
      message.error(`❌ 获取CSV文件列表失败: ${error.response?.data?.detail || error.message}`);
      setCsvFiles([]);
    } finally {
      setCsvLoading(false);
    }
  }, [csvDir]);

  // 获取结果文件列表
  const fetchResultFiles = useCallback(async () => {
    setResultLoading(true);
    try {
      const response = await dataQueryAPI.listResultFiles(resultDir);
      if (response.data.result_files) {
        setResultFiles(response.data.result_files);
        setSelectedResult(''); // 重置选择
        setResultContent(''); // 清空内容
        setContentVisible(false);
        if (response.data.result_files.length === 0) {
          message.info('📁 该目录下暂无结果文件');
        } else {
          message.success(`📊 找到 ${response.data.result_files.length} 个结果文件`);
        }
      }
    } catch (error: any) {
      console.error('获取结果文件列表失败:', error);
      message.error(`❌ 获取结果文件列表失败: ${error.response?.data?.detail || error.message}`);
      setResultFiles([]);
    } finally {
      setResultLoading(false);
    }
  }, [resultDir]);

  // 下载CSV文件
  const downloadCsv = async (fileName: string) => {
    try {
      setLoading(true);
      const response = await dataQueryAPI.downloadCsv(csvDir, fileName);

      // 创建下载链接
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', fileName);
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);

      message.success(`✅ ${fileName} 下载成功`);
    } catch (error: any) {
      console.error('下载CSV文件失败:', error);
      message.error(`❌ 下载失败: ${error.response?.data?.detail || error.message}`);
    } finally {
      setLoading(false);
    }
  };

  // 获取结果文件内容
  const getResultContent = async (fileName: string) => {
    try {
      setLoading(true);
      const response = await dataQueryAPI.getResultContent(resultDir, fileName);
      if (response.data.content) {
        setResultContent(response.data.content);
        setContentVisible(true);
        message.success('✅ 文件内容加载成功');
      }
    } catch (error: any) {
      console.error('获取文件内容失败:', error);
      message.error(`❌ 获取文件内容失败: ${error.response?.data?.detail || error.message}`);
    } finally {
      setLoading(false);
    }
  };

  // 下载结果文件
  const downloadResult = async (fileName: string) => {
    try {
      setLoading(true);
      const response = await dataQueryAPI.getResultContent(resultDir, fileName);

      // 创建下载链接
      const blob = new Blob([response.data.content], { type: 'text/plain' });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', fileName);
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);

      message.success(`✅ ${fileName} 下载成功`);
    } catch (error: any) {
      console.error('下载结果文件失败:', error);
      message.error(`❌ 下载失败: ${error.response?.data?.detail || error.message}`);
    } finally {
      setLoading(false);
    }
  };



  // 使用防抖来避免频繁请求 - 只在用户停止输入1.5秒后才发起请求
  useEffect(() => {
    if (csvDir && csvDir.length > 3) { // 至少输入4个字符才开始请求
      const timer = setTimeout(() => {
        fetchCsvFiles();
      }, 1500); // 1.5秒延迟，给用户足够时间输入完整路径

      return () => clearTimeout(timer);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [csvDir]);

  useEffect(() => {
    if (resultDir && resultDir.length > 3) { // 至少输入4个字符才开始请求
      const timer = setTimeout(() => {
        fetchResultFiles();
      }, 1500); // 1.5秒延迟，给用户足够时间输入完整路径

      return () => clearTimeout(timer);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [resultDir]);

  return (
    <div>
      <Title level={3} style={{ fontSize: '20px', fontWeight: 600, marginBottom: '8px' }}>数据查询</Title>
      <Text type="secondary">
        查询流量分析模块生成的CSV文件和流量检测模型预测的特征值。
      </Text>

      <Tabs defaultActiveKey="1" style={{ marginTop: 24 }}>
        <TabPane tab={<span><FileTextOutlined />CSV文件查询</span>} key="1">
          <Card title="清洗出的 CSV 文件查询" size="small">
            <div style={{ marginBottom: 24 }}>
              <Text strong>CSV文件目录：</Text>
              <Input
                value={csvDir}
                onChange={(e) => setCsvDir(e.target.value)}
                placeholder="例如: /data/output"
                prefix={<FolderOpenOutlined />}
                style={{ marginTop: 8 }}
                addonAfter={
                  <Button
                    size="small"
                    icon={<ReloadOutlined />}
                    onClick={fetchCsvFiles}
                    loading={csvLoading}
                  >
                    刷新
                  </Button>
                }
              />
            </div>

            {csvLoading ? (
              <div style={{ textAlign: 'center', padding: '20px' }}>
                <Spin size="large" />
                <div style={{ marginTop: 8 }}>
                  <Text type="secondary">正在加载CSV文件列表...</Text>
                </div>
              </div>
            ) : csvFiles.length === 0 ? (
              <Empty
                description="请先输入CSV文件目录路径并点击刷新按钮获取文件列表"
                image={Empty.PRESENTED_IMAGE_SIMPLE}
              />
            ) : (
              <div>
                <div style={{ marginBottom: 24 }}>
                  <Text strong>选择CSV文件：</Text>
                  <Select
                    style={{ width: '100%', marginTop: 8 }}
                    placeholder="选择要下载的CSV文件"
                    value={selectedCsv}
                    onChange={setSelectedCsv}
                    showSearch
                    filterOption={(input, option) =>
                      (option?.children as unknown as string)?.toLowerCase().includes(input.toLowerCase())
                    }
                  >
                    {csvFiles.map(file => (
                      <Option key={file} value={file}>
                        <Space>
                          <FileTextOutlined />
                          {file}
                        </Space>
                      </Option>
                    ))}
                  </Select>

                  {selectedCsv && (
                    <div style={{ marginTop: 16, textAlign: 'center' }}>
                      <Button
                        type="primary"
                        icon={<DownloadOutlined />}
                        onClick={() => downloadCsv(selectedCsv)}
                        loading={loading}
                        size="large"
                      >
                        下载 {selectedCsv}
                      </Button>
                    </div>
                  )}
                </div>

                <div>
                  <Text strong>文件列表：</Text>
                  <List
                    size="small"
                    style={{ marginTop: 8, maxHeight: 200, overflow: 'auto' }}
                    dataSource={csvFiles}
                    renderItem={(file) => (
                      <List.Item
                        actions={[
                          <Button
                            key="download"
                            type="link"
                            size="small"
                            icon={<DownloadOutlined />}
                            onClick={() => downloadCsv(file)}
                            loading={loading}
                          >
                            下载
                          </Button>
                        ]}
                      >
                        <List.Item.Meta
                          avatar={<FileTextOutlined style={{ color: '#1890ff' }} />}
                          title={file}
                          description={
                            <Tag color="blue" size="small">CSV文件</Tag>
                          }
                        />
                      </List.Item>
                    )}
                  />
                </div>
              </div>
            )}
          </Card>
        </TabPane>

        <TabPane tab={<span><SearchOutlined />特征预测值查询</span>} key="2">
          <Card title="特征预测值查询" size="small">
            <div style={{ marginBottom: 24 }}>
              <Text strong>结果文件目录：</Text>
              <Input
                value={resultDir}
                onChange={(e) => setResultDir(e.target.value)}
                placeholder="例如: /data/output"
                prefix={<FolderOpenOutlined />}
                style={{ marginTop: 8 }}
                addonAfter={
                  <Button
                    size="small"
                    icon={<ReloadOutlined />}
                    onClick={fetchResultFiles}
                    loading={resultLoading}
                  >
                    刷新
                  </Button>
                }
              />
            </div>

            {resultLoading ? (
              <div style={{ textAlign: 'center', padding: '20px' }}>
                <Spin size="large" />
                <div style={{ marginTop: 8 }}>
                  <Text type="secondary">正在加载结果文件列表...</Text>
                </div>
              </div>
            ) : resultFiles.length === 0 ? (
              <Empty
                description="请先输入结果文件目录路径并点击刷新按钮获取文件列表"
                image={Empty.PRESENTED_IMAGE_SIMPLE}
              />
            ) : (
              <div>
                <div style={{ marginBottom: 24 }}>
                  <Text strong>选择结果文件：</Text>
                  <Select
                    style={{ width: '100%', marginTop: 8 }}
                    placeholder="选择要查看的结果文件"
                    value={selectedResult}
                    onChange={setSelectedResult}
                    showSearch
                    filterOption={(input, option) =>
                      (option?.children as unknown as string)?.toLowerCase().includes(input.toLowerCase())
                    }
                  >
                    {resultFiles.map(file => (
                      <Option key={file} value={file}>
                        <Space>
                          <FileTextOutlined />
                          {file}
                        </Space>
                      </Option>
                    ))}
                  </Select>

                  {selectedResult && (
                    <div style={{ marginTop: 16 }}>
                      <Space>
                        <Button
                          type="primary"
                          icon={<EyeOutlined />}
                          onClick={() => getResultContent(selectedResult)}
                          loading={loading}
                        >
                          查看内容
                        </Button>
                        <Button
                          icon={<DownloadOutlined />}
                          onClick={() => downloadResult(selectedResult)}
                          loading={loading}
                        >
                          下载文件
                        </Button>
                      </Space>
                    </div>
                  )}
                </div>

                <div>
                  <Text strong>文件列表：</Text>
                  <List
                    size="small"
                    style={{ marginTop: 8, maxHeight: 200, overflow: 'auto' }}
                    dataSource={resultFiles}
                    renderItem={(file) => (
                      <List.Item
                        actions={[
                          <Button
                            key="view"
                            type="link"
                            size="small"
                            icon={<EyeOutlined />}
                            onClick={() => getResultContent(file)}
                            loading={loading}
                          >
                            查看
                          </Button>,
                          <Button
                            key="download"
                            type="link"
                            size="small"
                            icon={<DownloadOutlined />}
                            onClick={() => downloadResult(file)}
                            loading={loading}
                          >
                            下载
                          </Button>
                        ]}
                      >
                        <List.Item.Meta
                          avatar={<FileTextOutlined style={{ color: '#52c41a' }} />}
                          title={file}
                          description={
                            <Tag color="green" size="small">结果文件</Tag>
                          }
                        />
                      </List.Item>
                    )}
                  />
                </div>
              </div>
            )}
          </Card>
        </TabPane>
      </Tabs>

        {contentVisible && resultContent && (
          <TabPane tab={<span><EyeOutlined />文件内容</span>} key="3">
            <Card
              title={`${selectedResult} 内容`}
              size="small"
              extra={
                <Button
                  size="small"
                  onClick={() => setContentVisible(false)}
                >
                  关闭
                </Button>
              }
            >
              <TextArea
                value={resultContent}
                rows={15}
                readOnly
                style={{
                  fontFamily: 'monospace',
                  fontSize: '12px',
                  backgroundColor: '#f5f5f5'
                }}
              />
            </Card>
          </TabPane>
        )}
    </div>
  );
};

export default DataQueryPage;

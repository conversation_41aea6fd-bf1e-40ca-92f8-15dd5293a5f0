import React, { useState, useEffect } from 'react';
import {
  Card,
  Radio,
  Upload,
  Input,
  Select,
  Button,
  Alert,
  Progress,
  Typography,
  Space,
  Divider,
  message,
  Spin,
  Row,
  Col,
} from 'antd';
import { InboxOutlined, PlayCircleOutlined, DeleteOutlined, PlusOutlined } from '@ant-design/icons';
import { dataCleaningAPI } from '../services/api';

const { Title, Text } = Typography;
const { Dragger } = Upload;
const { Option } = Select;

const DataCleaningPage: React.FC = () => {
  const [dataSource, setDataSource] = useState<'upload' | 'local'>('local');
  const [processingMode, setProcessingMode] = useState<'single' | 'batch'>('single');
  const [uploadedFiles, setUploadedFiles] = useState<any[]>([]);
  const [folderPath, setFolderPath] = useState('');
  const [availableFiles, setAvailableFiles] = useState<string[]>([]);
  const [selectedFiles, setSelectedFiles] = useState<string[]>([]);
  const [outputDir, setOutputDir] = useState('');
  const [loading, setLoading] = useState(false);
  const [filesLoading, setFilesLoading] = useState(false);
  const [progress, setProgress] = useState(0);
  const [result, setResult] = useState<any>(null);

  // 批量任务相关状态
  const [batchTasks, setBatchTasks] = useState<Array<{
    id: string;
    inputDir: string;
    outputDir: string;
    fileCount?: number;
  }>>([]);
  const [batchLoading, setBatchLoading] = useState(false);
  const [batchProgress, setBatchProgress] = useState<{[key: string]: number}>({});

  // 批量任务管理函数
  const addBatchTask = () => {
    const newTask = {
      id: Date.now().toString(),
      inputDir: '',
      outputDir: '',
      fileCount: 0
    };
    setBatchTasks([...batchTasks, newTask]);
  };

  const updateBatchTask = (id: string, field: string, value: string) => {
    setBatchTasks(batchTasks.map(task =>
      task.id === id ? { ...task, [field]: value } : task
    ));
  };

  const removeBatchTask = (id: string) => {
    setBatchTasks(batchTasks.filter(task => task.id !== id));
  };

  const validateBatchTask = async (task: any) => {
    try {
      const response = await dataCleaningAPI.listFiles(task.inputDir);
      const files = response.data.files || [];
      const txtFiles = files.filter((file: string) =>
        file.toLowerCase().endsWith('.txt')
      );

      updateBatchTask(task.id, 'fileCount', txtFiles.length.toString());
      return txtFiles.length > 0;
    } catch (error) {
      message.error(`验证目录 ${task.inputDir} 失败`);
      return false;
    }
  };

  const startBatchAnalysis = async () => {
    // 验证所有任务
    const validTasks = [];
    for (let i = 0; i < batchTasks.length; i++) {
      const task = batchTasks[i];
      const taskName = `任务${i + 1}`;

      if (!task.inputDir || !task.outputDir) {
        message.error(`请完善 "${taskName}" 的配置`);
        return;
      }

      const isValid = await validateBatchTask(task);
      if (isValid) {
        validTasks.push({...task, taskName});
      }
    }

    if (validTasks.length === 0) {
      message.error('没有有效的批量任务');
      return;
    }

    setBatchLoading(true);

    try {
      // 调用批量分析API
      const response = await dataCleaningAPI.batchAnalyze({
        tasks: validTasks.map((task, index) => ({
          customer: `任务${index + 1}`,
          input_dir: task.inputDir,
          output_dir: task.outputDir
        }))
      });

      if (response.data.success) {
        const batchId = response.data.batch_id;
        message.success(`批量任务已启动，任务ID: ${batchId}`);

        // 开始监控批量任务进度
        monitorBatchProgress(batchId);
      } else {
        message.error('批量任务启动失败');
      }
    } catch (error: any) {
      message.error(error.response?.data?.detail || '批量分析失败');
    } finally {
      setBatchLoading(false);
    }
  };

  const monitorBatchProgress = async (batchId: string) => {
    const maxAttempts = 60; // 10分钟监控
    let attempt = 0;

    const checkProgress = async () => {
      try {
        const response = await dataCleaningAPI.getBatchStatus(batchId);

        console.log('批量任务状态响应:', response.data); // 添加调试日志

        if (response.data && response.data.success) {
          const { status, progress, error } = response.data;

          console.log(`批量任务状态: ${status}, 进度: ${progress}%`); // 添加调试日志

          // 更新进度状态
          setBatchProgress(prev => ({
            ...prev,
            [batchId]: progress || 0
          }));

          if (status === 'completed') {
            message.success('批量分析完成！');
            setBatchLoading(false);
            console.log('批量任务成功完成');
            return;
          } else if (status === 'failed') {
            const errorMsg = error || '未知错误';
            message.error(`批量分析失败: ${errorMsg}`);
            setBatchLoading(false);
            console.error('批量任务失败:', errorMsg);
            return;
          }

          // 如果任务还在进行中，继续监控
          if (attempt < maxAttempts && (status === 'running' || status === 'pending' || !status)) {
            attempt++;
            setTimeout(checkProgress, 5000); // 5秒后再次检查
          } else if (attempt >= maxAttempts) {
            // 超时时不显示失败，而是提示用户手动检查
            message.warning('批量任务监控超时，任务可能仍在后台运行，请稍后手动检查结果');
            setBatchLoading(false);
            console.warn('批量任务监控超时');
          }
        } else {
          // API响应格式问题，但不立即判定为失败
          console.warn('批量任务状态API响应格式异常:', response.data);
          if (attempt < maxAttempts) {
            attempt++;
            setTimeout(checkProgress, 5000);
          } else {
            message.warning('无法获取批量任务状态，请手动检查任务结果');
            setBatchLoading(false);
          }
        }
      } catch (error: any) {
        console.error('监控批量任务进度失败:', error);

        // 网络错误或API错误，增加重试次数
        if (attempt < 10) { // 增加重试次数到10次
          attempt++;
          console.log(`批量任务监控重试 ${attempt}/10`);
          setTimeout(checkProgress, 5000);
        } else {
          // 多次重试后仍失败，提示用户但不判定任务失败
          message.warning('无法监控批量任务进度，任务可能仍在后台运行，请稍后手动检查结果');
          setBatchLoading(false);
          console.error('批量任务监控最终失败');
        }
      }
    };

    // 开始监控
    checkProgress();
  };

  // 获取本地文件列表
  const fetchLocalFiles = async () => {
    if (!folderPath) return;

    setFilesLoading(true);
    try {
      const response = await dataCleaningAPI.listFiles(folderPath);
      const files = response.data.files || [];
      setAvailableFiles(files);

      // 自动选择所有txt文件
      const txtFiles = files.filter((file: string) =>
        file.toLowerCase().endsWith('.txt')
      );

      if (txtFiles.length > 0) {
        setSelectedFiles(txtFiles);
        message.success(`已自动选择 ${txtFiles.length} 个TXT文件`);
      } else {
        setSelectedFiles([]);
        if (files.length > 0) {
          message.warning('目录中没有找到TXT文件');
        }
      }
    } catch (error: any) {
      message.error(error.response?.data?.detail || '获取文件列表失败');
      setAvailableFiles([]);
      setSelectedFiles([]);
    } finally {
      setFilesLoading(false);
    }
  };

  // 使用防抖来避免频繁请求 - 只在用户停止输入1.5秒后才发起请求
  useEffect(() => {
    if (dataSource === 'local' && folderPath && folderPath.length > 3) { // 至少输入4个字符才开始请求
      const timer = setTimeout(() => {
        fetchLocalFiles();
      }, 1500); // 1.5秒延迟，给用户足够时间输入完整路径

      return () => clearTimeout(timer);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [dataSource, folderPath]);

  // 文件上传配置
  const uploadProps = {
    name: 'files',
    multiple: true,
    accept: '.txt',
    beforeUpload: () => false, // 阻止自动上传
    onChange: (info: any) => {
      setUploadedFiles(info.fileList);
    },
    onDrop: (e: any) => {
      console.log('Dropped files', e.dataTransfer.files);
    },
  };

  // 执行数据清洗
  const handleCleanData = async () => {
    // 验证输入
    if (dataSource === 'upload' && uploadedFiles.length === 0) {
      message.error('请上传至少一个文件');
      return;
    }
    
    if (dataSource === 'local' && (!folderPath || selectedFiles.length === 0)) {
      message.error('请提供文件夹路径并选择至少一个文件');
      return;
    }

    setLoading(true);
    setProgress(0);
    setResult(null);

    try {
      let response;
      
      if (dataSource === 'upload') {
        const formData = new FormData();
        uploadedFiles.forEach((file) => {
          formData.append('files', file.originFileObj);
        });
        formData.append('output_dir', outputDir);
        
        // 模拟进度更新
        const progressInterval = setInterval(() => {
          setProgress((prev) => {
            if (prev >= 90) {
              clearInterval(progressInterval);
              return prev;
            }
            return prev + 10;
          });
        }, 500);

        response = await dataCleaningAPI.cleanData(formData);
        clearInterval(progressInterval);
      } else {
        // 本地文件处理
        const progressInterval = setInterval(() => {
          setProgress((prev) => {
            if (prev >= 90) {
              clearInterval(progressInterval);
              return prev;
            }
            return prev + 10;
          });
        }, 500);

        response = await dataCleaningAPI.cleanDataLocal({
          folder_path: folderPath,
          selected_files: selectedFiles,
          output_dir: outputDir,
        });
        clearInterval(progressInterval);
      }
      
      setProgress(100);
      setResult(response.data);
      message.success('数据清洗完成！');
      
    } catch (error: any) {
      message.error(error.response?.data?.detail || '数据清洗失败');
    } finally {
      setLoading(false);
    }
  };

  const isFormValid = () => {
    if (dataSource === 'upload') {
      return uploadedFiles.length > 0;
    } else {
      return folderPath && selectedFiles.length > 0;
    }
  };

  return (
    <div>
      <Title level={3} style={{ fontSize: '20px', fontWeight: 600, marginBottom: '8px' }}>流量数据分析</Title>
      <Text type="secondary">
        上传流量数据文件或选择本地目录中的流量数据文件，分析后生成CSV文件并保存到指定目录。
      </Text>

      <Divider />

      <Card title="数据分析" className="function-card">
        <Space direction="vertical" size="large" style={{ width: '100%' }}>
          {/* 处理模式选择 */}
          <div>
            <Text strong>处理模式：</Text>
            <Radio.Group
              value={processingMode}
              onChange={(e) => setProcessingMode(e.target.value)}
              style={{ marginTop: 8 }}
            >
              <Radio value="single">单个目录分析</Radio>
              <Radio value="batch">批量目录分析</Radio>
            </Radio.Group>
          </div>

          {/* 单个目录模式 */}
          {processingMode === 'single' && (
            <>
              {/* 数据源选择 */}
          <div>
            <Text strong>选择流量数据源：</Text>
            <Radio.Group
              value={dataSource}
              onChange={(e) => setDataSource(e.target.value)}
              style={{ marginTop: 8 }}
            >
              <Radio value="local">选择本地目录文件</Radio>
              <Radio value="upload">上传流量数据TXT文件</Radio>
            </Radio.Group>
          </div>

          {/* 本地文件选择 */}
          {dataSource === 'local' && (
            <Space direction="vertical" style={{ width: '100%' }}>
              <div>
                <Text strong>本地目录路径：</Text>
                <Input.Group compact style={{ marginTop: 8, display: 'flex' }}>
                  <Input
                    value={folderPath}
                    onChange={(e) => setFolderPath(e.target.value)}
                    placeholder="例如: /data/aizhinengqingxicepingdaliu"
                    style={{ flex: 1 }}
                  />
                  <Button
                    type="primary"
                    onClick={fetchLocalFiles}
                    loading={filesLoading}
                    disabled={!folderPath}
                    style={{ marginLeft: 8 }}
                  >
                    刷新
                  </Button>
                </Input.Group>
              </div>

              <div>
                <Text strong>选择文件：</Text>
                <Spin spinning={filesLoading}>
                  <Select
                    mode="multiple"
                    value={selectedFiles}
                    onChange={setSelectedFiles}
                    placeholder="请选择TXT文件"
                    style={{ width: '100%', marginTop: 8 }}
                    loading={filesLoading}
                  >
                    {availableFiles.map((file) => (
                      <Option key={file} value={file}>
                        {file}
                      </Option>
                    ))}
                  </Select>
                </Spin>
              </div>
            </Space>
          )}

          {/* 文件上传 */}
          {dataSource === 'upload' && (
            <div>
              <Text strong>上传文件：</Text>
              <Dragger {...uploadProps} style={{ marginTop: 8 }}>
                <p className="ant-upload-drag-icon">
                  <InboxOutlined />
                </p>
                <p className="ant-upload-text">点击或拖拽文件到此区域上传</p>
                <p className="ant-upload-hint">
                  支持单个或批量上传TXT格式的流量数据文件
                </p>
              </Dragger>
            </div>
          )}

          {/* 输出目录 */}
          <div>
            <Text strong>CSV输出目录：</Text>
            <Input
              value={outputDir}
              onChange={(e) => setOutputDir(e.target.value)}
              placeholder="例如: /data/output"
              style={{ marginTop: 8 }}
            />
          </div>

          {/* 执行按钮 */}
          <Button
            type="primary"
            size="large"
            icon={<PlayCircleOutlined />}
            onClick={handleCleanData}
            loading={loading}
            disabled={!isFormValid()}
            className="action-button"
          >
            {loading ? '正在处理...' : '执行流量分析'}
          </Button>

          {/* 进度条 */}
          {loading && (
            <div className="progress-section">
              <Text>处理进度：</Text>
              <Progress percent={progress} status="active" />
            </div>
          )}

              {/* 结果展示 */}
              {result && (
                <Alert
                  message="处理完成"
                  description={
                    <div>
                      <p>处理结果：{result.message}</p>
                      {result.output_file && <p>输出文件：{result.output_file}</p>}
                      {result.processed_files && <p>处理的文件数：{result.processed_files}</p>}
                      {result.total_rows && <p>总行数：{result.total_rows}</p>}
                    </div>
                  }
                  type="success"
                  showIcon
                />
              )}
            </>
          )}

          {/* 批量目录模式 */}
          {processingMode === 'batch' && (
            <>
              <div>
                <Text strong>批量任务配置：</Text>
                <div style={{ marginTop: 16 }}>
                  {batchTasks.map((task, index) => (
                    <Card
                      key={task.id}
                      size="small"
                      style={{ marginBottom: 16 }}
                      title={`任务 ${index + 1}`}
                      extra={
                        <Button
                          type="text"
                          danger
                          icon={<DeleteOutlined />}
                          onClick={() => removeBatchTask(task.id)}
                        />
                      }
                    >
                      <Row gutter={16}>
                        <Col span={12}>
                          <Text strong>输入目录：</Text>
                          <Input
                            value={task.inputDir}
                            onChange={(e) => updateBatchTask(task.id, 'inputDir', e.target.value)}
                            placeholder="例如：/data/input"
                            style={{ marginTop: 4 }}
                          />
                        </Col>
                        <Col span={12}>
                          <Text strong>输出目录：</Text>
                          <Input
                            value={task.outputDir}
                            onChange={(e) => updateBatchTask(task.id, 'outputDir', e.target.value)}
                            placeholder="例如：/data/output"
                            style={{ marginTop: 4 }}
                          />
                        </Col>
                      </Row>
                      {task.fileCount !== undefined && (
                        <div style={{ marginTop: 8 }}>
                          <Text type="secondary">
                            检测到 {task.fileCount} 个TXT文件
                          </Text>
                        </div>
                      )}
                    </Card>
                  ))}

                  <Button
                    type="dashed"
                    icon={<PlusOutlined />}
                    onClick={addBatchTask}
                    style={{ width: '100%', marginBottom: 16 }}
                  >
                    添加任务
                  </Button>

                  <Button
                    type="primary"
                    size="large"
                    loading={batchLoading}
                    disabled={batchTasks.length === 0}
                    onClick={startBatchAnalysis}
                    style={{ width: '100%' }}
                  >
                    {batchLoading ? '批量分析中...' : '开始批量分析'}
                  </Button>

                  {/* 批量任务进度显示 */}
                  {batchLoading && Object.keys(batchProgress).length > 0 && (
                    <div style={{ marginTop: 16 }}>
                      <Text strong>批量任务进度：</Text>
                      {Object.entries(batchProgress).map(([taskId, progress]) => (
                        <div key={taskId} style={{ marginTop: 8 }}>
                          <Progress
                            percent={progress}
                            status={progress === 100 ? 'success' : 'active'}
                            format={(percent) => `${percent}%`}
                          />
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            </>
          )}
        </Space>
      </Card>
    </div>
  );
};

export default DataCleaningPage;

{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/frontend-react-stable/src/pages/ModelPredictionPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback } from 'react';\nimport { Card, Radio, Upload, Input, Select, Button, Typography, Space, Divider, message, Spin, Alert, Statistic, Row, Col, Progress, Checkbox } from 'antd';\nimport { InboxOutlined, PlayCircleOutlined } from '@ant-design/icons';\nimport { modelPredictionAPI } from '../services/api';\nimport useTaskManager from '../hooks/useTaskManager';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text\n} = Typography;\nconst {\n  Dragger\n} = Upload;\nconst {\n  Option\n} = Select;\n\n// 预测结果展示组件\nconst PredictionResultDisplay = ({\n  result\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(Row, {\n      gutter: 16,\n      style: {\n        marginBottom: 16\n      },\n      children: /*#__PURE__*/_jsxDEV(Col, {\n        span: 24,\n        children: [/*#__PURE__*/_jsxDEV(Statistic, {\n          title: \"\\u5EFA\\u8BAE\\u7684\\u6D41\\u91CF\\u6E05\\u6D17\\u9608\\u503C (pps)\",\n          value: result.suggested_threshold,\n          precision: 2,\n          valueStyle: {\n            color: '#1890ff'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 38,\n          columnNumber: 11\n        }, this), result.suggested_threshold && /*#__PURE__*/_jsxDEV(Alert, {\n          message: \"\\u2705 \\u6B64\\u9608\\u503C\\u5DF2\\u81EA\\u52A8\\u4FDD\\u5B58\\u5230\\u4EE5\\u8F93\\u5165CSV\\u6587\\u4EF6\\u547D\\u540D\\u7684\\u7ED3\\u679C\\u6587\\u4EF6\\u4E2D\\uFF0C\\u53EF\\u7528\\u4E8E\\u751F\\u6210\\u6E05\\u6D17\\u6A21\\u677F\\u3002\",\n          type: \"success\",\n          showIcon: true,\n          style: {\n            marginTop: 8\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 45,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 37,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 36,\n      columnNumber: 7\n    }, this), (result.duration_seconds !== undefined || result.cpu_percent !== undefined || result.memory_mb !== undefined || result.gpu_memory_mb !== undefined || result.gpu_utilization_percent !== undefined) && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: 24\n      },\n      children: [/*#__PURE__*/_jsxDEV(Title, {\n        level: 5,\n        children: \"\\u8D44\\u6E90\\u4F7F\\u7528\\u60C5\\u51B5\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 61,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Row, {\n        gutter: 16,\n        children: [result.duration_seconds !== undefined && /*#__PURE__*/_jsxDEV(Col, {\n          flex: \"auto\",\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u9884\\u6D4B\\u8017\\u65F6\",\n            value: result.duration_seconds,\n            precision: 2,\n            suffix: \"\\u79D2\",\n            valueStyle: {\n              color: '#1890ff'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 65,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 64,\n          columnNumber: 15\n        }, this), result.cpu_percent !== undefined && /*#__PURE__*/_jsxDEV(Col, {\n          flex: \"auto\",\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"CPU\\u4F7F\\u7528\\u7387\",\n            value: result.cpu_percent,\n            precision: 1,\n            suffix: \"%\",\n            valueStyle: {\n              color: '#52c41a'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 76,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 75,\n          columnNumber: 15\n        }, this), result.memory_mb !== undefined && /*#__PURE__*/_jsxDEV(Col, {\n          flex: \"auto\",\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u5185\\u5B58\\u4F7F\\u7528\",\n            value: result.memory_mb,\n            precision: 1,\n            suffix: \"MB\",\n            valueStyle: {\n              color: '#fa8c16'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 87,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 86,\n          columnNumber: 15\n        }, this), result.gpu_memory_mb !== undefined && /*#__PURE__*/_jsxDEV(Col, {\n          flex: \"auto\",\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"GPU\\u5185\\u5B58\",\n            value: result.gpu_memory_mb,\n            precision: 1,\n            suffix: \"MB\",\n            valueStyle: {\n              color: '#722ed1'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 98,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 97,\n          columnNumber: 15\n        }, this), result.gpu_utilization_percent !== undefined && /*#__PURE__*/_jsxDEV(Col, {\n          flex: \"auto\",\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"GPU\\u5229\\u7528\\u7387\",\n            value: result.gpu_utilization_percent,\n            precision: 1,\n            suffix: \"%\",\n            valueStyle: {\n              color: '#eb2f96'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 109,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 108,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 62,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 60,\n      columnNumber: 9\n    }, this), result.template_info && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: 24\n      },\n      children: [/*#__PURE__*/_jsxDEV(Title, {\n        level: 5,\n        children: \"\\u6E05\\u6D17\\u6A21\\u677F\\u751F\\u6210\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 125,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: 12,\n          backgroundColor: result.template_info.template_generated ? '#f6ffed' : '#fff2f0',\n          border: `1px solid ${result.template_info.template_generated ? '#b7eb8f' : '#ffccc7'}`,\n          borderRadius: 6\n        },\n        children: result.template_info.template_generated ? /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              color: '#52c41a',\n              fontWeight: 'bold',\n              margin: 0\n            },\n            children: \"\\u2705 \\u6E05\\u6D17\\u6A21\\u677F\\u5DF2\\u81EA\\u52A8\\u751F\\u6210\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 129,\n            columnNumber: 17\n          }, this), result.template_info.template_path && /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              margin: '8px 0 0 0'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\u6A21\\u677F\\u8DEF\\u5F84:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 131,\n              columnNumber: 54\n            }, this), \" \", result.template_info.template_path]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 131,\n            columnNumber: 19\n          }, this), result.template_info.updated_thresholds !== undefined && /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              margin: '4px 0 0 0'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\u66F4\\u65B0\\u9608\\u503C\\u6570\\u91CF:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 134,\n              columnNumber: 54\n            }, this), \" \", result.template_info.updated_thresholds]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 134,\n            columnNumber: 19\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 128,\n          columnNumber: 15\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              color: '#ff4d4f',\n              fontWeight: 'bold',\n              margin: 0\n            },\n            children: \"\\u274C \\u6E05\\u6D17\\u6A21\\u677F\\u751F\\u6210\\u5931\\u8D25\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 139,\n            columnNumber: 17\n          }, this), result.template_info.error && /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              margin: '8px 0 0 0'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\u9519\\u8BEF\\u4FE1\\u606F:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 141,\n              columnNumber: 54\n            }, this), \" \", result.template_info.error]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 141,\n            columnNumber: 19\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 138,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 126,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 124,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 35,\n    columnNumber: 5\n  }, this);\n};\n\n// 协议和数据类型配置\n_c = PredictionResultDisplay;\nconst protocolOptions = ['TCP', 'UDP', 'ICMP'];\nconst datatypeOptions = {\n  TCP: ['spt_sip_dip', 'dpt_sip_dip', 'len_dpt_syn', 'seq_ack_dip'],\n  UDP: ['spt_sip_dip', 'dpt_sip_dip'],\n  ICMP: ['dip']\n};\nconst ModelPredictionPage = () => {\n  _s();\n  const [dataSource, setDataSource] = useState('local');\n  const [uploadedFile, setUploadedFile] = useState(null);\n  const [csvDir, setCsvDir] = useState('');\n  const [availableCsvFiles, setAvailableCsvFiles] = useState([]);\n  const [selectedCsvFile, setSelectedCsvFile] = useState('');\n  const [csvFilesLoading, setCsvFilesLoading] = useState(false);\n\n  // 模型相关状态\n  const [modelDir, setModelDir] = useState('');\n  const [availablePthFiles, setAvailablePthFiles] = useState([]);\n  const [selectedModels, setSelectedModels] = useState([]);\n  const [modelsLoading, setModelsLoading] = useState(false);\n  const [predictionMode, setPredictionMode] = useState('single');\n\n  // 单模型选择状态\n  const [selectedModelFile, setSelectedModelFile] = useState('');\n  const [selectedParamsFile, setSelectedParamsFile] = useState('');\n  const [selectedScalerFile, setSelectedScalerFile] = useState('');\n  const [selectedProt, setSelectedProt] = useState('');\n  const [selectedDatatype, setSelectedDatatype] = useState('');\n  const [showManualSelection, setShowManualSelection] = useState(false);\n\n  // 预测状态\n  const [predicting, setPredicting] = useState(false);\n  const [progress, setProgress] = useState(0);\n  const [results, setResults] = useState([]);\n  const [selectedResultIndex, setSelectedResultIndex] = useState(0);\n  const [matchingFilesLoading, setMatchingFilesLoading] = useState(false);\n  const [autoGenerateTemplate, setAutoGenerateTemplate] = useState(false); // 新增：是否自动生成清洗模板\n\n  // 任务管理\n  const {\n    submitPredictionTask,\n    submitMultiPredictionTask,\n    getCompletedTasksByType,\n    fetchCompletedTasks\n  } = useTaskManager();\n  const [useAsyncPrediction, setUseAsyncPrediction] = useState(true); // 默认使用异步预测\n\n  // 异步任务结果状态\n  const [asyncPredictionResults, setAsyncPredictionResults] = useState([]);\n  const [selectedAsyncTaskId, setSelectedAsyncTaskId] = useState('');\n  const [asyncTemplateInfo, setAsyncTemplateInfo] = useState(null);\n  const [selectedAsyncResultIndex, setSelectedAsyncResultIndex] = useState(0);\n\n  // 获取已完成的预测任务\n  const completedPredictionTasks = getCompletedTasksByType('prediction');\n\n  // 处理异步任务选择\n  const handleAsyncTaskSelect = useCallback(taskId => {\n    setSelectedAsyncTaskId(taskId);\n    const selectedTask = completedPredictionTasks.find(task => task.task_id === taskId);\n    if (selectedTask && selectedTask.result) {\n      // 检查是否是多模型异步预测结果\n      if (selectedTask.result.is_multi_model && selectedTask.result.results) {\n        // 多模型异步预测结果 - 直接使用 results 数组\n        setAsyncPredictionResults(selectedTask.result.results);\n        setAsyncTemplateInfo(selectedTask.result.template_info);\n      } else {\n        // 单模型异步预测结果 - 转换为数组格式\n        const asyncResult = {\n          suggested_threshold: selectedTask.result.suggested_threshold || 0,\n          model_name: selectedTask.result.model_name || '未知模型',\n          message: selectedTask.result.message || '预测完成',\n          duration_seconds: selectedTask.result.duration_seconds,\n          cpu_percent: selectedTask.result.cpu_percent,\n          memory_mb: selectedTask.result.memory_mb,\n          gpu_memory_mb: selectedTask.result.gpu_memory_mb,\n          gpu_utilization_percent: selectedTask.result.gpu_utilization_percent,\n          template_info: selectedTask.result.template_info,\n          result_path: selectedTask.result.result_path\n        };\n        setAsyncPredictionResults([asyncResult]);\n        setAsyncTemplateInfo(selectedTask.result.template_info);\n      }\n    }\n  }, [completedPredictionTasks]);\n\n  // 页面加载时获取已完成任务\n  useEffect(() => {\n    fetchCompletedTasks();\n  }, [fetchCompletedTasks]);\n\n  // 自动选择最新的预测任务\n  useEffect(() => {\n    if (completedPredictionTasks.length > 0 && !selectedAsyncTaskId) {\n      const latestTask = completedPredictionTasks[completedPredictionTasks.length - 1];\n      handleAsyncTaskSelect(latestTask.task_id);\n    }\n  }, [completedPredictionTasks, selectedAsyncTaskId, handleAsyncTaskSelect]);\n\n  // 获取CSV文件列表\n  const fetchCsvFiles = async () => {\n    if (!csvDir) return;\n    setCsvFilesLoading(true);\n    try {\n      const response = await modelPredictionAPI.listCsvFiles(csvDir);\n      setAvailableCsvFiles(response.data.files || []);\n    } catch (error) {\n      var _error$response, _error$response$data;\n      message.error(((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.detail) || '获取CSV文件列表失败');\n      setAvailableCsvFiles([]);\n    } finally {\n      setCsvFilesLoading(false);\n    }\n  };\n\n  // 获取模型文件列表\n  const fetchModelFiles = async () => {\n    if (!modelDir) return;\n    setModelsLoading(true);\n    try {\n      const response = await modelPredictionAPI.listModelFiles(modelDir);\n      setAvailablePthFiles(response.data.pth_files || []);\n    } catch (error) {\n      var _error$response2, _error$response2$data;\n      message.error(((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.detail) || '获取模型文件列表失败');\n      setAvailablePthFiles([]);\n    } finally {\n      setModelsLoading(false);\n    }\n  };\n\n  // 自动匹配参数和标准化器文件\n  const autoMatchFiles = async modelFile => {\n    if (!modelFile || !modelDir) return;\n    setMatchingFilesLoading(true);\n    try {\n      // 调用后端API获取匹配的文件和协议信息\n      const response = await modelPredictionAPI.getMatchingFiles(modelFile, modelDir);\n      if (response.data) {\n        const matchingFiles = response.data;\n\n        // 设置自动匹配的文件\n        setSelectedParamsFile(matchingFiles.params_filename || '');\n        setSelectedScalerFile(matchingFiles.scaler_filename || '');\n        setSelectedProt(matchingFiles.protocol || '');\n        setSelectedDatatype(matchingFiles.datatype || '');\n\n        // 显示匹配结果\n        if (matchingFiles.params_filename && matchingFiles.scaler_filename) {\n          message.success('✅ 已自动匹配相关文件');\n        }\n        if (matchingFiles.protocol && matchingFiles.datatype) {\n          message.success(`✅ 已自动识别模型对应的协议和数据类型：${matchingFiles.protocol} - ${matchingFiles.datatype}`);\n          setShowManualSelection(false);\n        } else {\n          message.warning('⚠️ 无法从文件名自动识别协议和数据类型，请手动选择');\n          setShowManualSelection(true);\n        }\n      }\n    } catch (error) {\n      var _error$response3, _error$response3$data;\n      message.error(((_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : (_error$response3$data = _error$response3.data) === null || _error$response3$data === void 0 ? void 0 : _error$response3$data.detail) || '获取匹配文件失败');\n      // 如果API调用失败，回退到简单的文件名解析\n      const baseNameWithoutExt = modelFile.replace('_model_best.pth', '');\n      setSelectedParamsFile(`${baseNameWithoutExt}_params.json`);\n      setSelectedScalerFile(`${baseNameWithoutExt}_scaler_y.pkl`);\n      setShowManualSelection(true);\n    } finally {\n      setMatchingFilesLoading(false);\n    }\n  };\n\n  // 处理模型文件选择\n  const handleModelFileChange = modelFile => {\n    setSelectedModelFile(modelFile);\n    // 重置之前的选择\n    setSelectedParamsFile('');\n    setSelectedScalerFile('');\n    setSelectedProt('');\n    setSelectedDatatype('');\n    setShowManualSelection(false);\n\n    // 异步调用自动匹配\n    autoMatchFiles(modelFile);\n  };\n\n  // 使用防抖来避免频繁请求 - 只在用户停止输入1.5秒后才发起请求\n  useEffect(() => {\n    if (dataSource === 'local' && csvDir && csvDir.length > 3) {\n      // 至少输入4个字符才开始请求\n      const timer = setTimeout(() => {\n        fetchCsvFiles();\n      }, 1500); // 1.5秒延迟，给用户足够时间输入完整路径\n\n      return () => clearTimeout(timer);\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [dataSource, csvDir]);\n  useEffect(() => {\n    if (modelDir && modelDir.length > 3) {\n      // 至少输入4个字符才开始请求\n      const timer = setTimeout(() => {\n        fetchModelFiles();\n      }, 1500); // 1.5秒延迟，给用户足够时间输入完整路径\n\n      return () => clearTimeout(timer);\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [modelDir]);\n\n  // 文件上传配置\n  const uploadProps = {\n    name: 'file',\n    multiple: false,\n    accept: '.csv',\n    beforeUpload: () => false,\n    onChange: info => {\n      if (info.fileList.length > 0) {\n        setUploadedFile(info.fileList[0]);\n      } else {\n        setUploadedFile(null);\n      }\n    }\n  };\n\n  // 开始预测\n  const handleStartPrediction = async () => {\n    // 验证输入\n    if (dataSource === 'upload' && !uploadedFile) {\n      message.error('请上传CSV文件');\n      return;\n    }\n    if (dataSource === 'local' && (!csvDir || !selectedCsvFile)) {\n      message.error('请选择CSV文件');\n      return;\n    }\n\n    // 验证模型选择\n    let modelsToPredict = [];\n    if (predictionMode === 'single') {\n      if (!selectedModelFile || !selectedProt || !selectedDatatype) {\n        message.error('请选择模型文件并确保协议和数据类型已设置');\n        return;\n      }\n      modelsToPredict = [{\n        model_file: selectedModelFile,\n        params_file: selectedParamsFile,\n        scaler_file: selectedScalerFile,\n        protocol: selectedProt,\n        datatype: selectedDatatype\n      }];\n    } else {\n      if (selectedModels.length === 0) {\n        message.error('请至少选择一个模型');\n        return;\n      }\n\n      // 为每个选中的模型获取匹配信息（与Streamlit版本一致）\n      const validModels = [];\n      for (const modelFile of selectedModels) {\n        try {\n          const response = await modelPredictionAPI.getMatchingFiles(modelFile, modelDir);\n          if (response.data) {\n            const matchingFiles = response.data;\n            const params_file = matchingFiles.params_filename;\n            const scaler_file = matchingFiles.scaler_filename;\n            const protocol = matchingFiles.protocol;\n            const datatype = matchingFiles.datatype;\n\n            // 只有当所有必要信息都可用时，才添加到选中模型列表\n            if (params_file && scaler_file && protocol && datatype) {\n              validModels.push({\n                model_file: modelFile,\n                params_file,\n                scaler_file,\n                protocol,\n                datatype\n              });\n              message.success(`✅ 模型 ${modelFile} 已匹配: ${protocol} - ${datatype}`);\n            } else {\n              message.warning(`⚠️ 模型 ${modelFile} 无法自动识别所有必要参数，已跳过`);\n            }\n          } else {\n            message.error(`获取模型 ${modelFile} 的匹配文件失败`);\n          }\n        } catch (error) {\n          var _error$response4, _error$response4$data;\n          message.error(`处理模型 ${modelFile} 时出错: ${((_error$response4 = error.response) === null || _error$response4 === void 0 ? void 0 : (_error$response4$data = _error$response4.data) === null || _error$response4$data === void 0 ? void 0 : _error$response4$data.detail) || error.message}`);\n        }\n      }\n      if (validModels.length === 0) {\n        message.error('没有可用的模型可以进行预测，请检查模型文件名是否包含必要的协议和数据类型信息');\n        return;\n      }\n      modelsToPredict = validModels;\n    }\n    setPredicting(true);\n    setProgress(0);\n    setResults([]);\n    try {\n      if (useAsyncPrediction) {\n        // 异步预测模式（支持单模型和多模型）\n        const formData = new FormData();\n        if (dataSource === 'upload' && uploadedFile) {\n          formData.append('file', uploadedFile.originFileObj);\n        } else {\n          formData.append('csv_dir', csvDir);\n          formData.append('selected_file', selectedCsvFile);\n        }\n        formData.append('model_dir', modelDir);\n        formData.append('output_folder', modelDir);\n        formData.append('auto_generate_template', autoGenerateTemplate.toString());\n        let taskId;\n        if (predictionMode === 'single') {\n          // 单模型异步预测\n          formData.append('model_filename', selectedModelFile);\n          formData.append('params_filename', selectedParamsFile);\n          formData.append('scaler_filename', selectedScalerFile);\n          formData.append('selected_prot', selectedProt);\n          formData.append('selected_datatype', selectedDatatype);\n          taskId = await submitPredictionTask(formData);\n        } else {\n          // 多模型异步预测\n          const modelsConfig = modelsToPredict.map(model => ({\n            model_file: model.model_file,\n            params_file: model.params_file,\n            scaler_file: model.scaler_file,\n            protocol: model.protocol,\n            datatype: model.datatype\n          }));\n          formData.append('models_config', JSON.stringify(modelsConfig));\n          taskId = await submitMultiPredictionTask(formData);\n        }\n        if (taskId) {\n          const taskType = predictionMode === 'single' ? '预测任务' : '多模型预测任务';\n          message.success(`${taskType}已启动，您可以继续使用其他功能，任务完成后会收到通知`);\n          // 重置状态\n          setPredicting(false);\n          setProgress(0);\n        }\n        return; // 异步模式下直接返回\n      }\n\n      // 同步预测模式（保留原有逻辑）\n      const allResults = [];\n      for (let i = 0; i < modelsToPredict.length; i++) {\n        const model = modelsToPredict[i];\n\n        // 更新进度\n        setProgress(Math.round(i / modelsToPredict.length * 90));\n        if (modelsToPredict.length > 1) {\n          message.info(`正在使用模型 ${i + 1}/${modelsToPredict.length}: ${model.model_file} 进行预测...`);\n        }\n        const formData = new FormData();\n        if (dataSource === 'upload' && uploadedFile) {\n          // 重新读取文件内容，因为文件内容只能读取一次\n          formData.append('file', uploadedFile.originFileObj);\n        } else {\n          formData.append('csv_dir', csvDir);\n          formData.append('selected_file', selectedCsvFile);\n        }\n        formData.append('model_filename', model.model_file);\n        formData.append('params_filename', model.params_file);\n        formData.append('scaler_filename', model.scaler_file);\n        formData.append('selected_prot', model.protocol);\n        formData.append('selected_datatype', model.datatype);\n        formData.append('output_folder', modelDir);\n        // 多模型预测时，所有模型都保存结果到同一个文件，只有最后一个模型生成模板\n        formData.append('save_result_file', autoGenerateTemplate.toString()); // 如果要生成模板，所有模型都保存结果\n        formData.append('auto_generate_template', (autoGenerateTemplate && i === modelsToPredict.length - 1).toString());\n        const response = await modelPredictionAPI.predict(formData);\n        if (response.data) {\n          allResults.push({\n            model_name: response.data.model_name || `${model.protocol}_${model.datatype}`,\n            suggested_threshold: response.data.suggested_threshold || 0,\n            message: response.data.message || '预测完成',\n            // 添加资源监控信息\n            duration_seconds: response.data.duration_seconds,\n            cpu_percent: response.data.cpu_percent,\n            memory_mb: response.data.memory_mb,\n            gpu_memory_mb: response.data.gpu_memory_mb,\n            gpu_utilization_percent: response.data.gpu_utilization_percent,\n            // 添加模板信息（只有最后一个模型会有）\n            template_info: response.data.template_info,\n            result_path: response.data.result_path\n          });\n          if (modelsToPredict.length > 1) {\n            message.success(`✅ 模型 ${model.model_file} 预测成功`);\n\n            // 如果是最后一个模型且生成了模板，显示模板生成信息\n            if (i === modelsToPredict.length - 1 && response.data.template_info) {\n              if (response.data.template_info.template_generated) {\n                message.success({\n                  content: /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        fontWeight: 'bold',\n                        marginBottom: 4\n                      },\n                      children: \"\\uD83C\\uDF89 \\u6E05\\u6D17\\u6A21\\u677F\\u5DF2\\u81EA\\u52A8\\u751F\\u6210\\uFF01\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 601,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        fontSize: '12px',\n                        color: '#666'\n                      },\n                      children: [\"\\uD83D\\uDCC1 \\u6587\\u4EF6\\u8DEF\\u5F84: \", response.data.template_info.template_path, /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 605,\n                        columnNumber: 77\n                      }, this), \"\\uD83D\\uDD27 \\u66F4\\u65B0\\u9608\\u503C: \", response.data.template_info.updated_thresholds, \" \\u4E2A\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 604,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 600,\n                    columnNumber: 21\n                  }, this),\n                  duration: 6\n                });\n              } else {\n                message.error(`清洗模板生成失败: ${response.data.template_info.error}`);\n              }\n            }\n          }\n        }\n      }\n      setProgress(100);\n      setResults(allResults);\n      message.success(`✅ 共完成 ${allResults.length} 个模型的预测`);\n    } catch (error) {\n      var _error$response5, _error$response5$data;\n      message.error(((_error$response5 = error.response) === null || _error$response5 === void 0 ? void 0 : (_error$response5$data = _error$response5.data) === null || _error$response5$data === void 0 ? void 0 : _error$response5$data.detail) || '预测失败');\n    } finally {\n      setPredicting(false);\n    }\n  };\n  const isFormValid = () => {\n    const hasData = dataSource === 'upload' ? uploadedFile : csvDir && selectedCsvFile;\n    if (predictionMode === 'single') {\n      return hasData && selectedModelFile && selectedProt && selectedDatatype;\n    } else {\n      return hasData && selectedModels.length > 0;\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(Title, {\n      level: 3,\n      style: {\n        fontSize: '20px',\n        fontWeight: 600,\n        marginBottom: '8px'\n      },\n      children: \"\\u6A21\\u578B\\u9884\\u6D4B\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 645,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Text, {\n      type: \"secondary\",\n      children: \"\\u52A0\\u8F7D\\u5DF2\\u8BAD\\u7EC3\\u7684\\u6D41\\u91CF\\u6A21\\u578B\\uFF0C\\u5BF9\\u65B0\\u6570\\u636E\\u8FDB\\u884C\\u9884\\u6D4B\\uFF0C\\u8F93\\u51FA\\u6D41\\u91CF\\u6E05\\u6D17\\u9608\\u503C\\u3002\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 646,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 650,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      title: \"\\u6D41\\u91CF\\u6570\\u636E\\u6E90\",\n      className: \"function-card\",\n      children: /*#__PURE__*/_jsxDEV(Space, {\n        direction: \"vertical\",\n        size: \"large\",\n        style: {\n          width: '100%'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Text, {\n            strong: true,\n            children: \"\\u9884\\u6D4B\\u6570\\u636E\\u6E90\\uFF1A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 656,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Radio.Group, {\n            value: dataSource,\n            onChange: e => setDataSource(e.target.value),\n            style: {\n              marginTop: 8\n            },\n            children: [/*#__PURE__*/_jsxDEV(Radio, {\n              value: \"local\",\n              children: \"\\u9009\\u62E9\\u672C\\u5730CSV\\u6587\\u4EF6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 662,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Radio, {\n              value: \"upload\",\n              children: \"\\u4E0A\\u4F20CSV\\u6587\\u4EF6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 663,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 657,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 655,\n          columnNumber: 11\n        }, this), dataSource === 'local' && /*#__PURE__*/_jsxDEV(Space, {\n          direction: \"vertical\",\n          style: {\n            width: '100%'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: \"CSV\\u6587\\u4EF6\\u76EE\\u5F55\\uFF1A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 671,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Input.Group, {\n              compact: true,\n              style: {\n                marginTop: 8,\n                display: 'flex'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Input, {\n                value: csvDir,\n                onChange: e => setCsvDir(e.target.value),\n                placeholder: \"\\u4F8B\\u5982: /data/output\",\n                style: {\n                  flex: 1\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 673,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                type: \"primary\",\n                onClick: fetchCsvFiles,\n                loading: csvFilesLoading,\n                disabled: !csvDir,\n                style: {\n                  marginLeft: 8\n                },\n                children: \"\\u5237\\u65B0\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 679,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 672,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 670,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: \"\\u9009\\u62E9CSV\\u6587\\u4EF6\\uFF1A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 692,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Spin, {\n              spinning: csvFilesLoading,\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                value: selectedCsvFile,\n                onChange: setSelectedCsvFile,\n                placeholder: \"\\u8BF7\\u9009\\u62E9CSV\\u6587\\u4EF6\",\n                style: {\n                  width: '100%',\n                  marginTop: 8\n                },\n                loading: csvFilesLoading,\n                children: availableCsvFiles.map(file => /*#__PURE__*/_jsxDEV(Option, {\n                  value: file,\n                  children: file\n                }, file, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 702,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 694,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 693,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 691,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 669,\n          columnNumber: 13\n        }, this), dataSource === 'upload' && /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Text, {\n            strong: true,\n            children: \"\\u4E0A\\u4F20\\u5F85\\u9884\\u6D4B\\u7684CSV\\u6587\\u4EF6\\uFF1A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 715,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Dragger, {\n            ...uploadProps,\n            style: {\n              marginTop: 8\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"ant-upload-drag-icon\",\n              children: /*#__PURE__*/_jsxDEV(InboxOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 718,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 717,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"ant-upload-text\",\n              children: \"\\u70B9\\u51FB\\u6216\\u62D6\\u62FDCSV\\u6587\\u4EF6\\u5230\\u6B64\\u533A\\u57DF\\u4E0A\\u4F20\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 720,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"ant-upload-hint\",\n              children: \"\\u652F\\u6301CSV\\u683C\\u5F0F\\u7684\\u6D41\\u91CF\\u6570\\u636E\\u6587\\u4EF6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 721,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 716,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 714,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 654,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 653,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      title: \"\\u6A21\\u578B\\u9009\\u62E9\",\n      className: \"function-card\",\n      children: /*#__PURE__*/_jsxDEV(Space, {\n        direction: \"vertical\",\n        size: \"large\",\n        style: {\n          width: '100%'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            size: \"small\",\n            title: /*#__PURE__*/_jsxDEV(Space, {\n              children: [/*#__PURE__*/_jsxDEV(InboxOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 738,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Text, {\n                strong: true,\n                children: \"\\u6A21\\u578B\\u4FDD\\u5B58\\u8DEF\\u5F84\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 739,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 737,\n              columnNumber: 17\n            }, this),\n            children: /*#__PURE__*/_jsxDEV(Input.Group, {\n              compact: true,\n              style: {\n                display: 'flex'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Input, {\n                value: modelDir,\n                onChange: e => setModelDir(e.target.value),\n                placeholder: \"\\u4F8B\\u5982: /data/output\",\n                size: \"large\",\n                prefix: /*#__PURE__*/_jsxDEV(InboxOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 749,\n                  columnNumber: 27\n                }, this),\n                style: {\n                  flex: 1\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 744,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                type: \"primary\",\n                onClick: fetchModelFiles,\n                loading: modelsLoading,\n                disabled: !modelDir,\n                size: \"large\",\n                style: {\n                  marginLeft: 8\n                },\n                children: \"\\u5237\\u65B0\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 752,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 743,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 734,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 733,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Text, {\n            strong: true,\n            children: \"\\u9884\\u6D4B\\u6A21\\u5F0F\\uFF1A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 767,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Radio.Group, {\n            value: predictionMode,\n            onChange: e => setPredictionMode(e.target.value),\n            style: {\n              marginTop: 8\n            },\n            children: [/*#__PURE__*/_jsxDEV(Radio, {\n              value: \"single\",\n              children: \"\\u5355\\u4E2A\\u6A21\\u578B\\u9884\\u6D4B\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 773,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Radio, {\n              value: \"multiple\",\n              children: \"\\u591A\\u4E2A\\u6A21\\u578B\\u6279\\u91CF\\u9884\\u6D4B\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 774,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 768,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 766,\n          columnNumber: 11\n        }, this), predictionMode === 'single' ? /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Text, {\n            strong: true,\n            children: \"\\u9009\\u62E9\\u6A21\\u578B\\u6587\\u4EF6\\uFF1A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 780,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Spin, {\n            spinning: modelsLoading,\n            children: /*#__PURE__*/_jsxDEV(Select, {\n              value: selectedModelFile,\n              onChange: handleModelFileChange,\n              placeholder: \"\\u9009\\u62E9\\u4E00\\u4E2A\\u8BAD\\u7EC3\\u597D\\u7684\\u6A21\\u578B\\u6587\\u4EF6\\uFF0C\\u7CFB\\u7EDF\\u5C06\\u81EA\\u52A8\\u5339\\u914D\\u5BF9\\u5E94\\u7684\\u53C2\\u6570\\u548C\\u6807\\u51C6\\u5316\\u5668\\u6587\\u4EF6\",\n              style: {\n                width: '100%',\n                marginTop: 8\n              },\n              loading: modelsLoading,\n              children: availablePthFiles.map(file => /*#__PURE__*/_jsxDEV(Option, {\n                value: file,\n                children: file\n              }, file, false, {\n                fileName: _jsxFileName,\n                lineNumber: 790,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 782,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 781,\n            columnNumber: 15\n          }, this), selectedModelFile && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginTop: 16\n            },\n            children: /*#__PURE__*/_jsxDEV(Space, {\n              direction: \"vertical\",\n              style: {\n                width: '100%'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(Text, {\n                  type: \"secondary\",\n                  children: \"\\u81EA\\u52A8\\u5339\\u914D\\u7684\\u6587\\u4EF6\\uFF1A\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 801,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Spin, {\n                  spinning: matchingFilesLoading,\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      marginTop: 8,\n                      padding: 12,\n                      backgroundColor: '#f5f5f5',\n                      borderRadius: 4\n                    },\n                    children: matchingFilesLoading ? /*#__PURE__*/_jsxDEV(\"p\", {\n                      children: \"\\u6B63\\u5728\\u81EA\\u52A8\\u5339\\u914D\\u76F8\\u5173\\u6587\\u4EF6...\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 805,\n                      columnNumber: 29\n                    }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                          children: \"\\u53C2\\u6570\\u6587\\u4EF6:\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 808,\n                          columnNumber: 34\n                        }, this), \" \", selectedParamsFile || '未匹配']\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 808,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                          children: \"\\u6807\\u51C6\\u5316\\u5668\\u6587\\u4EF6:\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 809,\n                          columnNumber: 34\n                        }, this), \" \", selectedScalerFile || '未匹配']\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 809,\n                        columnNumber: 31\n                      }, this), !showManualSelection && selectedProt && selectedDatatype && /*#__PURE__*/_jsxDEV(_Fragment, {\n                        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                            children: \"\\u534F\\u8BAE:\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 812,\n                            columnNumber: 38\n                          }, this), \" \", selectedProt]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 812,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                            children: \"\\u6570\\u636E\\u7C7B\\u578B:\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 813,\n                            columnNumber: 38\n                          }, this), \" \", selectedDatatype]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 813,\n                          columnNumber: 35\n                        }, this)]\n                      }, void 0, true)]\n                    }, void 0, true)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 803,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 802,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 800,\n                columnNumber: 21\n              }, this), showManualSelection && /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(Text, {\n                  strong: true,\n                  children: \"\\u8BF7\\u624B\\u52A8\\u9009\\u62E9\\u534F\\u8BAE\\u548C\\u6570\\u636E\\u7C7B\\u578B\\uFF1A\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 824,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    marginTop: 8\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Space, {\n                    direction: \"vertical\",\n                    style: {\n                      width: '100%'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Select, {\n                      value: selectedProt,\n                      onChange: setSelectedProt,\n                      placeholder: \"\\u9009\\u62E9\\u4E0E\\u6A21\\u578B\\u5BF9\\u5E94\\u7684\\u534F\\u8BAE\",\n                      style: {\n                        width: '100%'\n                      },\n                      children: protocolOptions.map(prot => /*#__PURE__*/_jsxDEV(Option, {\n                        value: prot,\n                        children: prot\n                      }, prot, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 834,\n                        columnNumber: 33\n                      }, this))\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 827,\n                      columnNumber: 29\n                    }, this), selectedProt && /*#__PURE__*/_jsxDEV(Select, {\n                      value: selectedDatatype,\n                      onChange: setSelectedDatatype,\n                      placeholder: `选择与模型对应的 ${selectedProt} 数据类型`,\n                      style: {\n                        width: '100%'\n                      },\n                      children: (datatypeOptions[selectedProt] || []).map(datatype => /*#__PURE__*/_jsxDEV(Option, {\n                        value: datatype,\n                        children: datatype\n                      }, datatype, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 848,\n                        columnNumber: 35\n                      }, this))\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 841,\n                      columnNumber: 31\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 826,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 825,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 823,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 799,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 798,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 779,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Text, {\n            strong: true,\n            children: \"\\u9009\\u62E9\\u6A21\\u578B\\u6587\\u4EF6\\uFF08\\u591A\\u9009\\uFF09\\uFF1A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 864,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Spin, {\n            spinning: modelsLoading,\n            children: /*#__PURE__*/_jsxDEV(Select, {\n              mode: \"multiple\",\n              value: selectedModels,\n              onChange: setSelectedModels,\n              placeholder: \"\\u8BF7\\u9009\\u62E9\\u591A\\u4E2A\\u6A21\\u578B\\u6587\\u4EF6\\u8FDB\\u884C\\u6279\\u91CF\\u9884\\u6D4B\",\n              style: {\n                width: '100%',\n                marginTop: 8\n              },\n              loading: modelsLoading,\n              children: availablePthFiles.map(file => /*#__PURE__*/_jsxDEV(Option, {\n                value: file,\n                children: file\n              }, file, false, {\n                fileName: _jsxFileName,\n                lineNumber: 875,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 866,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 865,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 863,\n          columnNumber: 13\n        }, this), availablePthFiles.length === 0 && !modelsLoading && /*#__PURE__*/_jsxDEV(Alert, {\n          message: \"\\u672A\\u627E\\u5230\\u6A21\\u578B\\u6587\\u4EF6\",\n          description: \"\\u8BF7\\u786E\\u4FDD\\u6A21\\u578B\\u76EE\\u5F55\\u4E2D\\u5305\\u542B\\u8BAD\\u7EC3\\u597D\\u7684\\u6A21\\u578B\\u6587\\u4EF6\\uFF08.pth\\uFF09\\u53CA\\u5176\\u5BF9\\u5E94\\u7684\\u53C2\\u6570\\u6587\\u4EF6\\u548C\\u6807\\u51C6\\u5316\\u5668\\u6587\\u4EF6\\u3002\",\n          type: \"warning\",\n          showIcon: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 885,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 732,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 731,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      className: \"function-card\",\n      title: \"\\u9884\\u6D4B\\u6A21\\u5F0F\",\n      children: /*#__PURE__*/_jsxDEV(Space, {\n        direction: \"vertical\",\n        size: \"middle\",\n        style: {\n          width: '100%'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Text, {\n            strong: true,\n            children: \"\\u9009\\u62E9\\u9884\\u6D4B\\u6A21\\u5F0F\\uFF1A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 899,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Radio.Group, {\n            value: useAsyncPrediction,\n            onChange: e => setUseAsyncPrediction(e.target.value),\n            style: {\n              marginTop: 8\n            }\n            // 现在多模型预测也支持异步了\n            ,\n            children: [/*#__PURE__*/_jsxDEV(Radio, {\n              value: true,\n              children: /*#__PURE__*/_jsxDEV(Space, {\n                children: [\"\\u5F02\\u6B65\\u9884\\u6D4B\\uFF08\\u63A8\\u8350\\uFF09\", /*#__PURE__*/_jsxDEV(Text, {\n                  type: \"secondary\",\n                  style: {\n                    fontSize: 12\n                  },\n                  children: \"- \\u540E\\u53F0\\u8FD0\\u884C\\uFF0C\\u4E0D\\u963B\\u585E\\u5176\\u4ED6\\u64CD\\u4F5C\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 909,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 907,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 906,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Radio, {\n              value: false,\n              children: /*#__PURE__*/_jsxDEV(Space, {\n                children: [\"\\u540C\\u6B65\\u9884\\u6D4B\", /*#__PURE__*/_jsxDEV(Text, {\n                  type: \"secondary\",\n                  style: {\n                    fontSize: 12\n                  },\n                  children: \"- \\u7B49\\u5F85\\u9884\\u6D4B\\u5B8C\\u6210\\uFF0C\\u671F\\u95F4\\u65E0\\u6CD5\\u4F7F\\u7528\\u5176\\u4ED6\\u529F\\u80FD\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 917,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 915,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 914,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 900,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 898,\n          columnNumber: 11\n        }, this), useAsyncPrediction && /*#__PURE__*/_jsxDEV(Alert, {\n          message: \"\\u5F02\\u6B65\\u9884\\u6D4B\\u6A21\\u5F0F\",\n          description: /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [\"\\u9884\\u6D4B\\u4EFB\\u52A1\\u5C06\\u5728\\u540E\\u53F0\\u8FD0\\u884C\\uFF0C\\u60A8\\u53EF\\u4EE5\\u7EE7\\u7EED\\u4F7F\\u7528\\u7CFB\\u7EDF\\u7684\\u5176\\u4ED6\\u529F\\u80FD\\u3002\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 931,\n              columnNumber: 19\n            }, this), \"\\u4EFB\\u52A1\\u5B8C\\u6210\\u540E\\u4F1A\\u6536\\u5230\\u901A\\u77E5\\uFF0C\\u53EF\\u5728 \", /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\u4FA7\\u8FB9\\u680F\\u83DC\\u5355 \\u2192 \\u4EFB\\u52A1\\u7BA1\\u7406\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 932,\n              columnNumber: 33\n            }, this), \" \\u4E2D\\u67E5\\u770B\\u8FDB\\u5EA6\\u548C\\u7ED3\\u679C\\u3002\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 929,\n            columnNumber: 17\n          }, this),\n          type: \"info\",\n          showIcon: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 926,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 897,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 896,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      className: \"function-card\",\n      title: \"\\u6E05\\u6D17\\u6A21\\u677F\\u751F\\u6210\",\n      children: /*#__PURE__*/_jsxDEV(Space, {\n        direction: \"vertical\",\n        size: \"middle\",\n        style: {\n          width: '100%'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Text, {\n            strong: true,\n            children: \"\\u81EA\\u52A8\\u751F\\u6210\\u6E05\\u6D17\\u6A21\\u677F\\uFF1A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 946,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginTop: 8\n            },\n            children: /*#__PURE__*/_jsxDEV(Checkbox, {\n              checked: autoGenerateTemplate,\n              onChange: e => setAutoGenerateTemplate(e.target.checked),\n              children: \"\\u9884\\u6D4B\\u5B8C\\u6210\\u540E\\u81EA\\u52A8\\u751F\\u6210\\u6E05\\u6D17\\u6A21\\u677F\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 948,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 947,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginTop: 8\n            },\n            children: /*#__PURE__*/_jsxDEV(Text, {\n              type: \"secondary\",\n              style: {\n                fontSize: '12px'\n              },\n              children: \"\\u9009\\u62E9\\u6B64\\u9009\\u9879\\u540E\\uFF0C\\u7CFB\\u7EDF\\u5C06\\u5728\\u6A21\\u578B\\u9884\\u6D4B\\u5B8C\\u6210\\u540E\\u81EA\\u52A8\\u8C03\\u7528\\u6E05\\u6D17\\u6A21\\u677F\\u751F\\u6210\\u529F\\u80FD\\uFF0C \\u6839\\u636E\\u9884\\u6D4B\\u7ED3\\u679C\\u4E2D\\u7684\\u9608\\u503C\\u4FE1\\u606F\\u751F\\u6210\\u76F8\\u5E94\\u7684\\u6E05\\u6D17\\u6A21\\u677F\\u6587\\u4EF6\\u3002\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 956,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 955,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 945,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 944,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 943,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      className: \"function-card\",\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        type: \"primary\",\n        size: \"large\",\n        icon: /*#__PURE__*/_jsxDEV(PlayCircleOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 970,\n          columnNumber: 17\n        }, this),\n        onClick: handleStartPrediction,\n        loading: predicting,\n        disabled: !isFormValid(),\n        className: \"action-button\",\n        children: predicting ? '正在预测...' : '开始预测与检测'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 967,\n        columnNumber: 9\n      }, this), predicting && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"progress-section\",\n        children: [/*#__PURE__*/_jsxDEV(Text, {\n          children: \"\\u9884\\u6D4B\\u8FDB\\u5EA6\\uFF1A\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 982,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Progress, {\n          percent: progress,\n          status: \"active\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 983,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 981,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 966,\n      columnNumber: 7\n    }, this), results.length > 0 && /*#__PURE__*/_jsxDEV(Card, {\n      title: \"\\u9884\\u6D4B\\u7ED3\\u679C\",\n      className: \"function-card\",\n      children: results.length > 1 ?\n      /*#__PURE__*/\n      // 多模型结果展示 - 与Streamlit版本一致\n      _jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 994,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(Title, {\n          level: 4,\n          children: \"\\u591A\\u6A21\\u578B\\u9884\\u6D4B\\u7ED3\\u679C\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 995,\n          columnNumber: 15\n        }, this), (() => {\n          const lastResult = results[results.length - 1];\n          if (autoGenerateTemplate && lastResult !== null && lastResult !== void 0 && lastResult.template_info) {\n            return /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginBottom: 16\n              },\n              children: lastResult.template_info.template_generated ? /*#__PURE__*/_jsxDEV(Alert, {\n                message: \"\\u2705 \\u6E05\\u6D17\\u6A21\\u677F\\u5DF2\\u81EA\\u52A8\\u751F\\u6210\",\n                description: /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    style: {\n                      margin: 0\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"\\u6A21\\u677F\\u8DEF\\u5F84:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1008,\n                      columnNumber: 56\n                    }, this), \" \", lastResult.template_info.template_path]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1008,\n                    columnNumber: 31\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    style: {\n                      margin: '4px 0 0 0'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"\\u66F4\\u65B0\\u9608\\u503C\\u6570\\u91CF:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1009,\n                      columnNumber: 66\n                    }, this), \" \", lastResult.template_info.updated_thresholds, \" \\u4E2A\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1009,\n                    columnNumber: 31\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    style: {\n                      margin: '4px 0 0 0',\n                      fontSize: '12px',\n                      color: '#666'\n                    },\n                    children: [\"\\u57FA\\u4E8E \", results.length, \" \\u4E2A\\u6A21\\u578B\\u7684\\u9884\\u6D4B\\u7ED3\\u679C\\u751F\\u6210\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1010,\n                    columnNumber: 31\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1007,\n                  columnNumber: 29\n                }, this),\n                type: \"success\",\n                showIcon: true,\n                style: {\n                  marginBottom: 16\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1004,\n                columnNumber: 25\n              }, this) : /*#__PURE__*/_jsxDEV(Alert, {\n                message: \"\\u274C \\u6E05\\u6D17\\u6A21\\u677F\\u751F\\u6210\\u5931\\u8D25\",\n                description: lastResult.template_info.error,\n                type: \"error\",\n                showIcon: true,\n                style: {\n                  marginBottom: 16\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1020,\n                columnNumber: 25\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1002,\n              columnNumber: 21\n            }, this);\n          }\n          return null;\n        })(), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: 24\n          },\n          children: [/*#__PURE__*/_jsxDEV(Text, {\n            strong: true,\n            children: \"\\u9009\\u62E9\\u8981\\u67E5\\u770B\\u7684\\u6A21\\u578B\\u7ED3\\u679C\\uFF1A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1035,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Select, {\n            style: {\n              width: '100%',\n              marginTop: 8\n            },\n            placeholder: \"\\u9009\\u62E9\\u6A21\\u578B\\u7ED3\\u679C\",\n            value: selectedResultIndex,\n            onChange: value => setSelectedResultIndex(value),\n            children: results.map((result, index) => /*#__PURE__*/_jsxDEV(Option, {\n              value: index,\n              children: result.model_name\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1043,\n              columnNumber: 21\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1036,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1034,\n          columnNumber: 15\n        }, this), results[selectedResultIndex] && /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Title, {\n            level: 5,\n            children: [\"\\u6A21\\u578B: \", results[selectedResultIndex].model_name]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1053,\n            columnNumber: 19\n          }, this), /*#__PURE__*/_jsxDEV(PredictionResultDisplay, {\n            result: results[selectedResultIndex]\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1054,\n            columnNumber: 19\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1052,\n          columnNumber: 17\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 993,\n        columnNumber: 13\n      }, this) :\n      /*#__PURE__*/\n      // 单模型结果展示\n      _jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(Title, {\n          level: 4,\n          children: [\"\\u9884\\u6D4B\\u7ED3\\u679C - \", results[0].model_name]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1061,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(PredictionResultDisplay, {\n          result: results[0]\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1062,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1060,\n        columnNumber: 13\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 990,\n      columnNumber: 9\n    }, this), completedPredictionTasks.length > 0 && /*#__PURE__*/_jsxDEV(Card, {\n      title: \"\\u5F02\\u6B65\\u9884\\u6D4B\\u7ED3\\u679C\",\n      className: \"function-card\",\n      style: {\n        marginTop: 24\n      },\n      children: [/*#__PURE__*/_jsxDEV(Alert, {\n        message: \"\\u5F02\\u6B65\\u9884\\u6D4B\\u5DF2\\u5B8C\\u6210\",\n        description: \"\\u4EE5\\u4E0B\\u662F\\u540E\\u53F0\\u9884\\u6D4B\\u4EFB\\u52A1\\u7684\\u7ED3\\u679C\\uFF0C\\u60A8\\u53EF\\u4EE5\\u67E5\\u770B\\u9884\\u6D4B\\u7ED3\\u679C\\u3002\",\n        type: \"success\",\n        showIcon: true,\n        style: {\n          marginBottom: 16\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1071,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Space, {\n        direction: \"vertical\",\n        size: \"large\",\n        style: {\n          width: '100%'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Text, {\n            strong: true,\n            children: \"\\u9009\\u62E9\\u9884\\u6D4B\\u4EFB\\u52A1\\uFF1A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1081,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Select, {\n            value: selectedAsyncTaskId,\n            onChange: handleAsyncTaskSelect,\n            style: {\n              width: '100%',\n              marginTop: 8\n            },\n            placeholder: \"\\u8BF7\\u9009\\u62E9\\u8981\\u67E5\\u770B\\u7684\\u9884\\u6D4B\\u4EFB\\u52A1\",\n            children: completedPredictionTasks.map(task => /*#__PURE__*/_jsxDEV(Option, {\n              value: task.task_id,\n              children: task.task_id.includes('_') ? `${task.task_id.split('_')[0]} (${new Date(task.updated_at || task.created_at).toLocaleString()})` : `任务 ${task.task_id.substring(0, 8)}... (${new Date(task.updated_at || task.created_at).toLocaleString()})`\n            }, task.task_id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1089,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1082,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1080,\n          columnNumber: 13\n        }, this), asyncPredictionResults.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n          children: asyncPredictionResults.length > 1 ?\n          /*#__PURE__*/\n          // 多模型异步预测结果展示 - 与同步多模型预测保持一致\n          _jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1105,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(Title, {\n              level: 4,\n              children: \"\\u591A\\u6A21\\u578B\\u9884\\u6D4B\\u7ED3\\u679C\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1106,\n              columnNumber: 21\n            }, this), asyncTemplateInfo && /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginBottom: 16\n              },\n              children: asyncTemplateInfo.template_generated ? /*#__PURE__*/_jsxDEV(Alert, {\n                message: \"\\u2705 \\u6E05\\u6D17\\u6A21\\u677F\\u5DF2\\u81EA\\u52A8\\u751F\\u6210\",\n                description: /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [\"\\uD83D\\uDCC1 \\u6587\\u4EF6\\u8DEF\\u5F84: \", asyncTemplateInfo.template_path, /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1116,\n                    columnNumber: 75\n                  }, this), \"\\uD83D\\uDD27 \\u66F4\\u65B0\\u9608\\u503C: \", asyncTemplateInfo.updated_thresholds, \" \\u4E2A\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1115,\n                  columnNumber: 31\n                }, this),\n                type: \"success\",\n                showIcon: true,\n                style: {\n                  marginBottom: 16\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1112,\n                columnNumber: 27\n              }, this) : /*#__PURE__*/_jsxDEV(Alert, {\n                message: \"\\u274C \\u6E05\\u6D17\\u6A21\\u677F\\u751F\\u6210\\u5931\\u8D25\",\n                description: asyncTemplateInfo.error,\n                type: \"error\",\n                showIcon: true,\n                style: {\n                  marginBottom: 16\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1125,\n                columnNumber: 27\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1110,\n              columnNumber: 23\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginBottom: 24\n              },\n              children: [/*#__PURE__*/_jsxDEV(Text, {\n                strong: true,\n                children: \"\\u9009\\u62E9\\u8981\\u67E5\\u770B\\u7684\\u6A21\\u578B\\u7ED3\\u679C\\uFF1A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1137,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                style: {\n                  width: '100%',\n                  marginTop: 8\n                },\n                placeholder: \"\\u9009\\u62E9\\u6A21\\u578B\\u7ED3\\u679C\",\n                value: selectedAsyncResultIndex,\n                onChange: value => setSelectedAsyncResultIndex(value),\n                children: asyncPredictionResults.map((result, index) => /*#__PURE__*/_jsxDEV(Option, {\n                  value: index,\n                  children: result.model_name\n                }, index, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1145,\n                  columnNumber: 27\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1138,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1136,\n              columnNumber: 21\n            }, this), asyncPredictionResults[selectedAsyncResultIndex] && /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(Title, {\n                level: 5,\n                children: [\"\\u6A21\\u578B: \", asyncPredictionResults[selectedAsyncResultIndex].model_name]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1155,\n                columnNumber: 25\n              }, this), /*#__PURE__*/_jsxDEV(PredictionResultDisplay, {\n                result: asyncPredictionResults[selectedAsyncResultIndex]\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1156,\n                columnNumber: 25\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1154,\n              columnNumber: 23\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1104,\n            columnNumber: 19\n          }, this) :\n          /*#__PURE__*/\n          // 单模型异步预测结果展示\n          _jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(Title, {\n              level: 4,\n              children: [\"\\u9884\\u6D4B\\u7ED3\\u679C - \", asyncPredictionResults[0].model_name]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1163,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(PredictionResultDisplay, {\n              result: asyncPredictionResults[0]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1164,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1162,\n            columnNumber: 19\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1101,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1078,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1070,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 644,\n    columnNumber: 5\n  }, this);\n};\n_s(ModelPredictionPage, \"sgZYI6Y6En7VvaEST7LxAV2Q9G0=\", false, function () {\n  return [useTaskManager];\n});\n_c2 = ModelPredictionPage;\nexport default ModelPredictionPage;\nvar _c, _c2;\n$RefreshReg$(_c, \"PredictionResultDisplay\");\n$RefreshReg$(_c2, \"ModelPredictionPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "Card", "Radio", "Upload", "Input", "Select", "<PERSON><PERSON>", "Typography", "Space", "Divider", "message", "Spin", "<PERSON><PERSON>", "Statistic", "Row", "Col", "Progress", "Checkbox", "InboxOutlined", "PlayCircleOutlined", "modelPredictionAPI", "useTaskManager", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Title", "Text", "<PERSON><PERSON>", "Option", "PredictionResultDisplay", "result", "children", "gutter", "style", "marginBottom", "span", "title", "value", "suggested_threshold", "precision", "valueStyle", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "showIcon", "marginTop", "duration_seconds", "undefined", "cpu_percent", "memory_mb", "gpu_memory_mb", "gpu_utilization_percent", "level", "flex", "suffix", "template_info", "padding", "backgroundColor", "template_generated", "border", "borderRadius", "fontWeight", "margin", "template_path", "updated_thresholds", "error", "_c", "protocolOptions", "datatypeOptions", "TCP", "UDP", "ICMP", "ModelPredictionPage", "_s", "dataSource", "setDataSource", "uploadedFile", "setUploadedFile", "csvDir", "setCsvDir", "availableCsvFiles", "setAvailableCsvFiles", "selectedCsvFile", "setSelectedCsvFile", "csvFilesLoading", "setCsvFilesLoading", "modelDir", "setModelDir", "availablePthFiles", "setAvailablePthFiles", "selectedModels", "setSelectedModels", "modelsLoading", "setModelsLoading", "predictionMode", "setPredictionMode", "selectedModelFile", "setSelectedModelFile", "selectedParamsFile", "setSelectedParamsFile", "selectedScalerFile", "setSelectedScalerFile", "<PERSON><PERSON><PERSON>", "setSelected<PERSON>rot", "selectedDatatype", "setSelectedDatatype", "showManualSelection", "setShowManualSelection", "predicting", "setPredicting", "progress", "setProgress", "results", "setResults", "selectedResultIndex", "setSelectedResultIndex", "matchingFilesLoading", "setMatchingFilesLoading", "autoGenerateTemplate", "setAutoGenerateTemplate", "submitPredictionTask", "submitMultiPredictionTask", "getCompletedTasksByType", "fetchCompletedTasks", "useAsyncPrediction", "setUseAsyncPrediction", "asyncPredictionResults", "setAsyncPredictionResults", "selectedAsyncTaskId", "setSelectedAsyncTaskId", "asyncTemplateInfo", "setAsyncTemplateInfo", "selectedAsyncResultIndex", "setSelectedAsyncResultIndex", "completedPredictionTasks", "handleAsyncTaskSelect", "taskId", "selectedTask", "find", "task", "task_id", "is_multi_model", "asyncResult", "model_name", "result_path", "length", "latestTask", "fetchCsvFiles", "response", "listCsvFiles", "data", "files", "_error$response", "_error$response$data", "detail", "fetchModelFiles", "listModelFiles", "pth_files", "_error$response2", "_error$response2$data", "autoMatchFiles", "modelFile", "getMatchingFiles", "matchingFiles", "params_filename", "scaler_filename", "protocol", "datatype", "success", "warning", "_error$response3", "_error$response3$data", "baseNameWithoutExt", "replace", "handleModelFileChange", "timer", "setTimeout", "clearTimeout", "uploadProps", "name", "multiple", "accept", "beforeUpload", "onChange", "info", "fileList", "handleStartPrediction", "modelsToPredict", "model_file", "params_file", "scaler_file", "validModels", "push", "_error$response4", "_error$response4$data", "formData", "FormData", "append", "originFileObj", "toString", "modelsConfig", "map", "model", "JSON", "stringify", "taskType", "allResults", "i", "Math", "round", "predict", "content", "fontSize", "duration", "_error$response5", "_error$response5$data", "isFormValid", "hasData", "className", "direction", "size", "width", "strong", "Group", "e", "target", "compact", "display", "placeholder", "onClick", "loading", "disabled", "marginLeft", "spinning", "file", "prefix", "prot", "mode", "description", "checked", "icon", "percent", "status", "lastResult", "index", "includes", "split", "Date", "updated_at", "created_at", "toLocaleString", "substring", "_c2", "$RefreshReg$"], "sources": ["/home/<USER>/frontend-react-stable/src/pages/ModelPredictionPage.tsx"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from 'react';\nimport {\n  Card,\n  Radio,\n  Upload,\n  Input,\n  Select,\n  Button,\n  Typography,\n  Space,\n  Divider,\n  message,\n  Spin,\n  Alert,\n  Statistic,\n  Row,\n  Col,\n  Progress,\n  Checkbox,\n} from 'antd';\nimport { InboxOutlined, PlayCircleOutlined } from '@ant-design/icons';\n\nimport { modelPredictionAPI } from '../services/api';\nimport useTaskManager from '../hooks/useTaskManager';\n\nconst { Title, Text } = Typography;\nconst { Dragger } = Upload;\nconst { Option } = Select;\n\n// 预测结果展示组件\nconst PredictionResultDisplay: React.FC<{ result: PredictionResult }> = ({ result }) => {\n\n\n  return (\n    <div>\n      <Row gutter={16} style={{ marginBottom: 16 }}>\n        <Col span={24}>\n          <Statistic\n            title=\"建议的流量清洗阈值 (pps)\"\n            value={result.suggested_threshold}\n            precision={2}\n            valueStyle={{ color: '#1890ff' }}\n          />\n          {result.suggested_threshold && (\n            <Alert\n              message=\"✅ 此阈值已自动保存到以输入CSV文件命名的结果文件中，可用于生成清洗模板。\"\n              type=\"success\"\n              showIcon\n              style={{ marginTop: 8 }}\n            />\n          )}\n        </Col>\n\n      </Row>\n\n\n\n      {/* 资源监控信息 - 一行内展示 */}\n      {(result.duration_seconds !== undefined || result.cpu_percent !== undefined || result.memory_mb !== undefined || result.gpu_memory_mb !== undefined || result.gpu_utilization_percent !== undefined) && (\n        <div style={{ marginBottom: 24 }}>\n          <Title level={5}>资源使用情况</Title>\n          <Row gutter={16}>\n            {result.duration_seconds !== undefined && (\n              <Col flex=\"auto\">\n                <Statistic\n                  title=\"预测耗时\"\n                  value={result.duration_seconds}\n                  precision={2}\n                  suffix=\"秒\"\n                  valueStyle={{ color: '#1890ff' }}\n                />\n              </Col>\n            )}\n            {result.cpu_percent !== undefined && (\n              <Col flex=\"auto\">\n                <Statistic\n                  title=\"CPU使用率\"\n                  value={result.cpu_percent}\n                  precision={1}\n                  suffix=\"%\"\n                  valueStyle={{ color: '#52c41a' }}\n                />\n              </Col>\n            )}\n            {result.memory_mb !== undefined && (\n              <Col flex=\"auto\">\n                <Statistic\n                  title=\"内存使用\"\n                  value={result.memory_mb}\n                  precision={1}\n                  suffix=\"MB\"\n                  valueStyle={{ color: '#fa8c16' }}\n                />\n              </Col>\n            )}\n            {result.gpu_memory_mb !== undefined && (\n              <Col flex=\"auto\">\n                <Statistic\n                  title=\"GPU内存\"\n                  value={result.gpu_memory_mb}\n                  precision={1}\n                  suffix=\"MB\"\n                  valueStyle={{ color: '#722ed1' }}\n                />\n              </Col>\n            )}\n            {result.gpu_utilization_percent !== undefined && (\n              <Col flex=\"auto\">\n                <Statistic\n                  title=\"GPU利用率\"\n                  value={result.gpu_utilization_percent}\n                  precision={1}\n                  suffix=\"%\"\n                  valueStyle={{ color: '#eb2f96' }}\n                />\n              </Col>\n            )}\n          </Row>\n        </div>\n      )}\n\n      {/* 模板生成信息 */}\n      {result.template_info && (\n        <div style={{ marginBottom: 24 }}>\n          <Title level={5}>清洗模板生成</Title>\n          <div style={{ padding: 12, backgroundColor: result.template_info.template_generated ? '#f6ffed' : '#fff2f0', border: `1px solid ${result.template_info.template_generated ? '#b7eb8f' : '#ffccc7'}`, borderRadius: 6 }}>\n            {result.template_info.template_generated ? (\n              <div>\n                <p style={{ color: '#52c41a', fontWeight: 'bold', margin: 0 }}>✅ 清洗模板已自动生成</p>\n                {result.template_info.template_path && (\n                  <p style={{ margin: '8px 0 0 0' }}><strong>模板路径:</strong> {result.template_info.template_path}</p>\n                )}\n                {result.template_info.updated_thresholds !== undefined && (\n                  <p style={{ margin: '4px 0 0 0' }}><strong>更新阈值数量:</strong> {result.template_info.updated_thresholds}</p>\n                )}\n              </div>\n            ) : (\n              <div>\n                <p style={{ color: '#ff4d4f', fontWeight: 'bold', margin: 0 }}>❌ 清洗模板生成失败</p>\n                {result.template_info.error && (\n                  <p style={{ margin: '8px 0 0 0' }}><strong>错误信息:</strong> {result.template_info.error}</p>\n                )}\n              </div>\n            )}\n          </div>\n        </div>\n      )}\n\n    </div>\n  );\n};\n\n// 协议和数据类型配置\nconst protocolOptions = ['TCP', 'UDP', 'ICMP'];\nconst datatypeOptions = {\n  TCP: ['spt_sip_dip', 'dpt_sip_dip', 'len_dpt_syn', 'seq_ack_dip'],\n  UDP: ['spt_sip_dip', 'dpt_sip_dip'],\n  ICMP: ['dip']\n};\n\ninterface PredictionResult {\n  model_name: string;\n  suggested_threshold: number;\n  message?: string;\n  // 资源监控信息\n  duration_seconds?: number;\n  cpu_percent?: number;\n  memory_mb?: number;\n  gpu_memory_mb?: number;\n  gpu_utilization_percent?: number;\n  // 模板生成信息\n  template_info?: {\n    template_generated: boolean;\n    template_path?: string;\n    updated_thresholds?: number;\n    error?: string;\n  };\n  result_path?: string;\n}\n\nconst ModelPredictionPage: React.FC = () => {\n  const [dataSource, setDataSource] = useState<'upload' | 'local'>('local');\n  const [uploadedFile, setUploadedFile] = useState<any>(null);\n  const [csvDir, setCsvDir] = useState('');\n  const [availableCsvFiles, setAvailableCsvFiles] = useState<string[]>([]);\n  const [selectedCsvFile, setSelectedCsvFile] = useState<string>('');\n  const [csvFilesLoading, setCsvFilesLoading] = useState(false);\n\n  // 模型相关状态\n  const [modelDir, setModelDir] = useState('');\n  const [availablePthFiles, setAvailablePthFiles] = useState<string[]>([]);\n  const [selectedModels, setSelectedModels] = useState<string[]>([]);\n  const [modelsLoading, setModelsLoading] = useState(false);\n  const [predictionMode, setPredictionMode] = useState<'single' | 'multiple'>('single');\n\n  // 单模型选择状态\n  const [selectedModelFile, setSelectedModelFile] = useState<string>('');\n  const [selectedParamsFile, setSelectedParamsFile] = useState<string>('');\n  const [selectedScalerFile, setSelectedScalerFile] = useState<string>('');\n  const [selectedProt, setSelectedProt] = useState<string>('');\n  const [selectedDatatype, setSelectedDatatype] = useState<string>('');\n  const [showManualSelection, setShowManualSelection] = useState(false);\n\n  // 预测状态\n  const [predicting, setPredicting] = useState(false);\n  const [progress, setProgress] = useState(0);\n  const [results, setResults] = useState<PredictionResult[]>([]);\n  const [selectedResultIndex, setSelectedResultIndex] = useState<number>(0);\n  const [matchingFilesLoading, setMatchingFilesLoading] = useState(false);\n  const [autoGenerateTemplate, setAutoGenerateTemplate] = useState(false); // 新增：是否自动生成清洗模板\n\n  // 任务管理\n  const { submitPredictionTask, submitMultiPredictionTask, getCompletedTasksByType, fetchCompletedTasks } = useTaskManager();\n  const [useAsyncPrediction, setUseAsyncPrediction] = useState(true); // 默认使用异步预测\n\n  // 异步任务结果状态\n  const [asyncPredictionResults, setAsyncPredictionResults] = useState<PredictionResult[]>([]);\n  const [selectedAsyncTaskId, setSelectedAsyncTaskId] = useState<string>('');\n  const [asyncTemplateInfo, setAsyncTemplateInfo] = useState<any>(null);\n  const [selectedAsyncResultIndex, setSelectedAsyncResultIndex] = useState<number>(0);\n\n  // 获取已完成的预测任务\n  const completedPredictionTasks = getCompletedTasksByType('prediction');\n\n  // 处理异步任务选择\n  const handleAsyncTaskSelect = useCallback((taskId: string) => {\n    setSelectedAsyncTaskId(taskId);\n    const selectedTask = completedPredictionTasks.find(task => task.task_id === taskId);\n\n    if (selectedTask && selectedTask.result) {\n      // 检查是否是多模型异步预测结果\n      if (selectedTask.result.is_multi_model && selectedTask.result.results) {\n        // 多模型异步预测结果 - 直接使用 results 数组\n        setAsyncPredictionResults(selectedTask.result.results);\n        setAsyncTemplateInfo(selectedTask.result.template_info);\n      } else {\n        // 单模型异步预测结果 - 转换为数组格式\n        const asyncResult: PredictionResult = {\n          suggested_threshold: selectedTask.result.suggested_threshold || 0,\n          model_name: selectedTask.result.model_name || '未知模型',\n          message: selectedTask.result.message || '预测完成',\n          duration_seconds: selectedTask.result.duration_seconds,\n          cpu_percent: selectedTask.result.cpu_percent,\n          memory_mb: selectedTask.result.memory_mb,\n          gpu_memory_mb: selectedTask.result.gpu_memory_mb,\n          gpu_utilization_percent: selectedTask.result.gpu_utilization_percent,\n          template_info: selectedTask.result.template_info,\n          result_path: selectedTask.result.result_path\n        };\n\n        setAsyncPredictionResults([asyncResult]);\n        setAsyncTemplateInfo(selectedTask.result.template_info);\n      }\n    }\n  }, [completedPredictionTasks]);\n\n  // 页面加载时获取已完成任务\n  useEffect(() => {\n    fetchCompletedTasks();\n  }, [fetchCompletedTasks]);\n\n  // 自动选择最新的预测任务\n  useEffect(() => {\n    if (completedPredictionTasks.length > 0 && !selectedAsyncTaskId) {\n      const latestTask = completedPredictionTasks[completedPredictionTasks.length - 1];\n      handleAsyncTaskSelect(latestTask.task_id);\n    }\n  }, [completedPredictionTasks, selectedAsyncTaskId, handleAsyncTaskSelect]);\n\n  // 获取CSV文件列表\n  const fetchCsvFiles = async () => {\n    if (!csvDir) return;\n\n    setCsvFilesLoading(true);\n    try {\n      const response = await modelPredictionAPI.listCsvFiles(csvDir);\n      setAvailableCsvFiles(response.data.files || []);\n    } catch (error: any) {\n      message.error(error.response?.data?.detail || '获取CSV文件列表失败');\n      setAvailableCsvFiles([]);\n    } finally {\n      setCsvFilesLoading(false);\n    }\n  };\n\n  // 获取模型文件列表\n  const fetchModelFiles = async () => {\n    if (!modelDir) return;\n\n    setModelsLoading(true);\n    try {\n      const response = await modelPredictionAPI.listModelFiles(modelDir);\n      setAvailablePthFiles(response.data.pth_files || []);\n    } catch (error: any) {\n      message.error(error.response?.data?.detail || '获取模型文件列表失败');\n      setAvailablePthFiles([]);\n    } finally {\n      setModelsLoading(false);\n    }\n  };\n\n  // 自动匹配参数和标准化器文件\n  const autoMatchFiles = async (modelFile: string) => {\n    if (!modelFile || !modelDir) return;\n\n    setMatchingFilesLoading(true);\n    try {\n      // 调用后端API获取匹配的文件和协议信息\n      const response = await modelPredictionAPI.getMatchingFiles(modelFile, modelDir);\n\n      if (response.data) {\n        const matchingFiles = response.data;\n\n        // 设置自动匹配的文件\n        setSelectedParamsFile(matchingFiles.params_filename || '');\n        setSelectedScalerFile(matchingFiles.scaler_filename || '');\n        setSelectedProt(matchingFiles.protocol || '');\n        setSelectedDatatype(matchingFiles.datatype || '');\n\n        // 显示匹配结果\n        if (matchingFiles.params_filename && matchingFiles.scaler_filename) {\n          message.success('✅ 已自动匹配相关文件');\n        }\n\n        if (matchingFiles.protocol && matchingFiles.datatype) {\n          message.success(`✅ 已自动识别模型对应的协议和数据类型：${matchingFiles.protocol} - ${matchingFiles.datatype}`);\n          setShowManualSelection(false);\n        } else {\n          message.warning('⚠️ 无法从文件名自动识别协议和数据类型，请手动选择');\n          setShowManualSelection(true);\n        }\n      }\n    } catch (error: any) {\n      message.error(error.response?.data?.detail || '获取匹配文件失败');\n      // 如果API调用失败，回退到简单的文件名解析\n      const baseNameWithoutExt = modelFile.replace('_model_best.pth', '');\n      setSelectedParamsFile(`${baseNameWithoutExt}_params.json`);\n      setSelectedScalerFile(`${baseNameWithoutExt}_scaler_y.pkl`);\n      setShowManualSelection(true);\n    } finally {\n      setMatchingFilesLoading(false);\n    }\n  };\n\n  // 处理模型文件选择\n  const handleModelFileChange = (modelFile: string) => {\n    setSelectedModelFile(modelFile);\n    // 重置之前的选择\n    setSelectedParamsFile('');\n    setSelectedScalerFile('');\n    setSelectedProt('');\n    setSelectedDatatype('');\n    setShowManualSelection(false);\n\n    // 异步调用自动匹配\n    autoMatchFiles(modelFile);\n  };\n\n  // 使用防抖来避免频繁请求 - 只在用户停止输入1.5秒后才发起请求\n  useEffect(() => {\n    if (dataSource === 'local' && csvDir && csvDir.length > 3) { // 至少输入4个字符才开始请求\n      const timer = setTimeout(() => {\n        fetchCsvFiles();\n      }, 1500); // 1.5秒延迟，给用户足够时间输入完整路径\n\n      return () => clearTimeout(timer);\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [dataSource, csvDir]);\n\n  useEffect(() => {\n    if (modelDir && modelDir.length > 3) { // 至少输入4个字符才开始请求\n      const timer = setTimeout(() => {\n        fetchModelFiles();\n      }, 1500); // 1.5秒延迟，给用户足够时间输入完整路径\n\n      return () => clearTimeout(timer);\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [modelDir]);\n\n  // 文件上传配置\n  const uploadProps = {\n    name: 'file',\n    multiple: false,\n    accept: '.csv',\n    beforeUpload: () => false,\n    onChange: (info: any) => {\n      if (info.fileList.length > 0) {\n        setUploadedFile(info.fileList[0]);\n      } else {\n        setUploadedFile(null);\n      }\n    },\n  };\n\n  // 开始预测\n  const handleStartPrediction = async () => {\n    // 验证输入\n    if (dataSource === 'upload' && !uploadedFile) {\n      message.error('请上传CSV文件');\n      return;\n    }\n\n    if (dataSource === 'local' && (!csvDir || !selectedCsvFile)) {\n      message.error('请选择CSV文件');\n      return;\n    }\n\n    // 验证模型选择\n    let modelsToPredict: Array<{\n      model_file: string;\n      params_file: string;\n      scaler_file: string;\n      protocol: string;\n      datatype: string;\n    }> = [];\n\n    if (predictionMode === 'single') {\n      if (!selectedModelFile || !selectedProt || !selectedDatatype) {\n        message.error('请选择模型文件并确保协议和数据类型已设置');\n        return;\n      }\n      modelsToPredict = [{\n        model_file: selectedModelFile,\n        params_file: selectedParamsFile,\n        scaler_file: selectedScalerFile,\n        protocol: selectedProt,\n        datatype: selectedDatatype\n      }];\n    } else {\n      if (selectedModels.length === 0) {\n        message.error('请至少选择一个模型');\n        return;\n      }\n\n      // 为每个选中的模型获取匹配信息（与Streamlit版本一致）\n      const validModels: Array<{\n        model_file: string;\n        params_file: string;\n        scaler_file: string;\n        protocol: string;\n        datatype: string;\n      }> = [];\n\n      for (const modelFile of selectedModels) {\n        try {\n          const response = await modelPredictionAPI.getMatchingFiles(modelFile, modelDir);\n          if (response.data) {\n            const matchingFiles = response.data;\n            const params_file = matchingFiles.params_filename;\n            const scaler_file = matchingFiles.scaler_filename;\n            const protocol = matchingFiles.protocol;\n            const datatype = matchingFiles.datatype;\n\n            // 只有当所有必要信息都可用时，才添加到选中模型列表\n            if (params_file && scaler_file && protocol && datatype) {\n              validModels.push({\n                model_file: modelFile,\n                params_file,\n                scaler_file,\n                protocol,\n                datatype\n              });\n              message.success(`✅ 模型 ${modelFile} 已匹配: ${protocol} - ${datatype}`);\n            } else {\n              message.warning(`⚠️ 模型 ${modelFile} 无法自动识别所有必要参数，已跳过`);\n            }\n          } else {\n            message.error(`获取模型 ${modelFile} 的匹配文件失败`);\n          }\n        } catch (error: any) {\n          message.error(`处理模型 ${modelFile} 时出错: ${error.response?.data?.detail || error.message}`);\n        }\n      }\n\n      if (validModels.length === 0) {\n        message.error('没有可用的模型可以进行预测，请检查模型文件名是否包含必要的协议和数据类型信息');\n        return;\n      }\n\n      modelsToPredict = validModels;\n    }\n\n    setPredicting(true);\n    setProgress(0);\n    setResults([]);\n\n    try {\n      if (useAsyncPrediction) {\n        // 异步预测模式（支持单模型和多模型）\n        const formData = new FormData();\n\n        if (dataSource === 'upload' && uploadedFile) {\n          formData.append('file', uploadedFile.originFileObj);\n        } else {\n          formData.append('csv_dir', csvDir);\n          formData.append('selected_file', selectedCsvFile);\n        }\n\n        formData.append('model_dir', modelDir);\n        formData.append('output_folder', modelDir);\n        formData.append('auto_generate_template', autoGenerateTemplate.toString());\n\n        let taskId: string | undefined;\n\n        if (predictionMode === 'single') {\n          // 单模型异步预测\n          formData.append('model_filename', selectedModelFile);\n          formData.append('params_filename', selectedParamsFile);\n          formData.append('scaler_filename', selectedScalerFile);\n          formData.append('selected_prot', selectedProt);\n          formData.append('selected_datatype', selectedDatatype);\n\n          taskId = await submitPredictionTask(formData);\n        } else {\n          // 多模型异步预测\n          const modelsConfig = modelsToPredict.map(model => ({\n            model_file: model.model_file,\n            params_file: model.params_file,\n            scaler_file: model.scaler_file,\n            protocol: model.protocol,\n            datatype: model.datatype\n          }));\n\n          formData.append('models_config', JSON.stringify(modelsConfig));\n          taskId = await submitMultiPredictionTask(formData);\n        }\n\n        if (taskId) {\n          const taskType = predictionMode === 'single' ? '预测任务' : '多模型预测任务';\n          message.success(`${taskType}已启动，您可以继续使用其他功能，任务完成后会收到通知`);\n          // 重置状态\n          setPredicting(false);\n          setProgress(0);\n        }\n\n        return; // 异步模式下直接返回\n      }\n\n      // 同步预测模式（保留原有逻辑）\n      const allResults: PredictionResult[] = [];\n\n      for (let i = 0; i < modelsToPredict.length; i++) {\n        const model = modelsToPredict[i];\n\n        // 更新进度\n        setProgress(Math.round((i / modelsToPredict.length) * 90));\n\n        if (modelsToPredict.length > 1) {\n          message.info(`正在使用模型 ${i + 1}/${modelsToPredict.length}: ${model.model_file} 进行预测...`);\n        }\n\n        const formData = new FormData();\n\n        if (dataSource === 'upload' && uploadedFile) {\n          // 重新读取文件内容，因为文件内容只能读取一次\n          formData.append('file', uploadedFile.originFileObj);\n        } else {\n          formData.append('csv_dir', csvDir);\n          formData.append('selected_file', selectedCsvFile);\n        }\n\n        formData.append('model_filename', model.model_file);\n        formData.append('params_filename', model.params_file);\n        formData.append('scaler_filename', model.scaler_file);\n        formData.append('selected_prot', model.protocol);\n        formData.append('selected_datatype', model.datatype);\n        formData.append('output_folder', modelDir);\n        // 多模型预测时，所有模型都保存结果到同一个文件，只有最后一个模型生成模板\n        formData.append('save_result_file', autoGenerateTemplate.toString()); // 如果要生成模板，所有模型都保存结果\n        formData.append('auto_generate_template', (autoGenerateTemplate && i === modelsToPredict.length - 1).toString());\n\n        const response = await modelPredictionAPI.predict(formData);\n\n        if (response.data) {\n          allResults.push({\n            model_name: response.data.model_name || `${model.protocol}_${model.datatype}`,\n            suggested_threshold: response.data.suggested_threshold || 0,\n            message: response.data.message || '预测完成',\n            // 添加资源监控信息\n            duration_seconds: response.data.duration_seconds,\n            cpu_percent: response.data.cpu_percent,\n            memory_mb: response.data.memory_mb,\n            gpu_memory_mb: response.data.gpu_memory_mb,\n            gpu_utilization_percent: response.data.gpu_utilization_percent,\n            // 添加模板信息（只有最后一个模型会有）\n            template_info: response.data.template_info,\n            result_path: response.data.result_path,\n          });\n\n          if (modelsToPredict.length > 1) {\n            message.success(`✅ 模型 ${model.model_file} 预测成功`);\n\n            // 如果是最后一个模型且生成了模板，显示模板生成信息\n            if (i === modelsToPredict.length - 1 && response.data.template_info) {\n              if (response.data.template_info.template_generated) {\n                message.success({\n                  content: (\n                    <div>\n                      <div style={{ fontWeight: 'bold', marginBottom: 4 }}>\n                        🎉 清洗模板已自动生成！\n                      </div>\n                      <div style={{ fontSize: '12px', color: '#666' }}>\n                        📁 文件路径: {response.data.template_info.template_path}<br/>\n                        🔧 更新阈值: {response.data.template_info.updated_thresholds} 个\n                      </div>\n                    </div>\n                  ),\n                  duration: 6,\n                });\n              } else {\n                message.error(`清洗模板生成失败: ${response.data.template_info.error}`);\n              }\n            }\n          }\n        }\n      }\n\n      setProgress(100);\n      setResults(allResults);\n      message.success(`✅ 共完成 ${allResults.length} 个模型的预测`);\n\n    } catch (error: any) {\n      message.error(error.response?.data?.detail || '预测失败');\n    } finally {\n      setPredicting(false);\n    }\n  };\n\n  const isFormValid = () => {\n    const hasData = dataSource === 'upload' ? uploadedFile : (csvDir && selectedCsvFile);\n\n    if (predictionMode === 'single') {\n      return hasData && selectedModelFile && selectedProt && selectedDatatype;\n    } else {\n      return hasData && selectedModels.length > 0;\n    }\n  };\n\n\n\n  return (\n    <div>\n      <Title level={3} style={{ fontSize: '20px', fontWeight: 600, marginBottom: '8px' }}>模型预测</Title>\n      <Text type=\"secondary\">\n        加载已训练的流量模型，对新数据进行预测，输出流量清洗阈值。\n      </Text>\n\n      <Divider />\n\n      {/* 流量数据源 */}\n      <Card title=\"流量数据源\" className=\"function-card\">\n        <Space direction=\"vertical\" size=\"large\" style={{ width: '100%' }}>\n          <div>\n            <Text strong>预测数据源：</Text>\n            <Radio.Group\n              value={dataSource}\n              onChange={(e) => setDataSource(e.target.value)}\n              style={{ marginTop: 8 }}\n            >\n              <Radio value=\"local\">选择本地CSV文件</Radio>\n              <Radio value=\"upload\">上传CSV文件</Radio>\n            </Radio.Group>\n          </div>\n\n          {/* 本地文件选择 */}\n          {dataSource === 'local' && (\n            <Space direction=\"vertical\" style={{ width: '100%' }}>\n              <div>\n                <Text strong>CSV文件目录：</Text>\n                <Input.Group compact style={{ marginTop: 8, display: 'flex' }}>\n                  <Input\n                    value={csvDir}\n                    onChange={(e) => setCsvDir(e.target.value)}\n                    placeholder=\"例如: /data/output\"\n                    style={{ flex: 1 }}\n                  />\n                  <Button\n                    type=\"primary\"\n                    onClick={fetchCsvFiles}\n                    loading={csvFilesLoading}\n                    disabled={!csvDir}\n                    style={{ marginLeft: 8 }}\n                  >\n                    刷新\n                  </Button>\n                </Input.Group>\n              </div>\n\n              <div>\n                <Text strong>选择CSV文件：</Text>\n                <Spin spinning={csvFilesLoading}>\n                  <Select\n                    value={selectedCsvFile}\n                    onChange={setSelectedCsvFile}\n                    placeholder=\"请选择CSV文件\"\n                    style={{ width: '100%', marginTop: 8 }}\n                    loading={csvFilesLoading}\n                  >\n                    {availableCsvFiles.map((file) => (\n                      <Option key={file} value={file}>\n                        {file}\n                      </Option>\n                    ))}\n                  </Select>\n                </Spin>\n              </div>\n            </Space>\n          )}\n\n          {/* 文件上传 */}\n          {dataSource === 'upload' && (\n            <div>\n              <Text strong>上传待预测的CSV文件：</Text>\n              <Dragger {...uploadProps} style={{ marginTop: 8 }}>\n                <p className=\"ant-upload-drag-icon\">\n                  <InboxOutlined />\n                </p>\n                <p className=\"ant-upload-text\">点击或拖拽CSV文件到此区域上传</p>\n                <p className=\"ant-upload-hint\">\n                  支持CSV格式的流量数据文件\n                </p>\n              </Dragger>\n            </div>\n          )}\n        </Space>\n      </Card>\n\n      {/* 模型选择 */}\n      <Card title=\"模型选择\" className=\"function-card\">\n        <Space direction=\"vertical\" size=\"large\" style={{ width: '100%' }}>\n          <div>\n            <Card\n              size=\"small\"\n              title={\n                <Space>\n                  <InboxOutlined />\n                  <Text strong>模型保存路径</Text>\n                </Space>\n              }\n            >\n              <Input.Group compact style={{ display: 'flex' }}>\n                <Input\n                  value={modelDir}\n                  onChange={(e) => setModelDir(e.target.value)}\n                  placeholder=\"例如: /data/output\"\n                  size=\"large\"\n                  prefix={<InboxOutlined />}\n                  style={{ flex: 1 }}\n                />\n                <Button\n                  type=\"primary\"\n                  onClick={fetchModelFiles}\n                  loading={modelsLoading}\n                  disabled={!modelDir}\n                  size=\"large\"\n                  style={{ marginLeft: 8 }}\n                >\n                  刷新\n                </Button>\n              </Input.Group>\n            </Card>\n          </div>\n\n          <div>\n            <Text strong>预测模式：</Text>\n            <Radio.Group\n              value={predictionMode}\n              onChange={(e) => setPredictionMode(e.target.value)}\n              style={{ marginTop: 8 }}\n            >\n              <Radio value=\"single\">单个模型预测</Radio>\n              <Radio value=\"multiple\">多个模型批量预测</Radio>\n            </Radio.Group>\n          </div>\n\n          {predictionMode === 'single' ? (\n            <div>\n              <Text strong>选择模型文件：</Text>\n              <Spin spinning={modelsLoading}>\n                <Select\n                  value={selectedModelFile}\n                  onChange={handleModelFileChange}\n                  placeholder=\"选择一个训练好的模型文件，系统将自动匹配对应的参数和标准化器文件\"\n                  style={{ width: '100%', marginTop: 8 }}\n                  loading={modelsLoading}\n                >\n                  {availablePthFiles.map((file) => (\n                    <Option key={file} value={file}>\n                      {file}\n                    </Option>\n                  ))}\n                </Select>\n              </Spin>\n\n              {selectedModelFile && (\n                <div style={{ marginTop: 16 }}>\n                  <Space direction=\"vertical\" style={{ width: '100%' }}>\n                    <div>\n                      <Text type=\"secondary\">自动匹配的文件：</Text>\n                      <Spin spinning={matchingFilesLoading}>\n                        <div style={{ marginTop: 8, padding: 12, backgroundColor: '#f5f5f5', borderRadius: 4 }}>\n                          {matchingFilesLoading ? (\n                            <p>正在自动匹配相关文件...</p>\n                          ) : (\n                            <>\n                              <p><strong>参数文件:</strong> {selectedParamsFile || '未匹配'}</p>\n                              <p><strong>标准化器文件:</strong> {selectedScalerFile || '未匹配'}</p>\n                              {!showManualSelection && selectedProt && selectedDatatype && (\n                                <>\n                                  <p><strong>协议:</strong> {selectedProt}</p>\n                                  <p><strong>数据类型:</strong> {selectedDatatype}</p>\n                                </>\n                              )}\n                            </>\n                          )}\n                        </div>\n                      </Spin>\n                    </div>\n\n                    {showManualSelection && (\n                      <div>\n                        <Text strong>请手动选择协议和数据类型：</Text>\n                        <div style={{ marginTop: 8 }}>\n                          <Space direction=\"vertical\" style={{ width: '100%' }}>\n                            <Select\n                              value={selectedProt}\n                              onChange={setSelectedProt}\n                              placeholder=\"选择与模型对应的协议\"\n                              style={{ width: '100%' }}\n                            >\n                              {protocolOptions.map((prot) => (\n                                <Option key={prot} value={prot}>\n                                  {prot}\n                                </Option>\n                              ))}\n                            </Select>\n\n                            {selectedProt && (\n                              <Select\n                                value={selectedDatatype}\n                                onChange={setSelectedDatatype}\n                                placeholder={`选择与模型对应的 ${selectedProt} 数据类型`}\n                                style={{ width: '100%' }}\n                              >\n                                {(datatypeOptions[selectedProt as keyof typeof datatypeOptions] || []).map((datatype) => (\n                                  <Option key={datatype} value={datatype}>\n                                    {datatype}\n                                  </Option>\n                                ))}\n                              </Select>\n                            )}\n                          </Space>\n                        </div>\n                      </div>\n                    )}\n                  </Space>\n                </div>\n              )}\n            </div>\n          ) : (\n            <div>\n              <Text strong>选择模型文件（多选）：</Text>\n              <Spin spinning={modelsLoading}>\n                <Select\n                  mode=\"multiple\"\n                  value={selectedModels}\n                  onChange={setSelectedModels}\n                  placeholder=\"请选择多个模型文件进行批量预测\"\n                  style={{ width: '100%', marginTop: 8 }}\n                  loading={modelsLoading}\n                >\n                  {availablePthFiles.map((file) => (\n                    <Option key={file} value={file}>\n                      {file}\n                    </Option>\n                  ))}\n                </Select>\n              </Spin>\n            </div>\n          )}\n\n          {availablePthFiles.length === 0 && !modelsLoading && (\n            <Alert\n              message=\"未找到模型文件\"\n              description=\"请确保模型目录中包含训练好的模型文件（.pth）及其对应的参数文件和标准化器文件。\"\n              type=\"warning\"\n              showIcon\n            />\n          )}\n        </Space>\n      </Card>\n\n      {/* 预测模式选择 */}\n      <Card className=\"function-card\" title=\"预测模式\">\n        <Space direction=\"vertical\" size=\"middle\" style={{ width: '100%' }}>\n          <div>\n            <Text strong>选择预测模式：</Text>\n            <Radio.Group\n              value={useAsyncPrediction}\n              onChange={(e) => setUseAsyncPrediction(e.target.value)}\n              style={{ marginTop: 8 }}\n              // 现在多模型预测也支持异步了\n            >\n              <Radio value={true}>\n                <Space>\n                  异步预测（推荐）\n                  <Text type=\"secondary\" style={{ fontSize: 12 }}>\n                    - 后台运行，不阻塞其他操作\n                  </Text>\n                </Space>\n              </Radio>\n              <Radio value={false}>\n                <Space>\n                  同步预测\n                  <Text type=\"secondary\" style={{ fontSize: 12 }}>\n                    - 等待预测完成，期间无法使用其他功能\n                  </Text>\n                </Space>\n              </Radio>\n            </Radio.Group>\n          </div>\n\n          {useAsyncPrediction && (\n            <Alert\n              message=\"异步预测模式\"\n              description={\n                <div>\n                  预测任务将在后台运行，您可以继续使用系统的其他功能。\n                  <br />\n                  任务完成后会收到通知，可在 <strong>侧边栏菜单 → 任务管理</strong> 中查看进度和结果。\n                </div>\n              }\n              type=\"info\"\n              showIcon\n            />\n          )}\n        </Space>\n      </Card>\n\n      {/* 清洗模板生成选项 */}\n      <Card className=\"function-card\" title=\"清洗模板生成\">\n        <Space direction=\"vertical\" size=\"middle\" style={{ width: '100%' }}>\n          <div>\n            <Text strong>自动生成清洗模板：</Text>\n            <div style={{ marginTop: 8 }}>\n              <Checkbox\n                checked={autoGenerateTemplate}\n                onChange={(e) => setAutoGenerateTemplate(e.target.checked)}\n              >\n                预测完成后自动生成清洗模板\n              </Checkbox>\n            </div>\n            <div style={{ marginTop: 8 }}>\n              <Text type=\"secondary\" style={{ fontSize: '12px' }}>\n                选择此选项后，系统将在模型预测完成后自动调用清洗模板生成功能，\n                根据预测结果中的阈值信息生成相应的清洗模板文件。\n              </Text>\n            </div>\n          </div>\n        </Space>\n      </Card>\n\n      {/* 开始预测按钮 */}\n      <Card className=\"function-card\">\n        <Button\n          type=\"primary\"\n          size=\"large\"\n          icon={<PlayCircleOutlined />}\n          onClick={handleStartPrediction}\n          loading={predicting}\n          disabled={!isFormValid()}\n          className=\"action-button\"\n        >\n          {predicting ? '正在预测...' : '开始预测与检测'}\n        </Button>\n\n        {/* 预测进度 */}\n        {predicting && (\n          <div className=\"progress-section\">\n            <Text>预测进度：</Text>\n            <Progress percent={progress} status=\"active\" />\n          </div>\n        )}\n      </Card>\n\n      {/* 预测结果 */}\n      {results.length > 0 && (\n        <Card title=\"预测结果\" className=\"function-card\">\n          {results.length > 1 ? (\n            // 多模型结果展示 - 与Streamlit版本一致\n            <div>\n              <Divider />\n              <Title level={4}>多模型预测结果</Title>\n\n              {/* 显示模板生成状态 - 查找最后一个模型的模板信息 */}\n              {(() => {\n                const lastResult = results[results.length - 1];\n                if (autoGenerateTemplate && lastResult?.template_info) {\n                  return (\n                    <div style={{ marginBottom: 16 }}>\n                      {lastResult.template_info.template_generated ? (\n                        <Alert\n                          message=\"✅ 清洗模板已自动生成\"\n                          description={\n                            <div>\n                              <p style={{ margin: 0 }}><strong>模板路径:</strong> {lastResult.template_info.template_path}</p>\n                              <p style={{ margin: '4px 0 0 0' }}><strong>更新阈值数量:</strong> {lastResult.template_info.updated_thresholds} 个</p>\n                              <p style={{ margin: '4px 0 0 0', fontSize: '12px', color: '#666' }}>\n                                基于 {results.length} 个模型的预测结果生成\n                              </p>\n                            </div>\n                          }\n                          type=\"success\"\n                          showIcon\n                          style={{ marginBottom: 16 }}\n                        />\n                      ) : (\n                        <Alert\n                          message=\"❌ 清洗模板生成失败\"\n                          description={lastResult.template_info.error}\n                          type=\"error\"\n                          showIcon\n                          style={{ marginBottom: 16 }}\n                        />\n                      )}\n                    </div>\n                  );\n                }\n                return null;\n              })()}\n\n              <div style={{ marginBottom: 24 }}>\n                <Text strong>选择要查看的模型结果：</Text>\n                <Select\n                  style={{ width: '100%', marginTop: 8 }}\n                  placeholder=\"选择模型结果\"\n                  value={selectedResultIndex}\n                  onChange={(value) => setSelectedResultIndex(value)}\n                >\n                  {results.map((result, index) => (\n                    <Option key={index} value={index}>\n                      {result.model_name}\n                    </Option>\n                  ))}\n                </Select>\n              </div>\n\n              {/* 显示选中的模型结果 */}\n              {results[selectedResultIndex] && (\n                <div>\n                  <Title level={5}>模型: {results[selectedResultIndex].model_name}</Title>\n                  <PredictionResultDisplay result={results[selectedResultIndex]} />\n                </div>\n              )}\n            </div>\n          ) : (\n            // 单模型结果展示\n            <div>\n              <Title level={4}>预测结果 - {results[0].model_name}</Title>\n              <PredictionResultDisplay result={results[0]} />\n            </div>\n          )}\n        </Card>\n      )}\n\n      {/* 异步预测结果展示 */}\n      {completedPredictionTasks.length > 0 && (\n        <Card title=\"异步预测结果\" className=\"function-card\" style={{ marginTop: 24 }}>\n          <Alert\n            message=\"异步预测已完成\"\n            description=\"以下是后台预测任务的结果，您可以查看预测结果。\"\n            type=\"success\"\n            showIcon\n            style={{ marginBottom: 16 }}\n          />\n          <Space direction=\"vertical\" size=\"large\" style={{ width: '100%' }}>\n            {/* 任务选择器 */}\n            <div>\n              <Text strong>选择预测任务：</Text>\n              <Select\n                value={selectedAsyncTaskId}\n                onChange={handleAsyncTaskSelect}\n                style={{ width: '100%', marginTop: 8 }}\n                placeholder=\"请选择要查看的预测任务\"\n              >\n                {completedPredictionTasks.map((task) => (\n                  <Option key={task.task_id} value={task.task_id}>\n                    {task.task_id.includes('_') ?\n                      `${task.task_id.split('_')[0]} (${new Date(task.updated_at || task.created_at).toLocaleString()})` :\n                      `任务 ${task.task_id.substring(0, 8)}... (${new Date(task.updated_at || task.created_at).toLocaleString()})`\n                    }\n                  </Option>\n                ))}\n              </Select>\n            </div>\n\n            {/* 结果展示 */}\n            {asyncPredictionResults.length > 0 && (\n              <div>\n                {asyncPredictionResults.length > 1 ? (\n                  // 多模型异步预测结果展示 - 与同步多模型预测保持一致\n                  <div>\n                    <Divider />\n                    <Title level={4}>多模型预测结果</Title>\n\n                    {/* 清洗模板生成信息 */}\n                    {asyncTemplateInfo && (\n                      <div style={{ marginBottom: 16 }}>\n                        {asyncTemplateInfo.template_generated ? (\n                          <Alert\n                            message=\"✅ 清洗模板已自动生成\"\n                            description={\n                              <div>\n                                📁 文件路径: {asyncTemplateInfo.template_path}<br/>\n                                🔧 更新阈值: {asyncTemplateInfo.updated_thresholds} 个\n                              </div>\n                            }\n                            type=\"success\"\n                            showIcon\n                            style={{ marginBottom: 16 }}\n                          />\n                        ) : (\n                          <Alert\n                            message=\"❌ 清洗模板生成失败\"\n                            description={asyncTemplateInfo.error}\n                            type=\"error\"\n                            showIcon\n                            style={{ marginBottom: 16 }}\n                          />\n                        )}\n                      </div>\n                    )}\n\n                    <div style={{ marginBottom: 24 }}>\n                      <Text strong>选择要查看的模型结果：</Text>\n                      <Select\n                        style={{ width: '100%', marginTop: 8 }}\n                        placeholder=\"选择模型结果\"\n                        value={selectedAsyncResultIndex}\n                        onChange={(value) => setSelectedAsyncResultIndex(value)}\n                      >\n                        {asyncPredictionResults.map((result, index) => (\n                          <Option key={index} value={index}>\n                            {result.model_name}\n                          </Option>\n                        ))}\n                      </Select>\n                    </div>\n\n                    {/* 显示选中的模型结果 */}\n                    {asyncPredictionResults[selectedAsyncResultIndex] && (\n                      <div>\n                        <Title level={5}>模型: {asyncPredictionResults[selectedAsyncResultIndex].model_name}</Title>\n                        <PredictionResultDisplay result={asyncPredictionResults[selectedAsyncResultIndex]} />\n                      </div>\n                    )}\n                  </div>\n                ) : (\n                  // 单模型异步预测结果展示\n                  <div>\n                    <Title level={4}>预测结果 - {asyncPredictionResults[0].model_name}</Title>\n                    <PredictionResultDisplay result={asyncPredictionResults[0]} />\n                  </div>\n                )}\n              </div>\n            )}\n          </Space>\n        </Card>\n      )}\n    </div>\n  );\n};\n\nexport default ModelPredictionPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC/D,SACEC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,KAAK,EACLC,MAAM,EACNC,MAAM,EACNC,UAAU,EACVC,KAAK,EACLC,OAAO,EACPC,OAAO,EACPC,IAAI,EACJC,KAAK,EACLC,SAAS,EACTC,GAAG,EACHC,GAAG,EACHC,QAAQ,EACRC,QAAQ,QACH,MAAM;AACb,SAASC,aAAa,EAAEC,kBAAkB,QAAQ,mBAAmB;AAErE,SAASC,kBAAkB,QAAQ,iBAAiB;AACpD,OAAOC,cAAc,MAAM,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAErD,MAAM;EAAEC,KAAK;EAAEC;AAAK,CAAC,GAAGpB,UAAU;AAClC,MAAM;EAAEqB;AAAQ,CAAC,GAAGzB,MAAM;AAC1B,MAAM;EAAE0B;AAAO,CAAC,GAAGxB,MAAM;;AAEzB;AACA,MAAMyB,uBAA+D,GAAGA,CAAC;EAAEC;AAAO,CAAC,KAAK;EAGtF,oBACER,OAAA;IAAAS,QAAA,gBACET,OAAA,CAACT,GAAG;MAACmB,MAAM,EAAE,EAAG;MAACC,KAAK,EAAE;QAAEC,YAAY,EAAE;MAAG,CAAE;MAAAH,QAAA,eAC3CT,OAAA,CAACR,GAAG;QAACqB,IAAI,EAAE,EAAG;QAAAJ,QAAA,gBACZT,OAAA,CAACV,SAAS;UACRwB,KAAK,EAAC,8DAAiB;UACvBC,KAAK,EAAEP,MAAM,CAACQ,mBAAoB;UAClCC,SAAS,EAAE,CAAE;UACbC,UAAU,EAAE;YAAEC,KAAK,EAAE;UAAU;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CAAC,EACDf,MAAM,CAACQ,mBAAmB,iBACzBhB,OAAA,CAACX,KAAK;UACJF,OAAO,EAAC,kNAAwC;UAChDqC,IAAI,EAAC,SAAS;UACdC,QAAQ;UACRd,KAAK,EAAE;YAAEe,SAAS,EAAE;UAAE;QAAE;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB,CACF;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEH,CAAC,EAKL,CAACf,MAAM,CAACmB,gBAAgB,KAAKC,SAAS,IAAIpB,MAAM,CAACqB,WAAW,KAAKD,SAAS,IAAIpB,MAAM,CAACsB,SAAS,KAAKF,SAAS,IAAIpB,MAAM,CAACuB,aAAa,KAAKH,SAAS,IAAIpB,MAAM,CAACwB,uBAAuB,KAAKJ,SAAS,kBACjM5B,OAAA;MAAKW,KAAK,EAAE;QAAEC,YAAY,EAAE;MAAG,CAAE;MAAAH,QAAA,gBAC/BT,OAAA,CAACG,KAAK;QAAC8B,KAAK,EAAE,CAAE;QAAAxB,QAAA,EAAC;MAAM;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eAC/BvB,OAAA,CAACT,GAAG;QAACmB,MAAM,EAAE,EAAG;QAAAD,QAAA,GACbD,MAAM,CAACmB,gBAAgB,KAAKC,SAAS,iBACpC5B,OAAA,CAACR,GAAG;UAAC0C,IAAI,EAAC,MAAM;UAAAzB,QAAA,eACdT,OAAA,CAACV,SAAS;YACRwB,KAAK,EAAC,0BAAM;YACZC,KAAK,EAAEP,MAAM,CAACmB,gBAAiB;YAC/BV,SAAS,EAAE,CAAE;YACbkB,MAAM,EAAC,QAAG;YACVjB,UAAU,EAAE;cAAEC,KAAK,EAAE;YAAU;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN,EACAf,MAAM,CAACqB,WAAW,KAAKD,SAAS,iBAC/B5B,OAAA,CAACR,GAAG;UAAC0C,IAAI,EAAC,MAAM;UAAAzB,QAAA,eACdT,OAAA,CAACV,SAAS;YACRwB,KAAK,EAAC,uBAAQ;YACdC,KAAK,EAAEP,MAAM,CAACqB,WAAY;YAC1BZ,SAAS,EAAE,CAAE;YACbkB,MAAM,EAAC,GAAG;YACVjB,UAAU,EAAE;cAAEC,KAAK,EAAE;YAAU;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN,EACAf,MAAM,CAACsB,SAAS,KAAKF,SAAS,iBAC7B5B,OAAA,CAACR,GAAG;UAAC0C,IAAI,EAAC,MAAM;UAAAzB,QAAA,eACdT,OAAA,CAACV,SAAS;YACRwB,KAAK,EAAC,0BAAM;YACZC,KAAK,EAAEP,MAAM,CAACsB,SAAU;YACxBb,SAAS,EAAE,CAAE;YACbkB,MAAM,EAAC,IAAI;YACXjB,UAAU,EAAE;cAAEC,KAAK,EAAE;YAAU;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN,EACAf,MAAM,CAACuB,aAAa,KAAKH,SAAS,iBACjC5B,OAAA,CAACR,GAAG;UAAC0C,IAAI,EAAC,MAAM;UAAAzB,QAAA,eACdT,OAAA,CAACV,SAAS;YACRwB,KAAK,EAAC,iBAAO;YACbC,KAAK,EAAEP,MAAM,CAACuB,aAAc;YAC5Bd,SAAS,EAAE,CAAE;YACbkB,MAAM,EAAC,IAAI;YACXjB,UAAU,EAAE;cAAEC,KAAK,EAAE;YAAU;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN,EACAf,MAAM,CAACwB,uBAAuB,KAAKJ,SAAS,iBAC3C5B,OAAA,CAACR,GAAG;UAAC0C,IAAI,EAAC,MAAM;UAAAzB,QAAA,eACdT,OAAA,CAACV,SAAS;YACRwB,KAAK,EAAC,uBAAQ;YACdC,KAAK,EAAEP,MAAM,CAACwB,uBAAwB;YACtCf,SAAS,EAAE,CAAE;YACbkB,MAAM,EAAC,GAAG;YACVjB,UAAU,EAAE;cAAEC,KAAK,EAAE;YAAU;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAGAf,MAAM,CAAC4B,aAAa,iBACnBpC,OAAA;MAAKW,KAAK,EAAE;QAAEC,YAAY,EAAE;MAAG,CAAE;MAAAH,QAAA,gBAC/BT,OAAA,CAACG,KAAK;QAAC8B,KAAK,EAAE,CAAE;QAAAxB,QAAA,EAAC;MAAM;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eAC/BvB,OAAA;QAAKW,KAAK,EAAE;UAAE0B,OAAO,EAAE,EAAE;UAAEC,eAAe,EAAE9B,MAAM,CAAC4B,aAAa,CAACG,kBAAkB,GAAG,SAAS,GAAG,SAAS;UAAEC,MAAM,EAAE,aAAahC,MAAM,CAAC4B,aAAa,CAACG,kBAAkB,GAAG,SAAS,GAAG,SAAS,EAAE;UAAEE,YAAY,EAAE;QAAE,CAAE;QAAAhC,QAAA,EACpND,MAAM,CAAC4B,aAAa,CAACG,kBAAkB,gBACtCvC,OAAA;UAAAS,QAAA,gBACET,OAAA;YAAGW,KAAK,EAAE;cAAEQ,KAAK,EAAE,SAAS;cAAEuB,UAAU,EAAE,MAAM;cAAEC,MAAM,EAAE;YAAE,CAAE;YAAAlC,QAAA,EAAC;UAAW;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,EAC7Ef,MAAM,CAAC4B,aAAa,CAACQ,aAAa,iBACjC5C,OAAA;YAAGW,KAAK,EAAE;cAAEgC,MAAM,EAAE;YAAY,CAAE;YAAAlC,QAAA,gBAACT,OAAA;cAAAS,QAAA,EAAQ;YAAK;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACf,MAAM,CAAC4B,aAAa,CAACQ,aAAa;UAAA;YAAAxB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAClG,EACAf,MAAM,CAAC4B,aAAa,CAACS,kBAAkB,KAAKjB,SAAS,iBACpD5B,OAAA;YAAGW,KAAK,EAAE;cAAEgC,MAAM,EAAE;YAAY,CAAE;YAAAlC,QAAA,gBAACT,OAAA;cAAAS,QAAA,EAAQ;YAAO;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACf,MAAM,CAAC4B,aAAa,CAACS,kBAAkB;UAAA;YAAAzB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CACzG;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,gBAENvB,OAAA;UAAAS,QAAA,gBACET,OAAA;YAAGW,KAAK,EAAE;cAAEQ,KAAK,EAAE,SAAS;cAAEuB,UAAU,EAAE,MAAM;cAAEC,MAAM,EAAE;YAAE,CAAE;YAAAlC,QAAA,EAAC;UAAU;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,EAC5Ef,MAAM,CAAC4B,aAAa,CAACU,KAAK,iBACzB9C,OAAA;YAAGW,KAAK,EAAE;cAAEgC,MAAM,EAAE;YAAY,CAAE;YAAAlC,QAAA,gBAACT,OAAA;cAAAS,QAAA,EAAQ;YAAK;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACf,MAAM,CAAC4B,aAAa,CAACU,KAAK;UAAA;YAAA1B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAC1F;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAEE,CAAC;AAEV,CAAC;;AAED;AAAAwB,EAAA,GA1HMxC,uBAA+D;AA2HrE,MAAMyC,eAAe,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,CAAC;AAC9C,MAAMC,eAAe,GAAG;EACtBC,GAAG,EAAE,CAAC,aAAa,EAAE,aAAa,EAAE,aAAa,EAAE,aAAa,CAAC;EACjEC,GAAG,EAAE,CAAC,aAAa,EAAE,aAAa,CAAC;EACnCC,IAAI,EAAE,CAAC,KAAK;AACd,CAAC;AAsBD,MAAMC,mBAA6B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC1C,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGjF,QAAQ,CAAqB,OAAO,CAAC;EACzE,MAAM,CAACkF,YAAY,EAAEC,eAAe,CAAC,GAAGnF,QAAQ,CAAM,IAAI,CAAC;EAC3D,MAAM,CAACoF,MAAM,EAAEC,SAAS,CAAC,GAAGrF,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACsF,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGvF,QAAQ,CAAW,EAAE,CAAC;EACxE,MAAM,CAACwF,eAAe,EAAEC,kBAAkB,CAAC,GAAGzF,QAAQ,CAAS,EAAE,CAAC;EAClE,MAAM,CAAC0F,eAAe,EAAEC,kBAAkB,CAAC,GAAG3F,QAAQ,CAAC,KAAK,CAAC;;EAE7D;EACA,MAAM,CAAC4F,QAAQ,EAAEC,WAAW,CAAC,GAAG7F,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAAC8F,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG/F,QAAQ,CAAW,EAAE,CAAC;EACxE,MAAM,CAACgG,cAAc,EAAEC,iBAAiB,CAAC,GAAGjG,QAAQ,CAAW,EAAE,CAAC;EAClE,MAAM,CAACkG,aAAa,EAAEC,gBAAgB,CAAC,GAAGnG,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACoG,cAAc,EAAEC,iBAAiB,CAAC,GAAGrG,QAAQ,CAAwB,QAAQ,CAAC;;EAErF;EACA,MAAM,CAACsG,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGvG,QAAQ,CAAS,EAAE,CAAC;EACtE,MAAM,CAACwG,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGzG,QAAQ,CAAS,EAAE,CAAC;EACxE,MAAM,CAAC0G,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG3G,QAAQ,CAAS,EAAE,CAAC;EACxE,MAAM,CAAC4G,YAAY,EAAEC,eAAe,CAAC,GAAG7G,QAAQ,CAAS,EAAE,CAAC;EAC5D,MAAM,CAAC8G,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG/G,QAAQ,CAAS,EAAE,CAAC;EACpE,MAAM,CAACgH,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGjH,QAAQ,CAAC,KAAK,CAAC;;EAErE;EACA,MAAM,CAACkH,UAAU,EAAEC,aAAa,CAAC,GAAGnH,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACoH,QAAQ,EAAEC,WAAW,CAAC,GAAGrH,QAAQ,CAAC,CAAC,CAAC;EAC3C,MAAM,CAACsH,OAAO,EAAEC,UAAU,CAAC,GAAGvH,QAAQ,CAAqB,EAAE,CAAC;EAC9D,MAAM,CAACwH,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGzH,QAAQ,CAAS,CAAC,CAAC;EACzE,MAAM,CAAC0H,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG3H,QAAQ,CAAC,KAAK,CAAC;EACvE,MAAM,CAAC4H,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG7H,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;;EAEzE;EACA,MAAM;IAAE8H,oBAAoB;IAAEC,yBAAyB;IAAEC,uBAAuB;IAAEC;EAAoB,CAAC,GAAG1G,cAAc,CAAC,CAAC;EAC1H,MAAM,CAAC2G,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGnI,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;;EAEpE;EACA,MAAM,CAACoI,sBAAsB,EAAEC,yBAAyB,CAAC,GAAGrI,QAAQ,CAAqB,EAAE,CAAC;EAC5F,MAAM,CAACsI,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGvI,QAAQ,CAAS,EAAE,CAAC;EAC1E,MAAM,CAACwI,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGzI,QAAQ,CAAM,IAAI,CAAC;EACrE,MAAM,CAAC0I,wBAAwB,EAAEC,2BAA2B,CAAC,GAAG3I,QAAQ,CAAS,CAAC,CAAC;;EAEnF;EACA,MAAM4I,wBAAwB,GAAGZ,uBAAuB,CAAC,YAAY,CAAC;;EAEtE;EACA,MAAMa,qBAAqB,GAAG3I,WAAW,CAAE4I,MAAc,IAAK;IAC5DP,sBAAsB,CAACO,MAAM,CAAC;IAC9B,MAAMC,YAAY,GAAGH,wBAAwB,CAACI,IAAI,CAACC,IAAI,IAAIA,IAAI,CAACC,OAAO,KAAKJ,MAAM,CAAC;IAEnF,IAAIC,YAAY,IAAIA,YAAY,CAAC9G,MAAM,EAAE;MACvC;MACA,IAAI8G,YAAY,CAAC9G,MAAM,CAACkH,cAAc,IAAIJ,YAAY,CAAC9G,MAAM,CAACqF,OAAO,EAAE;QACrE;QACAe,yBAAyB,CAACU,YAAY,CAAC9G,MAAM,CAACqF,OAAO,CAAC;QACtDmB,oBAAoB,CAACM,YAAY,CAAC9G,MAAM,CAAC4B,aAAa,CAAC;MACzD,CAAC,MAAM;QACL;QACA,MAAMuF,WAA6B,GAAG;UACpC3G,mBAAmB,EAAEsG,YAAY,CAAC9G,MAAM,CAACQ,mBAAmB,IAAI,CAAC;UACjE4G,UAAU,EAAEN,YAAY,CAAC9G,MAAM,CAACoH,UAAU,IAAI,MAAM;UACpDzI,OAAO,EAAEmI,YAAY,CAAC9G,MAAM,CAACrB,OAAO,IAAI,MAAM;UAC9CwC,gBAAgB,EAAE2F,YAAY,CAAC9G,MAAM,CAACmB,gBAAgB;UACtDE,WAAW,EAAEyF,YAAY,CAAC9G,MAAM,CAACqB,WAAW;UAC5CC,SAAS,EAAEwF,YAAY,CAAC9G,MAAM,CAACsB,SAAS;UACxCC,aAAa,EAAEuF,YAAY,CAAC9G,MAAM,CAACuB,aAAa;UAChDC,uBAAuB,EAAEsF,YAAY,CAAC9G,MAAM,CAACwB,uBAAuB;UACpEI,aAAa,EAAEkF,YAAY,CAAC9G,MAAM,CAAC4B,aAAa;UAChDyF,WAAW,EAAEP,YAAY,CAAC9G,MAAM,CAACqH;QACnC,CAAC;QAEDjB,yBAAyB,CAAC,CAACe,WAAW,CAAC,CAAC;QACxCX,oBAAoB,CAACM,YAAY,CAAC9G,MAAM,CAAC4B,aAAa,CAAC;MACzD;IACF;EACF,CAAC,EAAE,CAAC+E,wBAAwB,CAAC,CAAC;;EAE9B;EACA3I,SAAS,CAAC,MAAM;IACdgI,mBAAmB,CAAC,CAAC;EACvB,CAAC,EAAE,CAACA,mBAAmB,CAAC,CAAC;;EAEzB;EACAhI,SAAS,CAAC,MAAM;IACd,IAAI2I,wBAAwB,CAACW,MAAM,GAAG,CAAC,IAAI,CAACjB,mBAAmB,EAAE;MAC/D,MAAMkB,UAAU,GAAGZ,wBAAwB,CAACA,wBAAwB,CAACW,MAAM,GAAG,CAAC,CAAC;MAChFV,qBAAqB,CAACW,UAAU,CAACN,OAAO,CAAC;IAC3C;EACF,CAAC,EAAE,CAACN,wBAAwB,EAAEN,mBAAmB,EAAEO,qBAAqB,CAAC,CAAC;;EAE1E;EACA,MAAMY,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI,CAACrE,MAAM,EAAE;IAEbO,kBAAkB,CAAC,IAAI,CAAC;IACxB,IAAI;MACF,MAAM+D,QAAQ,GAAG,MAAMpI,kBAAkB,CAACqI,YAAY,CAACvE,MAAM,CAAC;MAC9DG,oBAAoB,CAACmE,QAAQ,CAACE,IAAI,CAACC,KAAK,IAAI,EAAE,CAAC;IACjD,CAAC,CAAC,OAAOtF,KAAU,EAAE;MAAA,IAAAuF,eAAA,EAAAC,oBAAA;MACnBnJ,OAAO,CAAC2D,KAAK,CAAC,EAAAuF,eAAA,GAAAvF,KAAK,CAACmF,QAAQ,cAAAI,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBF,IAAI,cAAAG,oBAAA,uBAApBA,oBAAA,CAAsBC,MAAM,KAAI,aAAa,CAAC;MAC5DzE,oBAAoB,CAAC,EAAE,CAAC;IAC1B,CAAC,SAAS;MACRI,kBAAkB,CAAC,KAAK,CAAC;IAC3B;EACF,CAAC;;EAED;EACA,MAAMsE,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI,CAACrE,QAAQ,EAAE;IAEfO,gBAAgB,CAAC,IAAI,CAAC;IACtB,IAAI;MACF,MAAMuD,QAAQ,GAAG,MAAMpI,kBAAkB,CAAC4I,cAAc,CAACtE,QAAQ,CAAC;MAClEG,oBAAoB,CAAC2D,QAAQ,CAACE,IAAI,CAACO,SAAS,IAAI,EAAE,CAAC;IACrD,CAAC,CAAC,OAAO5F,KAAU,EAAE;MAAA,IAAA6F,gBAAA,EAAAC,qBAAA;MACnBzJ,OAAO,CAAC2D,KAAK,CAAC,EAAA6F,gBAAA,GAAA7F,KAAK,CAACmF,QAAQ,cAAAU,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBR,IAAI,cAAAS,qBAAA,uBAApBA,qBAAA,CAAsBL,MAAM,KAAI,YAAY,CAAC;MAC3DjE,oBAAoB,CAAC,EAAE,CAAC;IAC1B,CAAC,SAAS;MACRI,gBAAgB,CAAC,KAAK,CAAC;IACzB;EACF,CAAC;;EAED;EACA,MAAMmE,cAAc,GAAG,MAAOC,SAAiB,IAAK;IAClD,IAAI,CAACA,SAAS,IAAI,CAAC3E,QAAQ,EAAE;IAE7B+B,uBAAuB,CAAC,IAAI,CAAC;IAC7B,IAAI;MACF;MACA,MAAM+B,QAAQ,GAAG,MAAMpI,kBAAkB,CAACkJ,gBAAgB,CAACD,SAAS,EAAE3E,QAAQ,CAAC;MAE/E,IAAI8D,QAAQ,CAACE,IAAI,EAAE;QACjB,MAAMa,aAAa,GAAGf,QAAQ,CAACE,IAAI;;QAEnC;QACAnD,qBAAqB,CAACgE,aAAa,CAACC,eAAe,IAAI,EAAE,CAAC;QAC1D/D,qBAAqB,CAAC8D,aAAa,CAACE,eAAe,IAAI,EAAE,CAAC;QAC1D9D,eAAe,CAAC4D,aAAa,CAACG,QAAQ,IAAI,EAAE,CAAC;QAC7C7D,mBAAmB,CAAC0D,aAAa,CAACI,QAAQ,IAAI,EAAE,CAAC;;QAEjD;QACA,IAAIJ,aAAa,CAACC,eAAe,IAAID,aAAa,CAACE,eAAe,EAAE;UAClE/J,OAAO,CAACkK,OAAO,CAAC,aAAa,CAAC;QAChC;QAEA,IAAIL,aAAa,CAACG,QAAQ,IAAIH,aAAa,CAACI,QAAQ,EAAE;UACpDjK,OAAO,CAACkK,OAAO,CAAC,uBAAuBL,aAAa,CAACG,QAAQ,MAAMH,aAAa,CAACI,QAAQ,EAAE,CAAC;UAC5F5D,sBAAsB,CAAC,KAAK,CAAC;QAC/B,CAAC,MAAM;UACLrG,OAAO,CAACmK,OAAO,CAAC,4BAA4B,CAAC;UAC7C9D,sBAAsB,CAAC,IAAI,CAAC;QAC9B;MACF;IACF,CAAC,CAAC,OAAO1C,KAAU,EAAE;MAAA,IAAAyG,gBAAA,EAAAC,qBAAA;MACnBrK,OAAO,CAAC2D,KAAK,CAAC,EAAAyG,gBAAA,GAAAzG,KAAK,CAACmF,QAAQ,cAAAsB,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBpB,IAAI,cAAAqB,qBAAA,uBAApBA,qBAAA,CAAsBjB,MAAM,KAAI,UAAU,CAAC;MACzD;MACA,MAAMkB,kBAAkB,GAAGX,SAAS,CAACY,OAAO,CAAC,iBAAiB,EAAE,EAAE,CAAC;MACnE1E,qBAAqB,CAAC,GAAGyE,kBAAkB,cAAc,CAAC;MAC1DvE,qBAAqB,CAAC,GAAGuE,kBAAkB,eAAe,CAAC;MAC3DjE,sBAAsB,CAAC,IAAI,CAAC;IAC9B,CAAC,SAAS;MACRU,uBAAuB,CAAC,KAAK,CAAC;IAChC;EACF,CAAC;;EAED;EACA,MAAMyD,qBAAqB,GAAIb,SAAiB,IAAK;IACnDhE,oBAAoB,CAACgE,SAAS,CAAC;IAC/B;IACA9D,qBAAqB,CAAC,EAAE,CAAC;IACzBE,qBAAqB,CAAC,EAAE,CAAC;IACzBE,eAAe,CAAC,EAAE,CAAC;IACnBE,mBAAmB,CAAC,EAAE,CAAC;IACvBE,sBAAsB,CAAC,KAAK,CAAC;;IAE7B;IACAqD,cAAc,CAACC,SAAS,CAAC;EAC3B,CAAC;;EAED;EACAtK,SAAS,CAAC,MAAM;IACd,IAAI+E,UAAU,KAAK,OAAO,IAAII,MAAM,IAAIA,MAAM,CAACmE,MAAM,GAAG,CAAC,EAAE;MAAE;MAC3D,MAAM8B,KAAK,GAAGC,UAAU,CAAC,MAAM;QAC7B7B,aAAa,CAAC,CAAC;MACjB,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;;MAEV,OAAO,MAAM8B,YAAY,CAACF,KAAK,CAAC;IAClC;IACA;EACF,CAAC,EAAE,CAACrG,UAAU,EAAEI,MAAM,CAAC,CAAC;EAExBnF,SAAS,CAAC,MAAM;IACd,IAAI2F,QAAQ,IAAIA,QAAQ,CAAC2D,MAAM,GAAG,CAAC,EAAE;MAAE;MACrC,MAAM8B,KAAK,GAAGC,UAAU,CAAC,MAAM;QAC7BrB,eAAe,CAAC,CAAC;MACnB,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;;MAEV,OAAO,MAAMsB,YAAY,CAACF,KAAK,CAAC;IAClC;IACA;EACF,CAAC,EAAE,CAACzF,QAAQ,CAAC,CAAC;;EAEd;EACA,MAAM4F,WAAW,GAAG;IAClBC,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE,KAAK;IACfC,MAAM,EAAE,MAAM;IACdC,YAAY,EAAEA,CAAA,KAAM,KAAK;IACzBC,QAAQ,EAAGC,IAAS,IAAK;MACvB,IAAIA,IAAI,CAACC,QAAQ,CAACxC,MAAM,GAAG,CAAC,EAAE;QAC5BpE,eAAe,CAAC2G,IAAI,CAACC,QAAQ,CAAC,CAAC,CAAC,CAAC;MACnC,CAAC,MAAM;QACL5G,eAAe,CAAC,IAAI,CAAC;MACvB;IACF;EACF,CAAC;;EAED;EACA,MAAM6G,qBAAqB,GAAG,MAAAA,CAAA,KAAY;IACxC;IACA,IAAIhH,UAAU,KAAK,QAAQ,IAAI,CAACE,YAAY,EAAE;MAC5CtE,OAAO,CAAC2D,KAAK,CAAC,UAAU,CAAC;MACzB;IACF;IAEA,IAAIS,UAAU,KAAK,OAAO,KAAK,CAACI,MAAM,IAAI,CAACI,eAAe,CAAC,EAAE;MAC3D5E,OAAO,CAAC2D,KAAK,CAAC,UAAU,CAAC;MACzB;IACF;;IAEA;IACA,IAAI0H,eAMF,GAAG,EAAE;IAEP,IAAI7F,cAAc,KAAK,QAAQ,EAAE;MAC/B,IAAI,CAACE,iBAAiB,IAAI,CAACM,YAAY,IAAI,CAACE,gBAAgB,EAAE;QAC5DlG,OAAO,CAAC2D,KAAK,CAAC,sBAAsB,CAAC;QACrC;MACF;MACA0H,eAAe,GAAG,CAAC;QACjBC,UAAU,EAAE5F,iBAAiB;QAC7B6F,WAAW,EAAE3F,kBAAkB;QAC/B4F,WAAW,EAAE1F,kBAAkB;QAC/BkE,QAAQ,EAAEhE,YAAY;QACtBiE,QAAQ,EAAE/D;MACZ,CAAC,CAAC;IACJ,CAAC,MAAM;MACL,IAAId,cAAc,CAACuD,MAAM,KAAK,CAAC,EAAE;QAC/B3I,OAAO,CAAC2D,KAAK,CAAC,WAAW,CAAC;QAC1B;MACF;;MAEA;MACA,MAAM8H,WAMJ,GAAG,EAAE;MAEP,KAAK,MAAM9B,SAAS,IAAIvE,cAAc,EAAE;QACtC,IAAI;UACF,MAAM0D,QAAQ,GAAG,MAAMpI,kBAAkB,CAACkJ,gBAAgB,CAACD,SAAS,EAAE3E,QAAQ,CAAC;UAC/E,IAAI8D,QAAQ,CAACE,IAAI,EAAE;YACjB,MAAMa,aAAa,GAAGf,QAAQ,CAACE,IAAI;YACnC,MAAMuC,WAAW,GAAG1B,aAAa,CAACC,eAAe;YACjD,MAAM0B,WAAW,GAAG3B,aAAa,CAACE,eAAe;YACjD,MAAMC,QAAQ,GAAGH,aAAa,CAACG,QAAQ;YACvC,MAAMC,QAAQ,GAAGJ,aAAa,CAACI,QAAQ;;YAEvC;YACA,IAAIsB,WAAW,IAAIC,WAAW,IAAIxB,QAAQ,IAAIC,QAAQ,EAAE;cACtDwB,WAAW,CAACC,IAAI,CAAC;gBACfJ,UAAU,EAAE3B,SAAS;gBACrB4B,WAAW;gBACXC,WAAW;gBACXxB,QAAQ;gBACRC;cACF,CAAC,CAAC;cACFjK,OAAO,CAACkK,OAAO,CAAC,QAAQP,SAAS,SAASK,QAAQ,MAAMC,QAAQ,EAAE,CAAC;YACrE,CAAC,MAAM;cACLjK,OAAO,CAACmK,OAAO,CAAC,SAASR,SAAS,mBAAmB,CAAC;YACxD;UACF,CAAC,MAAM;YACL3J,OAAO,CAAC2D,KAAK,CAAC,QAAQgG,SAAS,UAAU,CAAC;UAC5C;QACF,CAAC,CAAC,OAAOhG,KAAU,EAAE;UAAA,IAAAgI,gBAAA,EAAAC,qBAAA;UACnB5L,OAAO,CAAC2D,KAAK,CAAC,QAAQgG,SAAS,SAAS,EAAAgC,gBAAA,GAAAhI,KAAK,CAACmF,QAAQ,cAAA6C,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgB3C,IAAI,cAAA4C,qBAAA,uBAApBA,qBAAA,CAAsBxC,MAAM,KAAIzF,KAAK,CAAC3D,OAAO,EAAE,CAAC;QAC1F;MACF;MAEA,IAAIyL,WAAW,CAAC9C,MAAM,KAAK,CAAC,EAAE;QAC5B3I,OAAO,CAAC2D,KAAK,CAAC,wCAAwC,CAAC;QACvD;MACF;MAEA0H,eAAe,GAAGI,WAAW;IAC/B;IAEAlF,aAAa,CAAC,IAAI,CAAC;IACnBE,WAAW,CAAC,CAAC,CAAC;IACdE,UAAU,CAAC,EAAE,CAAC;IAEd,IAAI;MACF,IAAIW,kBAAkB,EAAE;QACtB;QACA,MAAMuE,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;QAE/B,IAAI1H,UAAU,KAAK,QAAQ,IAAIE,YAAY,EAAE;UAC3CuH,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAEzH,YAAY,CAAC0H,aAAa,CAAC;QACrD,CAAC,MAAM;UACLH,QAAQ,CAACE,MAAM,CAAC,SAAS,EAAEvH,MAAM,CAAC;UAClCqH,QAAQ,CAACE,MAAM,CAAC,eAAe,EAAEnH,eAAe,CAAC;QACnD;QAEAiH,QAAQ,CAACE,MAAM,CAAC,WAAW,EAAE/G,QAAQ,CAAC;QACtC6G,QAAQ,CAACE,MAAM,CAAC,eAAe,EAAE/G,QAAQ,CAAC;QAC1C6G,QAAQ,CAACE,MAAM,CAAC,wBAAwB,EAAE/E,oBAAoB,CAACiF,QAAQ,CAAC,CAAC,CAAC;QAE1E,IAAI/D,MAA0B;QAE9B,IAAI1C,cAAc,KAAK,QAAQ,EAAE;UAC/B;UACAqG,QAAQ,CAACE,MAAM,CAAC,gBAAgB,EAAErG,iBAAiB,CAAC;UACpDmG,QAAQ,CAACE,MAAM,CAAC,iBAAiB,EAAEnG,kBAAkB,CAAC;UACtDiG,QAAQ,CAACE,MAAM,CAAC,iBAAiB,EAAEjG,kBAAkB,CAAC;UACtD+F,QAAQ,CAACE,MAAM,CAAC,eAAe,EAAE/F,YAAY,CAAC;UAC9C6F,QAAQ,CAACE,MAAM,CAAC,mBAAmB,EAAE7F,gBAAgB,CAAC;UAEtDgC,MAAM,GAAG,MAAMhB,oBAAoB,CAAC2E,QAAQ,CAAC;QAC/C,CAAC,MAAM;UACL;UACA,MAAMK,YAAY,GAAGb,eAAe,CAACc,GAAG,CAACC,KAAK,KAAK;YACjDd,UAAU,EAAEc,KAAK,CAACd,UAAU;YAC5BC,WAAW,EAAEa,KAAK,CAACb,WAAW;YAC9BC,WAAW,EAAEY,KAAK,CAACZ,WAAW;YAC9BxB,QAAQ,EAAEoC,KAAK,CAACpC,QAAQ;YACxBC,QAAQ,EAAEmC,KAAK,CAACnC;UAClB,CAAC,CAAC,CAAC;UAEH4B,QAAQ,CAACE,MAAM,CAAC,eAAe,EAAEM,IAAI,CAACC,SAAS,CAACJ,YAAY,CAAC,CAAC;UAC9DhE,MAAM,GAAG,MAAMf,yBAAyB,CAAC0E,QAAQ,CAAC;QACpD;QAEA,IAAI3D,MAAM,EAAE;UACV,MAAMqE,QAAQ,GAAG/G,cAAc,KAAK,QAAQ,GAAG,MAAM,GAAG,SAAS;UACjExF,OAAO,CAACkK,OAAO,CAAC,GAAGqC,QAAQ,4BAA4B,CAAC;UACxD;UACAhG,aAAa,CAAC,KAAK,CAAC;UACpBE,WAAW,CAAC,CAAC,CAAC;QAChB;QAEA,OAAO,CAAC;MACV;;MAEA;MACA,MAAM+F,UAA8B,GAAG,EAAE;MAEzC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGpB,eAAe,CAAC1C,MAAM,EAAE8D,CAAC,EAAE,EAAE;QAC/C,MAAML,KAAK,GAAGf,eAAe,CAACoB,CAAC,CAAC;;QAEhC;QACAhG,WAAW,CAACiG,IAAI,CAACC,KAAK,CAAEF,CAAC,GAAGpB,eAAe,CAAC1C,MAAM,GAAI,EAAE,CAAC,CAAC;QAE1D,IAAI0C,eAAe,CAAC1C,MAAM,GAAG,CAAC,EAAE;UAC9B3I,OAAO,CAACkL,IAAI,CAAC,UAAUuB,CAAC,GAAG,CAAC,IAAIpB,eAAe,CAAC1C,MAAM,KAAKyD,KAAK,CAACd,UAAU,UAAU,CAAC;QACxF;QAEA,MAAMO,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;QAE/B,IAAI1H,UAAU,KAAK,QAAQ,IAAIE,YAAY,EAAE;UAC3C;UACAuH,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAEzH,YAAY,CAAC0H,aAAa,CAAC;QACrD,CAAC,MAAM;UACLH,QAAQ,CAACE,MAAM,CAAC,SAAS,EAAEvH,MAAM,CAAC;UAClCqH,QAAQ,CAACE,MAAM,CAAC,eAAe,EAAEnH,eAAe,CAAC;QACnD;QAEAiH,QAAQ,CAACE,MAAM,CAAC,gBAAgB,EAAEK,KAAK,CAACd,UAAU,CAAC;QACnDO,QAAQ,CAACE,MAAM,CAAC,iBAAiB,EAAEK,KAAK,CAACb,WAAW,CAAC;QACrDM,QAAQ,CAACE,MAAM,CAAC,iBAAiB,EAAEK,KAAK,CAACZ,WAAW,CAAC;QACrDK,QAAQ,CAACE,MAAM,CAAC,eAAe,EAAEK,KAAK,CAACpC,QAAQ,CAAC;QAChD6B,QAAQ,CAACE,MAAM,CAAC,mBAAmB,EAAEK,KAAK,CAACnC,QAAQ,CAAC;QACpD4B,QAAQ,CAACE,MAAM,CAAC,eAAe,EAAE/G,QAAQ,CAAC;QAC1C;QACA6G,QAAQ,CAACE,MAAM,CAAC,kBAAkB,EAAE/E,oBAAoB,CAACiF,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;QACtEJ,QAAQ,CAACE,MAAM,CAAC,wBAAwB,EAAE,CAAC/E,oBAAoB,IAAIyF,CAAC,KAAKpB,eAAe,CAAC1C,MAAM,GAAG,CAAC,EAAEsD,QAAQ,CAAC,CAAC,CAAC;QAEhH,MAAMnD,QAAQ,GAAG,MAAMpI,kBAAkB,CAACkM,OAAO,CAACf,QAAQ,CAAC;QAE3D,IAAI/C,QAAQ,CAACE,IAAI,EAAE;UACjBwD,UAAU,CAACd,IAAI,CAAC;YACdjD,UAAU,EAAEK,QAAQ,CAACE,IAAI,CAACP,UAAU,IAAI,GAAG2D,KAAK,CAACpC,QAAQ,IAAIoC,KAAK,CAACnC,QAAQ,EAAE;YAC7EpI,mBAAmB,EAAEiH,QAAQ,CAACE,IAAI,CAACnH,mBAAmB,IAAI,CAAC;YAC3D7B,OAAO,EAAE8I,QAAQ,CAACE,IAAI,CAAChJ,OAAO,IAAI,MAAM;YACxC;YACAwC,gBAAgB,EAAEsG,QAAQ,CAACE,IAAI,CAACxG,gBAAgB;YAChDE,WAAW,EAAEoG,QAAQ,CAACE,IAAI,CAACtG,WAAW;YACtCC,SAAS,EAAEmG,QAAQ,CAACE,IAAI,CAACrG,SAAS;YAClCC,aAAa,EAAEkG,QAAQ,CAACE,IAAI,CAACpG,aAAa;YAC1CC,uBAAuB,EAAEiG,QAAQ,CAACE,IAAI,CAACnG,uBAAuB;YAC9D;YACAI,aAAa,EAAE6F,QAAQ,CAACE,IAAI,CAAC/F,aAAa;YAC1CyF,WAAW,EAAEI,QAAQ,CAACE,IAAI,CAACN;UAC7B,CAAC,CAAC;UAEF,IAAI2C,eAAe,CAAC1C,MAAM,GAAG,CAAC,EAAE;YAC9B3I,OAAO,CAACkK,OAAO,CAAC,QAAQkC,KAAK,CAACd,UAAU,OAAO,CAAC;;YAEhD;YACA,IAAImB,CAAC,KAAKpB,eAAe,CAAC1C,MAAM,GAAG,CAAC,IAAIG,QAAQ,CAACE,IAAI,CAAC/F,aAAa,EAAE;cACnE,IAAI6F,QAAQ,CAACE,IAAI,CAAC/F,aAAa,CAACG,kBAAkB,EAAE;gBAClDpD,OAAO,CAACkK,OAAO,CAAC;kBACd2C,OAAO,eACLhM,OAAA;oBAAAS,QAAA,gBACET,OAAA;sBAAKW,KAAK,EAAE;wBAAE+B,UAAU,EAAE,MAAM;wBAAE9B,YAAY,EAAE;sBAAE,CAAE;sBAAAH,QAAA,EAAC;oBAErD;sBAAAW,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNvB,OAAA;sBAAKW,KAAK,EAAE;wBAAEsL,QAAQ,EAAE,MAAM;wBAAE9K,KAAK,EAAE;sBAAO,CAAE;sBAAAV,QAAA,GAAC,yCACtC,EAACwH,QAAQ,CAACE,IAAI,CAAC/F,aAAa,CAACQ,aAAa,eAAC5C,OAAA;wBAAAoB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,2CAChD,EAAC0G,QAAQ,CAACE,IAAI,CAAC/F,aAAa,CAACS,kBAAkB,EAAC,SAC3D;oBAAA;sBAAAzB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CACN;kBACD2K,QAAQ,EAAE;gBACZ,CAAC,CAAC;cACJ,CAAC,MAAM;gBACL/M,OAAO,CAAC2D,KAAK,CAAC,aAAamF,QAAQ,CAACE,IAAI,CAAC/F,aAAa,CAACU,KAAK,EAAE,CAAC;cACjE;YACF;UACF;QACF;MACF;MAEA8C,WAAW,CAAC,GAAG,CAAC;MAChBE,UAAU,CAAC6F,UAAU,CAAC;MACtBxM,OAAO,CAACkK,OAAO,CAAC,SAASsC,UAAU,CAAC7D,MAAM,SAAS,CAAC;IAEtD,CAAC,CAAC,OAAOhF,KAAU,EAAE;MAAA,IAAAqJ,gBAAA,EAAAC,qBAAA;MACnBjN,OAAO,CAAC2D,KAAK,CAAC,EAAAqJ,gBAAA,GAAArJ,KAAK,CAACmF,QAAQ,cAAAkE,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBhE,IAAI,cAAAiE,qBAAA,uBAApBA,qBAAA,CAAsB7D,MAAM,KAAI,MAAM,CAAC;IACvD,CAAC,SAAS;MACR7C,aAAa,CAAC,KAAK,CAAC;IACtB;EACF,CAAC;EAED,MAAM2G,WAAW,GAAGA,CAAA,KAAM;IACxB,MAAMC,OAAO,GAAG/I,UAAU,KAAK,QAAQ,GAAGE,YAAY,GAAIE,MAAM,IAAII,eAAgB;IAEpF,IAAIY,cAAc,KAAK,QAAQ,EAAE;MAC/B,OAAO2H,OAAO,IAAIzH,iBAAiB,IAAIM,YAAY,IAAIE,gBAAgB;IACzE,CAAC,MAAM;MACL,OAAOiH,OAAO,IAAI/H,cAAc,CAACuD,MAAM,GAAG,CAAC;IAC7C;EACF,CAAC;EAID,oBACE9H,OAAA;IAAAS,QAAA,gBACET,OAAA,CAACG,KAAK;MAAC8B,KAAK,EAAE,CAAE;MAACtB,KAAK,EAAE;QAAEsL,QAAQ,EAAE,MAAM;QAAEvJ,UAAU,EAAE,GAAG;QAAE9B,YAAY,EAAE;MAAM,CAAE;MAAAH,QAAA,EAAC;IAAI;MAAAW,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC,eAChGvB,OAAA,CAACI,IAAI;MAACoB,IAAI,EAAC,WAAW;MAAAf,QAAA,EAAC;IAEvB;MAAAW,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eAEPvB,OAAA,CAACd,OAAO;MAAAkC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGXvB,OAAA,CAACtB,IAAI;MAACoC,KAAK,EAAC,gCAAO;MAACyL,SAAS,EAAC,eAAe;MAAA9L,QAAA,eAC3CT,OAAA,CAACf,KAAK;QAACuN,SAAS,EAAC,UAAU;QAACC,IAAI,EAAC,OAAO;QAAC9L,KAAK,EAAE;UAAE+L,KAAK,EAAE;QAAO,CAAE;QAAAjM,QAAA,gBAChET,OAAA;UAAAS,QAAA,gBACET,OAAA,CAACI,IAAI;YAACuM,MAAM;YAAAlM,QAAA,EAAC;UAAM;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC1BvB,OAAA,CAACrB,KAAK,CAACiO,KAAK;YACV7L,KAAK,EAAEwC,UAAW;YAClB6G,QAAQ,EAAGyC,CAAC,IAAKrJ,aAAa,CAACqJ,CAAC,CAACC,MAAM,CAAC/L,KAAK,CAAE;YAC/CJ,KAAK,EAAE;cAAEe,SAAS,EAAE;YAAE,CAAE;YAAAjB,QAAA,gBAExBT,OAAA,CAACrB,KAAK;cAACoC,KAAK,EAAC,OAAO;cAAAN,QAAA,EAAC;YAAS;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACtCvB,OAAA,CAACrB,KAAK;cAACoC,KAAK,EAAC,QAAQ;cAAAN,QAAA,EAAC;YAAO;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX,CAAC,EAGLgC,UAAU,KAAK,OAAO,iBACrBvD,OAAA,CAACf,KAAK;UAACuN,SAAS,EAAC,UAAU;UAAC7L,KAAK,EAAE;YAAE+L,KAAK,EAAE;UAAO,CAAE;UAAAjM,QAAA,gBACnDT,OAAA;YAAAS,QAAA,gBACET,OAAA,CAACI,IAAI;cAACuM,MAAM;cAAAlM,QAAA,EAAC;YAAQ;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC5BvB,OAAA,CAACnB,KAAK,CAAC+N,KAAK;cAACG,OAAO;cAACpM,KAAK,EAAE;gBAAEe,SAAS,EAAE,CAAC;gBAAEsL,OAAO,EAAE;cAAO,CAAE;cAAAvM,QAAA,gBAC5DT,OAAA,CAACnB,KAAK;gBACJkC,KAAK,EAAE4C,MAAO;gBACdyG,QAAQ,EAAGyC,CAAC,IAAKjJ,SAAS,CAACiJ,CAAC,CAACC,MAAM,CAAC/L,KAAK,CAAE;gBAC3CkM,WAAW,EAAC,4BAAkB;gBAC9BtM,KAAK,EAAE;kBAAEuB,IAAI,EAAE;gBAAE;cAAE;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB,CAAC,eACFvB,OAAA,CAACjB,MAAM;gBACLyC,IAAI,EAAC,SAAS;gBACd0L,OAAO,EAAElF,aAAc;gBACvBmF,OAAO,EAAElJ,eAAgB;gBACzBmJ,QAAQ,EAAE,CAACzJ,MAAO;gBAClBhD,KAAK,EAAE;kBAAE0M,UAAU,EAAE;gBAAE,CAAE;gBAAA5M,QAAA,EAC1B;cAED;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CAAC,eAENvB,OAAA;YAAAS,QAAA,gBACET,OAAA,CAACI,IAAI;cAACuM,MAAM;cAAAlM,QAAA,EAAC;YAAQ;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC5BvB,OAAA,CAACZ,IAAI;cAACkO,QAAQ,EAAErJ,eAAgB;cAAAxD,QAAA,eAC9BT,OAAA,CAAClB,MAAM;gBACLiC,KAAK,EAAEgD,eAAgB;gBACvBqG,QAAQ,EAAEpG,kBAAmB;gBAC7BiJ,WAAW,EAAC,mCAAU;gBACtBtM,KAAK,EAAE;kBAAE+L,KAAK,EAAE,MAAM;kBAAEhL,SAAS,EAAE;gBAAE,CAAE;gBACvCyL,OAAO,EAAElJ,eAAgB;gBAAAxD,QAAA,EAExBoD,iBAAiB,CAACyH,GAAG,CAAEiC,IAAI,iBAC1BvN,OAAA,CAACM,MAAM;kBAAYS,KAAK,EAAEwM,IAAK;kBAAA9M,QAAA,EAC5B8M;gBAAI,GADMA,IAAI;kBAAAnM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAET,CACT;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CACR,EAGAgC,UAAU,KAAK,QAAQ,iBACtBvD,OAAA;UAAAS,QAAA,gBACET,OAAA,CAACI,IAAI;YAACuM,MAAM;YAAAlM,QAAA,EAAC;UAAY;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAChCvB,OAAA,CAACK,OAAO;YAAA,GAAK0J,WAAW;YAAEpJ,KAAK,EAAE;cAAEe,SAAS,EAAE;YAAE,CAAE;YAAAjB,QAAA,gBAChDT,OAAA;cAAGuM,SAAS,EAAC,sBAAsB;cAAA9L,QAAA,eACjCT,OAAA,CAACL,aAAa;gBAAAyB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChB,CAAC,eACJvB,OAAA;cAAGuM,SAAS,EAAC,iBAAiB;cAAA9L,QAAA,EAAC;YAAgB;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACnDvB,OAAA;cAAGuM,SAAS,EAAC,iBAAiB;cAAA9L,QAAA,EAAC;YAE/B;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAGPvB,OAAA,CAACtB,IAAI;MAACoC,KAAK,EAAC,0BAAM;MAACyL,SAAS,EAAC,eAAe;MAAA9L,QAAA,eAC1CT,OAAA,CAACf,KAAK;QAACuN,SAAS,EAAC,UAAU;QAACC,IAAI,EAAC,OAAO;QAAC9L,KAAK,EAAE;UAAE+L,KAAK,EAAE;QAAO,CAAE;QAAAjM,QAAA,gBAChET,OAAA;UAAAS,QAAA,eACET,OAAA,CAACtB,IAAI;YACH+N,IAAI,EAAC,OAAO;YACZ3L,KAAK,eACHd,OAAA,CAACf,KAAK;cAAAwB,QAAA,gBACJT,OAAA,CAACL,aAAa;gBAAAyB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACjBvB,OAAA,CAACI,IAAI;gBAACuM,MAAM;gBAAAlM,QAAA,EAAC;cAAM;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB,CACR;YAAAd,QAAA,eAEDT,OAAA,CAACnB,KAAK,CAAC+N,KAAK;cAACG,OAAO;cAACpM,KAAK,EAAE;gBAAEqM,OAAO,EAAE;cAAO,CAAE;cAAAvM,QAAA,gBAC9CT,OAAA,CAACnB,KAAK;gBACJkC,KAAK,EAAEoD,QAAS;gBAChBiG,QAAQ,EAAGyC,CAAC,IAAKzI,WAAW,CAACyI,CAAC,CAACC,MAAM,CAAC/L,KAAK,CAAE;gBAC7CkM,WAAW,EAAC,4BAAkB;gBAC9BR,IAAI,EAAC,OAAO;gBACZe,MAAM,eAAExN,OAAA,CAACL,aAAa;kBAAAyB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC1BZ,KAAK,EAAE;kBAAEuB,IAAI,EAAE;gBAAE;cAAE;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB,CAAC,eACFvB,OAAA,CAACjB,MAAM;gBACLyC,IAAI,EAAC,SAAS;gBACd0L,OAAO,EAAE1E,eAAgB;gBACzB2E,OAAO,EAAE1I,aAAc;gBACvB2I,QAAQ,EAAE,CAACjJ,QAAS;gBACpBsI,IAAI,EAAC,OAAO;gBACZ9L,KAAK,EAAE;kBAAE0M,UAAU,EAAE;gBAAE,CAAE;gBAAA5M,QAAA,EAC1B;cAED;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAENvB,OAAA;UAAAS,QAAA,gBACET,OAAA,CAACI,IAAI;YAACuM,MAAM;YAAAlM,QAAA,EAAC;UAAK;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACzBvB,OAAA,CAACrB,KAAK,CAACiO,KAAK;YACV7L,KAAK,EAAE4D,cAAe;YACtByF,QAAQ,EAAGyC,CAAC,IAAKjI,iBAAiB,CAACiI,CAAC,CAACC,MAAM,CAAC/L,KAAK,CAAE;YACnDJ,KAAK,EAAE;cAAEe,SAAS,EAAE;YAAE,CAAE;YAAAjB,QAAA,gBAExBT,OAAA,CAACrB,KAAK;cAACoC,KAAK,EAAC,QAAQ;cAAAN,QAAA,EAAC;YAAM;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACpCvB,OAAA,CAACrB,KAAK;cAACoC,KAAK,EAAC,UAAU;cAAAN,QAAA,EAAC;YAAQ;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX,CAAC,EAELoD,cAAc,KAAK,QAAQ,gBAC1B3E,OAAA;UAAAS,QAAA,gBACET,OAAA,CAACI,IAAI;YAACuM,MAAM;YAAAlM,QAAA,EAAC;UAAO;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC3BvB,OAAA,CAACZ,IAAI;YAACkO,QAAQ,EAAE7I,aAAc;YAAAhE,QAAA,eAC5BT,OAAA,CAAClB,MAAM;cACLiC,KAAK,EAAE8D,iBAAkB;cACzBuF,QAAQ,EAAET,qBAAsB;cAChCsD,WAAW,EAAC,kMAAkC;cAC9CtM,KAAK,EAAE;gBAAE+L,KAAK,EAAE,MAAM;gBAAEhL,SAAS,EAAE;cAAE,CAAE;cACvCyL,OAAO,EAAE1I,aAAc;cAAAhE,QAAA,EAEtB4D,iBAAiB,CAACiH,GAAG,CAAEiC,IAAI,iBAC1BvN,OAAA,CAACM,MAAM;gBAAYS,KAAK,EAAEwM,IAAK;gBAAA9M,QAAA,EAC5B8M;cAAI,GADMA,IAAI;gBAAAnM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAET,CACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,EAENsD,iBAAiB,iBAChB7E,OAAA;YAAKW,KAAK,EAAE;cAAEe,SAAS,EAAE;YAAG,CAAE;YAAAjB,QAAA,eAC5BT,OAAA,CAACf,KAAK;cAACuN,SAAS,EAAC,UAAU;cAAC7L,KAAK,EAAE;gBAAE+L,KAAK,EAAE;cAAO,CAAE;cAAAjM,QAAA,gBACnDT,OAAA;gBAAAS,QAAA,gBACET,OAAA,CAACI,IAAI;kBAACoB,IAAI,EAAC,WAAW;kBAAAf,QAAA,EAAC;gBAAQ;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACtCvB,OAAA,CAACZ,IAAI;kBAACkO,QAAQ,EAAErH,oBAAqB;kBAAAxF,QAAA,eACnCT,OAAA;oBAAKW,KAAK,EAAE;sBAAEe,SAAS,EAAE,CAAC;sBAAEW,OAAO,EAAE,EAAE;sBAAEC,eAAe,EAAE,SAAS;sBAAEG,YAAY,EAAE;oBAAE,CAAE;oBAAAhC,QAAA,EACpFwF,oBAAoB,gBACnBjG,OAAA;sBAAAS,QAAA,EAAG;oBAAa;sBAAAW,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC,gBAEpBvB,OAAA,CAAAE,SAAA;sBAAAO,QAAA,gBACET,OAAA;wBAAAS,QAAA,gBAAGT,OAAA;0BAAAS,QAAA,EAAQ;wBAAK;0BAAAW,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,KAAC,EAACwD,kBAAkB,IAAI,KAAK;sBAAA;wBAAA3D,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eAC3DvB,OAAA;wBAAAS,QAAA,gBAAGT,OAAA;0BAAAS,QAAA,EAAQ;wBAAO;0BAAAW,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,KAAC,EAAC0D,kBAAkB,IAAI,KAAK;sBAAA;wBAAA7D,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,EAC5D,CAACgE,mBAAmB,IAAIJ,YAAY,IAAIE,gBAAgB,iBACvDrF,OAAA,CAAAE,SAAA;wBAAAO,QAAA,gBACET,OAAA;0BAAAS,QAAA,gBAAGT,OAAA;4BAAAS,QAAA,EAAQ;0BAAG;4BAAAW,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAQ,CAAC,KAAC,EAAC4D,YAAY;wBAAA;0BAAA/D,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,eAC1CvB,OAAA;0BAAAS,QAAA,gBAAGT,OAAA;4BAAAS,QAAA,EAAQ;0BAAK;4BAAAW,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAQ,CAAC,KAAC,EAAC8D,gBAAgB;wBAAA;0BAAAjE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC;sBAAA,eAChD,CACH;oBAAA,eACD;kBACH;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,EAELgE,mBAAmB,iBAClBvF,OAAA;gBAAAS,QAAA,gBACET,OAAA,CAACI,IAAI;kBAACuM,MAAM;kBAAAlM,QAAA,EAAC;gBAAa;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACjCvB,OAAA;kBAAKW,KAAK,EAAE;oBAAEe,SAAS,EAAE;kBAAE,CAAE;kBAAAjB,QAAA,eAC3BT,OAAA,CAACf,KAAK;oBAACuN,SAAS,EAAC,UAAU;oBAAC7L,KAAK,EAAE;sBAAE+L,KAAK,EAAE;oBAAO,CAAE;oBAAAjM,QAAA,gBACnDT,OAAA,CAAClB,MAAM;sBACLiC,KAAK,EAAEoE,YAAa;sBACpBiF,QAAQ,EAAEhF,eAAgB;sBAC1B6H,WAAW,EAAC,8DAAY;sBACxBtM,KAAK,EAAE;wBAAE+L,KAAK,EAAE;sBAAO,CAAE;sBAAAjM,QAAA,EAExBuC,eAAe,CAACsI,GAAG,CAAEmC,IAAI,iBACxBzN,OAAA,CAACM,MAAM;wBAAYS,KAAK,EAAE0M,IAAK;wBAAAhN,QAAA,EAC5BgN;sBAAI,GADMA,IAAI;wBAAArM,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAET,CACT;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACI,CAAC,EAER4D,YAAY,iBACXnF,OAAA,CAAClB,MAAM;sBACLiC,KAAK,EAAEsE,gBAAiB;sBACxB+E,QAAQ,EAAE9E,mBAAoB;sBAC9B2H,WAAW,EAAE,YAAY9H,YAAY,OAAQ;sBAC7CxE,KAAK,EAAE;wBAAE+L,KAAK,EAAE;sBAAO,CAAE;sBAAAjM,QAAA,EAExB,CAACwC,eAAe,CAACkC,YAAY,CAAiC,IAAI,EAAE,EAAEmG,GAAG,CAAElC,QAAQ,iBAClFpJ,OAAA,CAACM,MAAM;wBAAgBS,KAAK,EAAEqI,QAAS;wBAAA3I,QAAA,EACpC2I;sBAAQ,GADEA,QAAQ;wBAAAhI,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAEb,CACT;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACI,CACT;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACI;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,gBAENvB,OAAA;UAAAS,QAAA,gBACET,OAAA,CAACI,IAAI;YAACuM,MAAM;YAAAlM,QAAA,EAAC;UAAW;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC/BvB,OAAA,CAACZ,IAAI;YAACkO,QAAQ,EAAE7I,aAAc;YAAAhE,QAAA,eAC5BT,OAAA,CAAClB,MAAM;cACL4O,IAAI,EAAC,UAAU;cACf3M,KAAK,EAAEwD,cAAe;cACtB6F,QAAQ,EAAE5F,iBAAkB;cAC5ByI,WAAW,EAAC,4FAAiB;cAC7BtM,KAAK,EAAE;gBAAE+L,KAAK,EAAE,MAAM;gBAAEhL,SAAS,EAAE;cAAE,CAAE;cACvCyL,OAAO,EAAE1I,aAAc;cAAAhE,QAAA,EAEtB4D,iBAAiB,CAACiH,GAAG,CAAEiC,IAAI,iBAC1BvN,OAAA,CAACM,MAAM;gBAAYS,KAAK,EAAEwM,IAAK;gBAAA9M,QAAA,EAC5B8M;cAAI,GADMA,IAAI;gBAAAnM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAET,CACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CACN,EAEA8C,iBAAiB,CAACyD,MAAM,KAAK,CAAC,IAAI,CAACrD,aAAa,iBAC/CzE,OAAA,CAACX,KAAK;UACJF,OAAO,EAAC,4CAAS;UACjBwO,WAAW,EAAC,oOAA2C;UACvDnM,IAAI,EAAC,SAAS;UACdC,QAAQ;QAAA;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CACF;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAGPvB,OAAA,CAACtB,IAAI;MAAC6N,SAAS,EAAC,eAAe;MAACzL,KAAK,EAAC,0BAAM;MAAAL,QAAA,eAC1CT,OAAA,CAACf,KAAK;QAACuN,SAAS,EAAC,UAAU;QAACC,IAAI,EAAC,QAAQ;QAAC9L,KAAK,EAAE;UAAE+L,KAAK,EAAE;QAAO,CAAE;QAAAjM,QAAA,gBACjET,OAAA;UAAAS,QAAA,gBACET,OAAA,CAACI,IAAI;YAACuM,MAAM;YAAAlM,QAAA,EAAC;UAAO;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC3BvB,OAAA,CAACrB,KAAK,CAACiO,KAAK;YACV7L,KAAK,EAAE0F,kBAAmB;YAC1B2D,QAAQ,EAAGyC,CAAC,IAAKnG,qBAAqB,CAACmG,CAAC,CAACC,MAAM,CAAC/L,KAAK,CAAE;YACvDJ,KAAK,EAAE;cAAEe,SAAS,EAAE;YAAE;YACtB;YAAA;YAAAjB,QAAA,gBAEAT,OAAA,CAACrB,KAAK;cAACoC,KAAK,EAAE,IAAK;cAAAN,QAAA,eACjBT,OAAA,CAACf,KAAK;gBAAAwB,QAAA,GAAC,kDAEL,eAAAT,OAAA,CAACI,IAAI;kBAACoB,IAAI,EAAC,WAAW;kBAACb,KAAK,EAAE;oBAAEsL,QAAQ,EAAE;kBAAG,CAAE;kBAAAxL,QAAA,EAAC;gBAEhD;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACRvB,OAAA,CAACrB,KAAK;cAACoC,KAAK,EAAE,KAAM;cAAAN,QAAA,eAClBT,OAAA,CAACf,KAAK;gBAAAwB,QAAA,GAAC,0BAEL,eAAAT,OAAA,CAACI,IAAI;kBAACoB,IAAI,EAAC,WAAW;kBAACb,KAAK,EAAE;oBAAEsL,QAAQ,EAAE;kBAAG,CAAE;kBAAAxL,QAAA,EAAC;gBAEhD;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX,CAAC,EAELkF,kBAAkB,iBACjBzG,OAAA,CAACX,KAAK;UACJF,OAAO,EAAC,sCAAQ;UAChBwO,WAAW,eACT3N,OAAA;YAAAS,QAAA,GAAK,8JAEH,eAAAT,OAAA;cAAAoB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,mFACQ,eAAAvB,OAAA;cAAAS,QAAA,EAAQ;YAAY;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,2DAC7C;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CACN;UACDC,IAAI,EAAC,MAAM;UACXC,QAAQ;QAAA;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CACF;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAGPvB,OAAA,CAACtB,IAAI;MAAC6N,SAAS,EAAC,eAAe;MAACzL,KAAK,EAAC,sCAAQ;MAAAL,QAAA,eAC5CT,OAAA,CAACf,KAAK;QAACuN,SAAS,EAAC,UAAU;QAACC,IAAI,EAAC,QAAQ;QAAC9L,KAAK,EAAE;UAAE+L,KAAK,EAAE;QAAO,CAAE;QAAAjM,QAAA,eACjET,OAAA;UAAAS,QAAA,gBACET,OAAA,CAACI,IAAI;YAACuM,MAAM;YAAAlM,QAAA,EAAC;UAAS;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC7BvB,OAAA;YAAKW,KAAK,EAAE;cAAEe,SAAS,EAAE;YAAE,CAAE;YAAAjB,QAAA,eAC3BT,OAAA,CAACN,QAAQ;cACPkO,OAAO,EAAEzH,oBAAqB;cAC9BiE,QAAQ,EAAGyC,CAAC,IAAKzG,uBAAuB,CAACyG,CAAC,CAACC,MAAM,CAACc,OAAO,CAAE;cAAAnN,QAAA,EAC5D;YAED;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC,eACNvB,OAAA;YAAKW,KAAK,EAAE;cAAEe,SAAS,EAAE;YAAE,CAAE;YAAAjB,QAAA,eAC3BT,OAAA,CAACI,IAAI;cAACoB,IAAI,EAAC,WAAW;cAACb,KAAK,EAAE;gBAAEsL,QAAQ,EAAE;cAAO,CAAE;cAAAxL,QAAA,EAAC;YAGpD;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAGPvB,OAAA,CAACtB,IAAI;MAAC6N,SAAS,EAAC,eAAe;MAAA9L,QAAA,gBAC7BT,OAAA,CAACjB,MAAM;QACLyC,IAAI,EAAC,SAAS;QACdiL,IAAI,EAAC,OAAO;QACZoB,IAAI,eAAE7N,OAAA,CAACJ,kBAAkB;UAAAwB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAC7B2L,OAAO,EAAE3C,qBAAsB;QAC/B4C,OAAO,EAAE1H,UAAW;QACpB2H,QAAQ,EAAE,CAACf,WAAW,CAAC,CAAE;QACzBE,SAAS,EAAC,eAAe;QAAA9L,QAAA,EAExBgF,UAAU,GAAG,SAAS,GAAG;MAAS;QAAArE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7B,CAAC,EAGRkE,UAAU,iBACTzF,OAAA;QAAKuM,SAAS,EAAC,kBAAkB;QAAA9L,QAAA,gBAC/BT,OAAA,CAACI,IAAI;UAAAK,QAAA,EAAC;QAAK;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAClBvB,OAAA,CAACP,QAAQ;UAACqO,OAAO,EAAEnI,QAAS;UAACoI,MAAM,EAAC;QAAQ;UAAA3M,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5C,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC,EAGNsE,OAAO,CAACiC,MAAM,GAAG,CAAC,iBACjB9H,OAAA,CAACtB,IAAI;MAACoC,KAAK,EAAC,0BAAM;MAACyL,SAAS,EAAC,eAAe;MAAA9L,QAAA,EACzCoF,OAAO,CAACiC,MAAM,GAAG,CAAC;MAAA;MACjB;MACA9H,OAAA;QAAAS,QAAA,gBACET,OAAA,CAACd,OAAO;UAAAkC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACXvB,OAAA,CAACG,KAAK;UAAC8B,KAAK,EAAE,CAAE;UAAAxB,QAAA,EAAC;QAAO;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,EAG/B,CAAC,MAAM;UACN,MAAMyM,UAAU,GAAGnI,OAAO,CAACA,OAAO,CAACiC,MAAM,GAAG,CAAC,CAAC;UAC9C,IAAI3B,oBAAoB,IAAI6H,UAAU,aAAVA,UAAU,eAAVA,UAAU,CAAE5L,aAAa,EAAE;YACrD,oBACEpC,OAAA;cAAKW,KAAK,EAAE;gBAAEC,YAAY,EAAE;cAAG,CAAE;cAAAH,QAAA,EAC9BuN,UAAU,CAAC5L,aAAa,CAACG,kBAAkB,gBAC1CvC,OAAA,CAACX,KAAK;gBACJF,OAAO,EAAC,+DAAa;gBACrBwO,WAAW,eACT3N,OAAA;kBAAAS,QAAA,gBACET,OAAA;oBAAGW,KAAK,EAAE;sBAAEgC,MAAM,EAAE;oBAAE,CAAE;oBAAAlC,QAAA,gBAACT,OAAA;sBAAAS,QAAA,EAAQ;oBAAK;sBAAAW,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAACyM,UAAU,CAAC5L,aAAa,CAACQ,aAAa;kBAAA;oBAAAxB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC5FvB,OAAA;oBAAGW,KAAK,EAAE;sBAAEgC,MAAM,EAAE;oBAAY,CAAE;oBAAAlC,QAAA,gBAACT,OAAA;sBAAAS,QAAA,EAAQ;oBAAO;sBAAAW,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAACyM,UAAU,CAAC5L,aAAa,CAACS,kBAAkB,EAAC,SAAE;kBAAA;oBAAAzB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eAC/GvB,OAAA;oBAAGW,KAAK,EAAE;sBAAEgC,MAAM,EAAE,WAAW;sBAAEsJ,QAAQ,EAAE,MAAM;sBAAE9K,KAAK,EAAE;oBAAO,CAAE;oBAAAV,QAAA,GAAC,eAC/D,EAACoF,OAAO,CAACiC,MAAM,EAAC,+DACrB;kBAAA;oBAAA1G,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CACN;gBACDC,IAAI,EAAC,SAAS;gBACdC,QAAQ;gBACRd,KAAK,EAAE;kBAAEC,YAAY,EAAE;gBAAG;cAAE;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B,CAAC,gBAEFvB,OAAA,CAACX,KAAK;gBACJF,OAAO,EAAC,yDAAY;gBACpBwO,WAAW,EAAEK,UAAU,CAAC5L,aAAa,CAACU,KAAM;gBAC5CtB,IAAI,EAAC,OAAO;gBACZC,QAAQ;gBACRd,KAAK,EAAE;kBAAEC,YAAY,EAAE;gBAAG;cAAE;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B;YACF;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAEV;UACA,OAAO,IAAI;QACb,CAAC,EAAE,CAAC,eAEJvB,OAAA;UAAKW,KAAK,EAAE;YAAEC,YAAY,EAAE;UAAG,CAAE;UAAAH,QAAA,gBAC/BT,OAAA,CAACI,IAAI;YAACuM,MAAM;YAAAlM,QAAA,EAAC;UAAW;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC/BvB,OAAA,CAAClB,MAAM;YACL6B,KAAK,EAAE;cAAE+L,KAAK,EAAE,MAAM;cAAEhL,SAAS,EAAE;YAAE,CAAE;YACvCuL,WAAW,EAAC,sCAAQ;YACpBlM,KAAK,EAAEgF,mBAAoB;YAC3BqE,QAAQ,EAAGrJ,KAAK,IAAKiF,sBAAsB,CAACjF,KAAK,CAAE;YAAAN,QAAA,EAElDoF,OAAO,CAACyF,GAAG,CAAC,CAAC9K,MAAM,EAAEyN,KAAK,kBACzBjO,OAAA,CAACM,MAAM;cAAaS,KAAK,EAAEkN,KAAM;cAAAxN,QAAA,EAC9BD,MAAM,CAACoH;YAAU,GADPqG,KAAK;cAAA7M,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEV,CACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,EAGLsE,OAAO,CAACE,mBAAmB,CAAC,iBAC3B/F,OAAA;UAAAS,QAAA,gBACET,OAAA,CAACG,KAAK;YAAC8B,KAAK,EAAE,CAAE;YAAAxB,QAAA,GAAC,gBAAI,EAACoF,OAAO,CAACE,mBAAmB,CAAC,CAAC6B,UAAU;UAAA;YAAAxG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACtEvB,OAAA,CAACO,uBAAuB;YAACC,MAAM,EAAEqF,OAAO,CAACE,mBAAmB;UAAE;YAAA3E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9D,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;MAAA;MAEN;MACAvB,OAAA;QAAAS,QAAA,gBACET,OAAA,CAACG,KAAK;UAAC8B,KAAK,EAAE,CAAE;UAAAxB,QAAA,GAAC,6BAAO,EAACoF,OAAO,CAAC,CAAC,CAAC,CAAC+B,UAAU;QAAA;UAAAxG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACvDvB,OAAA,CAACO,uBAAuB;UAACC,MAAM,EAAEqF,OAAO,CAAC,CAAC;QAAE;UAAAzE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5C;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CACP,EAGA4F,wBAAwB,CAACW,MAAM,GAAG,CAAC,iBAClC9H,OAAA,CAACtB,IAAI;MAACoC,KAAK,EAAC,sCAAQ;MAACyL,SAAS,EAAC,eAAe;MAAC5L,KAAK,EAAE;QAAEe,SAAS,EAAE;MAAG,CAAE;MAAAjB,QAAA,gBACtET,OAAA,CAACX,KAAK;QACJF,OAAO,EAAC,4CAAS;QACjBwO,WAAW,EAAC,4IAAyB;QACrCnM,IAAI,EAAC,SAAS;QACdC,QAAQ;QACRd,KAAK,EAAE;UAAEC,YAAY,EAAE;QAAG;MAAE;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7B,CAAC,eACFvB,OAAA,CAACf,KAAK;QAACuN,SAAS,EAAC,UAAU;QAACC,IAAI,EAAC,OAAO;QAAC9L,KAAK,EAAE;UAAE+L,KAAK,EAAE;QAAO,CAAE;QAAAjM,QAAA,gBAEhET,OAAA;UAAAS,QAAA,gBACET,OAAA,CAACI,IAAI;YAACuM,MAAM;YAAAlM,QAAA,EAAC;UAAO;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC3BvB,OAAA,CAAClB,MAAM;YACLiC,KAAK,EAAE8F,mBAAoB;YAC3BuD,QAAQ,EAAEhD,qBAAsB;YAChCzG,KAAK,EAAE;cAAE+L,KAAK,EAAE,MAAM;cAAEhL,SAAS,EAAE;YAAE,CAAE;YACvCuL,WAAW,EAAC,oEAAa;YAAAxM,QAAA,EAExB0G,wBAAwB,CAACmE,GAAG,CAAE9D,IAAI,iBACjCxH,OAAA,CAACM,MAAM;cAAoBS,KAAK,EAAEyG,IAAI,CAACC,OAAQ;cAAAhH,QAAA,EAC5C+G,IAAI,CAACC,OAAO,CAACyG,QAAQ,CAAC,GAAG,CAAC,GACzB,GAAG1G,IAAI,CAACC,OAAO,CAAC0G,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,IAAIC,IAAI,CAAC5G,IAAI,CAAC6G,UAAU,IAAI7G,IAAI,CAAC8G,UAAU,CAAC,CAACC,cAAc,CAAC,CAAC,GAAG,GAClG,MAAM/G,IAAI,CAACC,OAAO,CAAC+G,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,QAAQ,IAAIJ,IAAI,CAAC5G,IAAI,CAAC6G,UAAU,IAAI7G,IAAI,CAAC8G,UAAU,CAAC,CAACC,cAAc,CAAC,CAAC;YAAG,GAHjG/G,IAAI,CAACC,OAAO;cAAArG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAKjB,CACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,EAGLoF,sBAAsB,CAACmB,MAAM,GAAG,CAAC,iBAChC9H,OAAA;UAAAS,QAAA,EACGkG,sBAAsB,CAACmB,MAAM,GAAG,CAAC;UAAA;UAChC;UACA9H,OAAA;YAAAS,QAAA,gBACET,OAAA,CAACd,OAAO;cAAAkC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACXvB,OAAA,CAACG,KAAK;cAAC8B,KAAK,EAAE,CAAE;cAAAxB,QAAA,EAAC;YAAO;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,EAG/BwF,iBAAiB,iBAChB/G,OAAA;cAAKW,KAAK,EAAE;gBAAEC,YAAY,EAAE;cAAG,CAAE;cAAAH,QAAA,EAC9BsG,iBAAiB,CAACxE,kBAAkB,gBACnCvC,OAAA,CAACX,KAAK;gBACJF,OAAO,EAAC,+DAAa;gBACrBwO,WAAW,eACT3N,OAAA;kBAAAS,QAAA,GAAK,yCACM,EAACsG,iBAAiB,CAACnE,aAAa,eAAC5C,OAAA;oBAAAoB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,2CACtC,EAACwF,iBAAiB,CAAClE,kBAAkB,EAAC,SACjD;gBAAA;kBAAAzB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CACN;gBACDC,IAAI,EAAC,SAAS;gBACdC,QAAQ;gBACRd,KAAK,EAAE;kBAAEC,YAAY,EAAE;gBAAG;cAAE;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B,CAAC,gBAEFvB,OAAA,CAACX,KAAK;gBACJF,OAAO,EAAC,yDAAY;gBACpBwO,WAAW,EAAE5G,iBAAiB,CAACjE,KAAM;gBACrCtB,IAAI,EAAC,OAAO;gBACZC,QAAQ;gBACRd,KAAK,EAAE;kBAAEC,YAAY,EAAE;gBAAG;cAAE;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B;YACF;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CACN,eAEDvB,OAAA;cAAKW,KAAK,EAAE;gBAAEC,YAAY,EAAE;cAAG,CAAE;cAAAH,QAAA,gBAC/BT,OAAA,CAACI,IAAI;gBAACuM,MAAM;gBAAAlM,QAAA,EAAC;cAAW;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC/BvB,OAAA,CAAClB,MAAM;gBACL6B,KAAK,EAAE;kBAAE+L,KAAK,EAAE,MAAM;kBAAEhL,SAAS,EAAE;gBAAE,CAAE;gBACvCuL,WAAW,EAAC,sCAAQ;gBACpBlM,KAAK,EAAEkG,wBAAyB;gBAChCmD,QAAQ,EAAGrJ,KAAK,IAAKmG,2BAA2B,CAACnG,KAAK,CAAE;gBAAAN,QAAA,EAEvDkG,sBAAsB,CAAC2E,GAAG,CAAC,CAAC9K,MAAM,EAAEyN,KAAK,kBACxCjO,OAAA,CAACM,MAAM;kBAAaS,KAAK,EAAEkN,KAAM;kBAAAxN,QAAA,EAC9BD,MAAM,CAACoH;gBAAU,GADPqG,KAAK;kBAAA7M,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEV,CACT;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,EAGLoF,sBAAsB,CAACM,wBAAwB,CAAC,iBAC/CjH,OAAA;cAAAS,QAAA,gBACET,OAAA,CAACG,KAAK;gBAAC8B,KAAK,EAAE,CAAE;gBAAAxB,QAAA,GAAC,gBAAI,EAACkG,sBAAsB,CAACM,wBAAwB,CAAC,CAACW,UAAU;cAAA;gBAAAxG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC1FvB,OAAA,CAACO,uBAAuB;gBAACC,MAAM,EAAEmG,sBAAsB,CAACM,wBAAwB;cAAE;gBAAA7F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClF,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;UAAA;UAEN;UACAvB,OAAA;YAAAS,QAAA,gBACET,OAAA,CAACG,KAAK;cAAC8B,KAAK,EAAE,CAAE;cAAAxB,QAAA,GAAC,6BAAO,EAACkG,sBAAsB,CAAC,CAAC,CAAC,CAACiB,UAAU;YAAA;cAAAxG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACtEvB,OAAA,CAACO,uBAAuB;cAACC,MAAM,EAAEmG,sBAAsB,CAAC,CAAC;YAAE;cAAAvF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3D;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CACP;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAC+B,EAAA,CAj+BID,mBAA6B;EAAA,QAgCyEvD,cAAc;AAAA;AAAA2O,GAAA,GAhCpHpL,mBAA6B;AAm+BnC,eAAeA,mBAAmB;AAAC,IAAAN,EAAA,EAAA0L,GAAA;AAAAC,YAAA,CAAA3L,EAAA;AAAA2L,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module"}
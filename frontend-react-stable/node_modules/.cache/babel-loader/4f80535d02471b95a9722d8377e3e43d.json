{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/frontend-react-stable/src/pages/ModelTrainingPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Card, Radio, Upload, Input, Select, Button, Typography, Space, Divider, message, Spin, InputNumber, Slider, Checkbox, Progress, Alert, Row, Col, Statistic, Tabs } from 'antd';\nimport { InboxOutlined, PlayCircleOutlined, SettingOutlined, ExperimentOutlined, PlusOutlined, DeleteOutlined, UploadOutlined } from '@ant-design/icons';\nimport { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';\nimport { modelTrainingAPI } from '../services/api';\nimport useTaskManager from '../hooks/useTaskManager';\nimport { startMultiTrainingAsync } from '../services/taskApi';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text\n} = Typography;\nconst {\n  Dragger\n} = Upload;\nconst {\n  Option\n} = Select;\nconst {\n  TabPane\n} = Tabs;\n\n// 数据源类型定义\n\n// 训练结果展示组件\nconst TrainingResultDisplay = ({\n  resultKey,\n  result\n}) => {\n  var _result$train_losses, _result$val_losses;\n  const [selectedProt, selectedDatatype] = resultKey.split('_', 2);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: /*#__PURE__*/_jsxDEV(Space, {\n      direction: \"vertical\",\n      size: \"middle\",\n      style: {\n        width: '100%'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(Text, {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"\\u534F\\u8BAE:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 56,\n            columnNumber: 17\n          }, this), \" \", selectedProt]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 56,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 57,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Text, {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"\\u6570\\u636E\\u7C7B\\u578B:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 58,\n            columnNumber: 17\n          }, this), \" \", selectedDatatype]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 58,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 55,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Row, {\n        gutter: 16,\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          span: 6,\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u8BAD\\u7EC3\\u96C6\\u6570\\u636E\\u5F62\\u72B6\",\n            value: result.train_shape ? `${result.train_shape[0]} × ${result.train_shape[1]}` : 'N/A'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 63,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 62,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          span: 6,\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u6D4B\\u8BD5\\u96C6\\u6570\\u636E\\u5F62\\u72B6\",\n            value: result.test_shape ? `${result.test_shape[0]} × ${result.test_shape[1]}` : 'N/A'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 69,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 68,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          span: 6,\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"R\\xB2 \\u5206\\u6570\",\n            value: result.r2_score ? result.r2_score.toFixed(4) : result.r2 ? result.r2.toFixed(4) : 'N/A',\n            precision: 4,\n            valueStyle: {\n              color: result.r2_score > 0.8 || result.r2 > 0.8 ? '#3f8600' : '#cf1322'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 75,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 74,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          span: 6,\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u5EFA\\u8BAE\\u6E05\\u6D17\\u9608\\u503C\",\n            value: result.static_anomaly_threshold ? result.static_anomaly_threshold.toFixed(2) : 'N/A',\n            precision: 2\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 83,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 82,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 61,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Row, {\n        gutter: 16,\n        style: {\n          marginTop: 16\n        },\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          span: 6,\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"CPU\\u4F7F\\u7528\\u7387\",\n            value: result.cpu_percent ? `${result.cpu_percent.toFixed(2)}%` : 'N/A',\n            valueStyle: {\n              color: '#1890ff'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 93,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 92,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          span: 6,\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u5185\\u5B58\\u4F7F\\u7528\",\n            value: result.memory_mb ? `${result.memory_mb.toFixed(2)} MB` : 'N/A',\n            valueStyle: {\n              color: '#1890ff'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 100,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 99,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          span: 6,\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"GPU\\u5185\\u5B58\",\n            value: result.gpu_memory_mb ? `${result.gpu_memory_mb.toFixed(2)} MB` : result.gpu_memory ? `${result.gpu_memory.toFixed(2)} MB` : 'N/A',\n            valueStyle: {\n              color: '#1890ff'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 107,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 106,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          span: 6,\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"GPU\\u4F7F\\u7528\\u7387\",\n            value: result.gpu_utilization_percent ? `${result.gpu_utilization_percent.toFixed(2)}%` : result.gpu_utilization ? `${result.gpu_utilization.toFixed(2)}%` : 'N/A',\n            valueStyle: {\n              color: '#1890ff'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 114,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 113,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 91,\n        columnNumber: 9\n      }, this), result.train_losses && result.val_losses && /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(Text, {\n          strong: true,\n          children: \"\\u8BAD\\u7EC3\\u635F\\u5931\\u66F2\\u7EBF\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 124,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            height: 300,\n            marginTop: 8\n          },\n          children: /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n            width: \"100%\",\n            height: \"100%\",\n            children: /*#__PURE__*/_jsxDEV(LineChart, {\n              data: result.train_losses.map((trainLoss, index) => ({\n                epoch: index + 1,\n                训练损失: trainLoss,\n                验证损失: result.val_losses[index] || null\n              })),\n              margin: {\n                top: 5,\n                right: 30,\n                left: 20,\n                bottom: 5\n              },\n              children: [/*#__PURE__*/_jsxDEV(CartesianGrid, {\n                strokeDasharray: \"3 3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 135,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(XAxis, {\n                dataKey: \"epoch\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 136,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(YAxis, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 137,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Tooltip, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 138,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Legend, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 139,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Line, {\n                type: \"monotone\",\n                dataKey: \"\\u8BAD\\u7EC3\\u635F\\u5931\",\n                stroke: \"#8884d8\",\n                strokeWidth: 2,\n                dot: false\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 140,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Line, {\n                type: \"monotone\",\n                dataKey: \"\\u9A8C\\u8BC1\\u635F\\u5931\",\n                stroke: \"#82ca9d\",\n                strokeWidth: 2,\n                dot: false\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 147,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 127,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 126,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 125,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: 8\n          },\n          children: /*#__PURE__*/_jsxDEV(Text, {\n            type: \"secondary\",\n            children: [\"\\u8BAD\\u7EC3\\u8F6E\\u6570: \", result.train_losses.length, \" epochs | \\u6700\\u7EC8\\u8BAD\\u7EC3\\u635F\\u5931: \", (_result$train_losses = result.train_losses[result.train_losses.length - 1]) === null || _result$train_losses === void 0 ? void 0 : _result$train_losses.toFixed(6), \" | \\u6700\\u7EC8\\u9A8C\\u8BC1\\u635F\\u5931: \", (_result$val_losses = result.val_losses[result.val_losses.length - 1]) === null || _result$val_losses === void 0 ? void 0 : _result$val_losses.toFixed(6)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 158,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 157,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 123,\n        columnNumber: 11\n      }, this), result.y_test_actual && result.y_pred && result.y_test_actual.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(Text, {\n          strong: true,\n          children: \"\\u5B9E\\u9645\\u503C vs \\u9884\\u6D4B\\u503C\\u5BF9\\u6BD4\\u56FE\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 169,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            height: 300,\n            marginTop: 8\n          },\n          children: /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n            width: \"100%\",\n            height: \"100%\",\n            children: /*#__PURE__*/_jsxDEV(LineChart, {\n              data: result.y_test_actual.map((actual, index) => ({\n                index: index + 1,\n                实际值: actual,\n                预测值: result.y_pred[index]\n              })),\n              margin: {\n                top: 5,\n                right: 30,\n                left: 20,\n                bottom: 5\n              },\n              children: [/*#__PURE__*/_jsxDEV(CartesianGrid, {\n                strokeDasharray: \"3 3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 180,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(XAxis, {\n                dataKey: \"index\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 181,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(YAxis, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 182,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Tooltip, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 183,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Legend, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 184,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Line, {\n                type: \"monotone\",\n                dataKey: \"\\u5B9E\\u9645\\u503C\",\n                stroke: \"#8884d8\",\n                strokeWidth: 2,\n                dot: false\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 185,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Line, {\n                type: \"monotone\",\n                dataKey: \"\\u9884\\u6D4B\\u503C\",\n                stroke: \"#82ca9d\",\n                strokeWidth: 2,\n                dot: false\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 192,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 172,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 171,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 170,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: 8\n          },\n          children: /*#__PURE__*/_jsxDEV(Text, {\n            type: \"secondary\",\n            children: [\"\\u663E\\u793A\\u6240\\u6709 \", result.y_test_actual.length, \" \\u4E2A\\u6D4B\\u8BD5\\u6837\\u672C\\u7684\\u9884\\u6D4B\\u5BF9\\u6BD4 | R\\xB2 \\u5206\\u6570: \", result.r2_score ? result.r2_score.toFixed(4) : result.r2 ? result.r2.toFixed(4) : 'N/A', \" | \\u5EFA\\u8BAE\\u9608\\u503C: \", result.static_anomaly_threshold ? result.static_anomaly_threshold.toFixed(2) : 'N/A']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 203,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 202,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 168,\n        columnNumber: 11\n      }, this), result.model_save_path && /*#__PURE__*/_jsxDEV(Alert, {\n        message: \"\\u6A21\\u578B\\u6587\\u4EF6\\u4FE1\\u606F\",\n        description: /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\u6A21\\u578B\\u4FDD\\u5B58\\u8DEF\\u5F84:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 219,\n              columnNumber: 20\n            }, this), \" \", result.model_save_path]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 219,\n            columnNumber: 17\n          }, this), result.scaler_y_save_path && /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\u6807\\u51C6\\u5316\\u5668\\u4FDD\\u5B58\\u8DEF\\u5F84:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 221,\n              columnNumber: 22\n            }, this), \" \", result.scaler_y_save_path]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 221,\n            columnNumber: 19\n          }, this), result.params_save_path && /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\u53C2\\u6570\\u4FDD\\u5B58\\u8DEF\\u5F84:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 224,\n              columnNumber: 22\n            }, this), \" \", result.params_save_path]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 224,\n            columnNumber: 19\n          }, this), result.test_save_path && /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\u6D4B\\u8BD5\\u6570\\u636E\\u4FDD\\u5B58\\u8DEF\\u5F84:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 227,\n              columnNumber: 22\n            }, this), \" \", result.test_save_path]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 227,\n            columnNumber: 19\n          }, this), result.static_anomaly_threshold && /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\u5EFA\\u8BAE\\u6E05\\u6D17\\u9608\\u503C:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 230,\n              columnNumber: 22\n            }, this), \" \", result.static_anomaly_threshold.toFixed(2)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 230,\n            columnNumber: 19\n          }, this), result.finished_time && /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\u8BAD\\u7EC3\\u5B8C\\u6210\\u65F6\\u95F4:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 233,\n              columnNumber: 22\n            }, this), \" \", result.finished_time]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 233,\n            columnNumber: 19\n          }, this), result.duration_seconds && /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\u8BAD\\u7EC3\\u8017\\u65F6:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 236,\n              columnNumber: 22\n            }, this), \" \", result.duration_seconds.toFixed(2), \" \\u79D2\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 236,\n            columnNumber: 19\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 218,\n          columnNumber: 15\n        }, this),\n        type: \"info\",\n        showIcon: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 215,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 54,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 53,\n    columnNumber: 5\n  }, this);\n};\n_c = TrainingResultDisplay;\nconst ModelTrainingPage = () => {\n  _s();\n  // 训练模式：single（单文件）或 multi（多文件）\n  const [trainingMode, setTrainingMode] = useState('single');\n\n  // 单文件模式的状态（保持向后兼容）\n  const [dataSource, setDataSource] = useState('local');\n  const [uploadedFile, setUploadedFile] = useState(null);\n  const [csvDir, setCsvDir] = useState('');\n  const [availableFiles, setAvailableFiles] = useState([]);\n  const [selectedFile, setSelectedFile] = useState('');\n  const [filesLoading, setFilesLoading] = useState(false);\n\n  // 多文件模式的状态\n  const [dataSources, setDataSources] = useState([{\n    id: '1',\n    type: 'local',\n    outputFolder: '/data/output',\n    enabled: true,\n    availableFiles: [],\n    filesLoading: false\n  }]);\n\n  // 协议和数据类型选择（与Streamlit版本一致）\n  const [selectedProts, setSelectedProts] = useState(['TCP']);\n  const [selectedDatatypes, setSelectedDatatypes] = useState({\n    TCP: ['spt_sip_dip']\n  });\n\n  // 训练参数\n  const [learningRate, setLearningRate] = useState(0.0001);\n  const [batchSize, setBatchSize] = useState(64);\n  const [epochs, setEpochs] = useState(100);\n  const [sequenceLength, setSequenceLength] = useState(10);\n  const [hiddenSize, setHiddenSize] = useState(50);\n  const [numLayers, setNumLayers] = useState(2);\n  const [dropout, setDropout] = useState(0.2);\n  const [outputFolder, setOutputFolder] = useState('');\n  const [autoGenerateTemplate, setAutoGenerateTemplate] = useState(false); // 新增：是否自动生成清洗模板\n\n  // 训练状态\n  const [training, setTraining] = useState(false);\n  const [progress, setProgress] = useState(0);\n  const [trainingResults, setTrainingResults] = useState(null);\n  const [selectedResultKey, setSelectedResultKey] = useState('');\n\n  // 多文件训练结果状态\n  const [multiTrainingResults, setMultiTrainingResults] = useState(null);\n  const [selectedMultiSourceKey, setSelectedMultiSourceKey] = useState('');\n  const [selectedMultiResultKey, setSelectedMultiResultKey] = useState('');\n\n  // 任务管理\n  const {\n    submitTrainingTask,\n    getCompletedTasksByType,\n    fetchCompletedTasks\n  } = useTaskManager();\n  const [useAsyncTraining, setUseAsyncTraining] = useState(true); // 默认使用异步训练\n\n  // 多数据源管理函数\n  const addDataSource = () => {\n    const newSource = {\n      id: Date.now().toString(),\n      type: 'local',\n      outputFolder: `/data/output/model_${dataSources.length + 1}`,\n      enabled: true,\n      availableFiles: [],\n      filesLoading: false\n    };\n    setDataSources([...dataSources, newSource]);\n  };\n  const removeDataSource = id => {\n    setDataSources(dataSources.filter(source => source.id !== id));\n  };\n  const updateDataSource = (id, updates) => {\n    setDataSources(dataSources.map(source => source.id === id ? {\n      ...source,\n      ...updates\n    } : source));\n  };\n\n  // 异步任务结果状态\n  const [asyncTrainingResults, setAsyncTrainingResults] = useState(null);\n  const [selectedAsyncResultKey, setSelectedAsyncResultKey] = useState('');\n  const [selectedAsyncTaskId, setSelectedAsyncTaskId] = useState('');\n\n  // 获取已完成的训练任务\n  const completedTrainingTasks = getCompletedTasksByType('training');\n\n  // 处理异步任务选择\n  const handleAsyncTaskSelect = taskId => {\n    setSelectedAsyncTaskId(taskId);\n    const selectedTask = completedTrainingTasks.find(task => task.task_id === taskId);\n    if (selectedTask && selectedTask.result && selectedTask.result.results) {\n      setAsyncTrainingResults(selectedTask.result);\n      setSelectedAsyncResultKey(Object.keys(selectedTask.result.results)[0]);\n    }\n  };\n\n  // 页面加载时获取已完成任务\n  useEffect(() => {\n    fetchCompletedTasks();\n  }, [fetchCompletedTasks]);\n\n  // 自动选择最新的训练任务\n  useEffect(() => {\n    if (completedTrainingTasks.length > 0 && !selectedAsyncTaskId) {\n      const latestTask = completedTrainingTasks[completedTrainingTasks.length - 1];\n      handleAsyncTaskSelect(latestTask.task_id);\n    }\n  }, [completedTrainingTasks, selectedAsyncTaskId, handleAsyncTaskSelect]);\n\n  // 协议和数据类型配置（与Streamlit版本完全一致）\n  const protocolOptions = ['TCP', 'UDP', 'ICMP'];\n  const datatypeOptions = {\n    TCP: ['spt_sip_dip', 'dpt_sip_dip', 'len_dpt_syn', 'seq_ack_dip'],\n    UDP: ['spt_sip_dip', 'dpt_sip_dip'],\n    ICMP: ['dip']\n  };\n\n  // 获取CSV文件列表（单文件模式）\n  const fetchCsvFiles = async () => {\n    if (!csvDir) return;\n    setFilesLoading(true);\n    try {\n      const response = await modelTrainingAPI.listCsvFiles(csvDir);\n      setAvailableFiles(response.data.files || []);\n    } catch (error) {\n      var _error$response, _error$response$data;\n      message.error(((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.detail) || '获取文件列表失败');\n      setAvailableFiles([]);\n    } finally {\n      setFilesLoading(false);\n    }\n  };\n\n  // 获取多数据源的CSV文件列表\n  const fetchCsvFilesForSource = async (sourceId, csvDir) => {\n    if (!csvDir) return;\n    updateDataSource(sourceId, {\n      filesLoading: true\n    });\n    try {\n      const response = await modelTrainingAPI.listCsvFiles(csvDir);\n      updateDataSource(sourceId, {\n        availableFiles: response.data.files || [],\n        filesLoading: false\n      });\n    } catch (error) {\n      var _error$response2, _error$response2$data;\n      message.error(((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.detail) || '获取文件列表失败');\n      updateDataSource(sourceId, {\n        availableFiles: [],\n        filesLoading: false\n      });\n    }\n  };\n\n  // 使用防抖来避免频繁请求 - 只在用户停止输入1.5秒后才发起请求\n  useEffect(() => {\n    if (dataSource === 'local' && csvDir && csvDir.length > 3) {\n      // 至少输入4个字符才开始请求\n      const timer = setTimeout(() => {\n        fetchCsvFiles();\n      }, 1500); // 1.5秒延迟，给用户足够时间输入完整路径\n\n      return () => clearTimeout(timer);\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [dataSource, csvDir]);\n\n  // 文件上传配置（单文件模式）\n  const uploadProps = {\n    name: 'file',\n    multiple: false,\n    accept: '.csv',\n    beforeUpload: () => false,\n    onChange: info => {\n      if (info.fileList.length > 0) {\n        setUploadedFile(info.fileList[0]);\n      } else {\n        setUploadedFile(null);\n      }\n    }\n  };\n\n  // 多数据源文件上传配置\n  const getUploadPropsForSource = sourceId => ({\n    name: 'file',\n    multiple: false,\n    accept: '.csv',\n    beforeUpload: () => false,\n    onChange: info => {\n      if (info.fileList.length > 0) {\n        updateDataSource(sourceId, {\n          file: info.fileList[0]\n        });\n      } else {\n        updateDataSource(sourceId, {\n          file: undefined\n        });\n      }\n    }\n  });\n\n  // 协议选择变化处理（与Streamlit版本一致）\n  const handleProtocolChange = prots => {\n    setSelectedProts(prots);\n    // 为新选择的协议添加默认数据类型\n    const newDatatypes = {\n      ...selectedDatatypes\n    };\n    prots.forEach(prot => {\n      if (!newDatatypes[prot] && datatypeOptions[prot]) {\n        newDatatypes[prot] = [datatypeOptions[prot][0]];\n      }\n    });\n    // 移除未选择协议的数据类型\n    Object.keys(newDatatypes).forEach(prot => {\n      if (!prots.includes(prot)) {\n        delete newDatatypes[prot];\n      }\n    });\n    setSelectedDatatypes(newDatatypes);\n  };\n\n  // 数据类型选择变化处理\n  const handleDatatypeChange = (protocol, datatypes) => {\n    setSelectedDatatypes(prev => ({\n      ...prev,\n      [protocol]: datatypes\n    }));\n  };\n\n  // 验证多文件训练输入\n  const validateMultiTraining = () => {\n    // 检查是否至少有一个有效的数据源\n    const validSources = dataSources.filter(source => {\n      if (source.type === 'upload') {\n        return source.file && source.outputFolder;\n      } else {\n        return source.csvDir && source.selectedFile && source.outputFolder;\n      }\n    });\n    if (validSources.length === 0) {\n      message.error('请至少配置一个有效的数据源');\n      return false;\n    }\n\n    // 检查输出路径是否重复\n    const outputPaths = validSources.map(s => s.outputFolder);\n    const uniquePaths = new Set(outputPaths);\n    if (outputPaths.length !== uniquePaths.size) {\n      message.error('模型保存路径不能重复');\n      return false;\n    }\n    return true;\n  };\n\n  // 开始训练\n  const handleStartTraining = async () => {\n    // 根据训练模式进行不同的验证\n    if (trainingMode === 'multi') {\n      if (!validateMultiTraining()) {\n        return;\n      }\n    } else {\n      // 单文件模式验证（保持原有逻辑）\n      if (dataSource === 'upload' && !uploadedFile) {\n        message.error('请上传CSV文件');\n        return;\n      }\n      if (dataSource === 'local' && (!csvDir || !selectedFile)) {\n        message.error('请选择CSV文件');\n        return;\n      }\n    }\n    if (selectedProts.length === 0) {\n      message.error('请至少选择一种协议');\n      return;\n    }\n    const hasValidDatatypes = selectedProts.some(prot => selectedDatatypes[prot] && selectedDatatypes[prot].length > 0);\n    if (!hasValidDatatypes) {\n      message.error('请为每个协议至少选择一种数据类型');\n      return;\n    }\n    setTraining(true);\n    setProgress(0);\n    setTrainingResults(null);\n    setSelectedResultKey('');\n    try {\n      if (useAsyncTraining) {\n        // 异步训练模式\n        if (trainingMode === 'multi') {\n          // 多文件异步训练\n          const formData = new FormData();\n\n          // 添加文件\n          dataSources.forEach(source => {\n            if (source.type === 'upload' && source.file) {\n              formData.append('files', source.file.originFileObj);\n            }\n          });\n\n          // 添加数据源配置\n          formData.append('data_sources', JSON.stringify(dataSources));\n          formData.append('selected_prots', JSON.stringify(selectedProts));\n          formData.append('selected_datatypes', JSON.stringify(selectedDatatypes));\n          formData.append('learning_rate', learningRate.toString());\n          formData.append('batch_size', batchSize.toString());\n          formData.append('epochs', epochs.toString());\n          formData.append('sequence_length', sequenceLength.toString());\n          formData.append('hidden_size', hiddenSize.toString());\n          formData.append('num_layers', numLayers.toString());\n          formData.append('dropout', dropout.toString());\n          formData.append('auto_generate_template', autoGenerateTemplate.toString());\n\n          // 提交多文件异步任务\n          const taskId = await startMultiTrainingAsync(formData);\n          if (taskId) {\n            message.success(`多文件训练任务已启动（共${dataSources.length}个数据源），您可以继续使用其他功能，任务完成后会收到通知`);\n            // 重置状态\n            setTraining(false);\n            setProgress(0);\n          }\n          return; // 异步模式下直接返回\n        } else {\n          // 单文件异步训练（保持原有逻辑）\n          const formData = new FormData();\n          if (dataSource === 'upload') {\n            formData.append('file', uploadedFile.originFileObj);\n          } else {\n            formData.append('csv_dir', csvDir);\n            formData.append('selected_file', selectedFile);\n          }\n          formData.append('selected_prots', JSON.stringify(selectedProts));\n          formData.append('selected_datatypes', JSON.stringify(selectedDatatypes));\n          formData.append('learning_rate', learningRate.toString());\n          formData.append('batch_size', batchSize.toString());\n          formData.append('epochs', epochs.toString());\n          formData.append('sequence_length', sequenceLength.toString());\n          formData.append('hidden_size', hiddenSize.toString());\n          formData.append('num_layers', numLayers.toString());\n          formData.append('dropout', dropout.toString());\n          formData.append('output_folder', outputFolder);\n          formData.append('auto_generate_template', autoGenerateTemplate.toString());\n\n          // 提交异步任务\n          const taskId = await submitTrainingTask(formData);\n          if (taskId) {\n            message.success('训练任务已启动，您可以继续使用其他功能，任务完成后会收到通知');\n            // 重置状态\n            setTraining(false);\n            setProgress(0);\n          }\n          return; // 异步模式下直接返回\n        }\n      }\n\n      // 同步训练模式\n      let response;\n      if (trainingMode === 'multi') {\n        // 多文件同步训练\n        const formData = new FormData();\n\n        // 添加文件\n        dataSources.forEach(source => {\n          if (source.type === 'upload' && source.file) {\n            formData.append('files', source.file.originFileObj);\n          }\n        });\n\n        // 添加数据源配置\n        formData.append('data_sources', JSON.stringify(dataSources));\n        formData.append('selected_prots', JSON.stringify(selectedProts));\n        formData.append('selected_datatypes', JSON.stringify(selectedDatatypes));\n        formData.append('learning_rate', learningRate.toString());\n        formData.append('batch_size', batchSize.toString());\n        formData.append('epochs', epochs.toString());\n        formData.append('sequence_length', sequenceLength.toString());\n        formData.append('hidden_size', hiddenSize.toString());\n        formData.append('num_layers', numLayers.toString());\n        formData.append('dropout', dropout.toString());\n        formData.append('auto_generate_template', autoGenerateTemplate.toString());\n\n        // 模拟进度更新\n        const progressInterval = setInterval(() => {\n          setProgress(prev => {\n            if (prev >= 90) {\n              clearInterval(progressInterval);\n              return prev;\n            }\n            return prev + 5;\n          });\n        }, 1000);\n        response = await modelTrainingAPI.trainModelMulti(formData);\n        clearInterval(progressInterval);\n\n        // 设置多文件训练结果\n        setMultiTrainingResults(response.data);\n        if (response.data.results && Object.keys(response.data.results).length > 0) {\n          const firstSourceKey = Object.keys(response.data.results)[0];\n          setSelectedMultiSourceKey(firstSourceKey);\n          if (response.data.results[firstSourceKey] && response.data.results[firstSourceKey].results) {\n            setSelectedMultiResultKey(Object.keys(response.data.results[firstSourceKey].results)[0]);\n          }\n        }\n      } else if (dataSource === 'upload') {\n        const formData = new FormData();\n        formData.append('file', uploadedFile.originFileObj);\n        formData.append('selected_prots', JSON.stringify(selectedProts));\n        formData.append('selected_datatypes', JSON.stringify(selectedDatatypes));\n        formData.append('learning_rate', learningRate.toString());\n        formData.append('batch_size', batchSize.toString());\n        formData.append('epochs', epochs.toString());\n        formData.append('sequence_length', sequenceLength.toString());\n        formData.append('hidden_size', hiddenSize.toString());\n        formData.append('num_layers', numLayers.toString());\n        formData.append('dropout', dropout.toString());\n        formData.append('output_folder', outputFolder);\n        formData.append('auto_generate_template', autoGenerateTemplate.toString());\n\n        // 模拟进度更新\n        const progressInterval = setInterval(() => {\n          setProgress(prev => {\n            if (prev >= 90) {\n              clearInterval(progressInterval);\n              return prev;\n            }\n            return prev + 5;\n          });\n        }, 1000);\n        response = await modelTrainingAPI.trainModel(formData);\n        clearInterval(progressInterval);\n      } else {\n        // 本地文件训练逻辑\n        const localTrainingData = {\n          csv_dir: csvDir,\n          selected_file: selectedFile,\n          selected_prots: selectedProts,\n          selected_datatypes: selectedDatatypes,\n          learning_rate: learningRate,\n          batch_size: batchSize,\n          epochs: epochs,\n          sequence_length: sequenceLength,\n          hidden_size: hiddenSize,\n          num_layers: numLayers,\n          dropout: dropout,\n          output_folder: outputFolder,\n          auto_generate_template: autoGenerateTemplate\n        };\n\n        // 模拟进度更新\n        const progressInterval = setInterval(() => {\n          setProgress(prev => {\n            if (prev >= 90) {\n              clearInterval(progressInterval);\n              return prev;\n            }\n            return prev + 5;\n          });\n        }, 1000);\n        response = await modelTrainingAPI.trainModelLocal(localTrainingData);\n        clearInterval(progressInterval);\n      }\n      setProgress(100);\n\n      // 添加调试信息\n      console.log('训练响应数据:', response.data);\n      setTrainingResults(response.data);\n\n      // 设置默认选择第一个结果\n      if (response.data.results && Object.keys(response.data.results).length > 0) {\n        setSelectedResultKey(Object.keys(response.data.results)[0]);\n      }\n      message.success('模型训练完成！');\n\n      // 显示训练结果路径信息\n      if (response.data.result_path) {\n        message.info(`结果已保存至: ${response.data.result_path}`);\n      }\n    } catch (error) {\n      console.error('训练错误详情:', error);\n      console.error('错误响应:', error.response);\n\n      // 更详细的错误信息\n      let errorMessage = '模型训练失败';\n      if (error.response) {\n        var _error$response$data2, _error$response$data3;\n        if ((_error$response$data2 = error.response.data) !== null && _error$response$data2 !== void 0 && _error$response$data2.detail) {\n          errorMessage = error.response.data.detail;\n        } else if ((_error$response$data3 = error.response.data) !== null && _error$response$data3 !== void 0 && _error$response$data3.message) {\n          errorMessage = error.response.data.message;\n        } else if (error.response.statusText) {\n          errorMessage = `请求失败: ${error.response.status} ${error.response.statusText}`;\n        }\n      } else if (error.message) {\n        errorMessage = error.message;\n      }\n      message.error(errorMessage);\n    } finally {\n      setTraining(false);\n    }\n  };\n  const isFormValid = () => {\n    if (trainingMode === 'multi') {\n      // 多文件模式验证\n      const validSources = dataSources.filter(source => {\n        if (source.type === 'upload') {\n          return source.file && source.outputFolder;\n        } else {\n          return source.csvDir && source.selectedFile && source.outputFolder;\n        }\n      });\n      return validSources.length > 0 && selectedProts.length > 0;\n    } else {\n      // 单文件模式验证（保持原有逻辑）\n      if (dataSource === 'upload') {\n        return uploadedFile && selectedProts.length > 0;\n      } else {\n        return csvDir && selectedFile && selectedProts.length > 0;\n      }\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(Title, {\n      level: 3,\n      style: {\n        fontSize: '20px',\n        fontWeight: 600,\n        marginBottom: '8px'\n      },\n      children: \"\\u6A21\\u578B\\u8BAD\\u7EC3\\u4E0E\\u7279\\u5F81\\u9884\\u6D4B\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 790,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Text, {\n      type: \"secondary\",\n      children: \"\\u4E0A\\u4F20\\u6216\\u9009\\u62E9CSV\\u6587\\u4EF6\\uFF0C\\u914D\\u7F6E\\u8BAD\\u7EC3\\u53C2\\u6570\\uFF0C\\u6839\\u636E\\u591A\\u7EF4\\u7279\\u5F81\\u8BAD\\u7EC3\\u6D41\\u91CF\\u68C0\\u6D4B\\u6A21\\u578B\\uFF0C\\u5E76\\u8FDB\\u884C\\u7279\\u5F81\\u9884\\u6D4B\\u3002\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 791,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 795,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      title: \"\\u8BAD\\u7EC3\\u6A21\\u5F0F\",\n      className: \"function-card\",\n      children: /*#__PURE__*/_jsxDEV(Space, {\n        direction: \"vertical\",\n        size: \"large\",\n        style: {\n          width: '100%'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Text, {\n            strong: true,\n            children: \"\\u9009\\u62E9\\u8BAD\\u7EC3\\u6A21\\u5F0F\\uFF1A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 801,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Radio.Group, {\n            value: trainingMode,\n            onChange: e => setTrainingMode(e.target.value),\n            style: {\n              marginTop: 8\n            },\n            children: [/*#__PURE__*/_jsxDEV(Radio, {\n              value: \"single\",\n              children: \"\\u5355\\u6587\\u4EF6\\u8BAD\\u7EC3\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 807,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Radio, {\n              value: \"multi\",\n              children: \"\\u591A\\u6587\\u4EF6\\u6279\\u91CF\\u8BAD\\u7EC3\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 808,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 802,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 800,\n          columnNumber: 11\n        }, this), trainingMode === 'multi' && /*#__PURE__*/_jsxDEV(Alert, {\n          message: \"\\u591A\\u6587\\u4EF6\\u6279\\u91CF\\u8BAD\\u7EC3\\u6A21\\u5F0F\",\n          description: \"\\u60A8\\u53EF\\u4EE5\\u6DFB\\u52A0\\u591A\\u4E2A\\u6570\\u636E\\u6E90\\uFF08\\u672C\\u5730\\u6587\\u4EF6\\u6216\\u4E0A\\u4F20\\u6587\\u4EF6\\uFF09\\uFF0C\\u4E3A\\u6BCF\\u4E2A\\u6570\\u636E\\u6E90\\u914D\\u7F6E\\u72EC\\u7ACB\\u7684\\u6A21\\u578B\\u4FDD\\u5B58\\u8DEF\\u5F84\\uFF0C\\u7CFB\\u7EDF\\u5C06\\u4E3A\\u6BCF\\u4E2A\\u6570\\u636E\\u6E90\\u8BAD\\u7EC3\\u72EC\\u7ACB\\u7684\\u6A21\\u578B\\u3002\",\n          type: \"info\",\n          showIcon: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 813,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 799,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 798,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      title: trainingMode === 'multi' ? \"数据源配置\" : \"数据源\",\n      className: \"function-card\",\n      children: trainingMode === 'multi' ? /*#__PURE__*/_jsxDEV(Space, {\n        direction: \"vertical\",\n        size: \"large\",\n        style: {\n          width: '100%'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: 16\n          },\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            type: \"dashed\",\n            icon: /*#__PURE__*/_jsxDEV(PlusOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 831,\n              columnNumber: 23\n            }, this),\n            onClick: addDataSource,\n            style: {\n              width: '100%'\n            },\n            children: \"\\u6DFB\\u52A0\\u6570\\u636E\\u6E90\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 829,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 828,\n          columnNumber: 13\n        }, this), dataSources.map((source, index) => /*#__PURE__*/_jsxDEV(Card, {\n          size: \"small\",\n          title: `数据源 ${index + 1}`,\n          extra: dataSources.length > 1 && /*#__PURE__*/_jsxDEV(Button, {\n            type: \"text\",\n            danger: true,\n            icon: /*#__PURE__*/_jsxDEV(DeleteOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 850,\n              columnNumber: 29\n            }, this),\n            onClick: () => removeDataSource(source.id)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 847,\n            columnNumber: 21\n          }, this),\n          style: {\n            marginBottom: 16\n          },\n          children: /*#__PURE__*/_jsxDEV(Space, {\n            direction: \"vertical\",\n            style: {\n              width: '100%'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(Text, {\n                strong: true,\n                children: \"\\u6570\\u636E\\u6E90\\u7C7B\\u578B\\uFF1A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 860,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Radio.Group, {\n                value: source.type,\n                onChange: e => updateDataSource(source.id, {\n                  type: e.target.value\n                }),\n                style: {\n                  marginTop: 8\n                },\n                children: [/*#__PURE__*/_jsxDEV(Radio, {\n                  value: \"local\",\n                  children: \"\\u672C\\u5730\\u6587\\u4EF6\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 866,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Radio, {\n                  value: \"upload\",\n                  children: \"\\u4E0A\\u4F20\\u6587\\u4EF6\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 867,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 861,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 859,\n              columnNumber: 19\n            }, this), source.type === 'local' && /*#__PURE__*/_jsxDEV(Space, {\n              direction: \"vertical\",\n              style: {\n                width: '100%'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(Text, {\n                  strong: true,\n                  children: \"CSV\\u6587\\u4EF6\\u76EE\\u5F55\\uFF1A\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 875,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(Input.Group, {\n                  compact: true,\n                  style: {\n                    marginTop: 8,\n                    display: 'flex'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Input, {\n                    value: source.csvDir,\n                    onChange: e => updateDataSource(source.id, {\n                      csvDir: e.target.value\n                    }),\n                    placeholder: \"\\u4F8B\\u5982: /home/<USER>\",\n                    style: {\n                      flex: 1\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 877,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(Button, {\n                    type: \"primary\",\n                    onClick: () => fetchCsvFilesForSource(source.id, source.csvDir || ''),\n                    loading: source.filesLoading,\n                    disabled: !source.csvDir,\n                    style: {\n                      marginLeft: 8\n                    },\n                    children: \"\\u5237\\u65B0\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 883,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 876,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 874,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(Text, {\n                  strong: true,\n                  children: \"\\u9009\\u62E9\\u6587\\u4EF6\\uFF1A\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 896,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(Spin, {\n                  spinning: source.filesLoading || false,\n                  children: /*#__PURE__*/_jsxDEV(Select, {\n                    value: source.selectedFile,\n                    onChange: value => updateDataSource(source.id, {\n                      selectedFile: value\n                    }),\n                    placeholder: \"\\u8BF7\\u9009\\u62E9CSV\\u6587\\u4EF6\",\n                    style: {\n                      width: '100%',\n                      marginTop: 8\n                    },\n                    loading: source.filesLoading,\n                    children: (source.availableFiles || []).map(file => /*#__PURE__*/_jsxDEV(Option, {\n                      value: file,\n                      children: file\n                    }, file, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 906,\n                      columnNumber: 31\n                    }, this))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 898,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 897,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 895,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 873,\n              columnNumber: 21\n            }, this), source.type === 'upload' && /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(Text, {\n                strong: true,\n                children: \"\\u4E0A\\u4F20\\u6587\\u4EF6\\uFF1A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 919,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(Upload, {\n                ...getUploadPropsForSource(source.id),\n                style: {\n                  marginTop: 8\n                },\n                children: /*#__PURE__*/_jsxDEV(Button, {\n                  icon: /*#__PURE__*/_jsxDEV(UploadOutlined, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 921,\n                    columnNumber: 39\n                  }, this),\n                  children: \"\\u9009\\u62E9CSV\\u6587\\u4EF6\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 921,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 920,\n                columnNumber: 23\n              }, this), source.file && /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  marginTop: 8\n                },\n                children: /*#__PURE__*/_jsxDEV(Text, {\n                  type: \"secondary\",\n                  children: [\"\\u5DF2\\u9009\\u62E9: \", source.file.name]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 925,\n                  columnNumber: 27\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 924,\n                columnNumber: 25\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 918,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(Text, {\n                strong: true,\n                children: \"\\u6A21\\u578B\\u4FDD\\u5B58\\u8DEF\\u5F84\\uFF1A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 933,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Input, {\n                value: source.outputFolder,\n                onChange: e => updateDataSource(source.id, {\n                  outputFolder: e.target.value\n                }),\n                placeholder: \"\\u4F8B\\u5982: /data/output/model1\",\n                style: {\n                  marginTop: 8\n                },\n                prefix: /*#__PURE__*/_jsxDEV(InboxOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 939,\n                  columnNumber: 31\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 934,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 932,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 857,\n            columnNumber: 17\n          }, this)\n        }, source.id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 841,\n          columnNumber: 15\n        }, this))]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 826,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(Space, {\n        direction: \"vertical\",\n        size: \"large\",\n        style: {\n          width: '100%'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Text, {\n            strong: true,\n            children: \"\\u8BAD\\u7EC3\\u6570\\u636E\\u6E90\\uFF1A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 949,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Radio.Group, {\n            value: dataSource,\n            onChange: e => setDataSource(e.target.value),\n            style: {\n              marginTop: 8\n            },\n            children: [/*#__PURE__*/_jsxDEV(Radio, {\n              value: \"local\",\n              children: \"\\u9009\\u62E9\\u672C\\u5730CSV\\u6587\\u4EF6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 955,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Radio, {\n              value: \"upload\",\n              children: \"\\u4E0A\\u4F20CSV\\u6587\\u4EF6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 956,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 950,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 948,\n          columnNumber: 13\n        }, this), dataSource === 'local' && /*#__PURE__*/_jsxDEV(Space, {\n          direction: \"vertical\",\n          style: {\n            width: '100%'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: \"CSV\\u6587\\u4EF6\\u76EE\\u5F55\\uFF1A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 964,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Input.Group, {\n              compact: true,\n              style: {\n                marginTop: 8,\n                display: 'flex'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Input, {\n                value: csvDir,\n                onChange: e => setCsvDir(e.target.value),\n                placeholder: \"\\u4F8B\\u5982: /home/<USER>\",\n                style: {\n                  flex: 1\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 966,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                type: \"primary\",\n                onClick: fetchCsvFiles,\n                loading: filesLoading,\n                disabled: !csvDir,\n                style: {\n                  marginLeft: 8\n                },\n                children: \"\\u5237\\u65B0\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 972,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 965,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 963,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: \"\\u9009\\u62E9\\u6587\\u4EF6\\uFF1A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 985,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Spin, {\n              spinning: filesLoading,\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                value: selectedFile,\n                onChange: setSelectedFile,\n                placeholder: \"\\u8BF7\\u9009\\u62E9CSV\\u6587\\u4EF6\",\n                style: {\n                  width: '100%',\n                  marginTop: 8\n                },\n                loading: filesLoading,\n                children: availableFiles.map(file => /*#__PURE__*/_jsxDEV(Option, {\n                  value: file,\n                  children: file\n                }, file, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 995,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 987,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 986,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 984,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 962,\n          columnNumber: 13\n        }, this), dataSource === 'upload' && /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Text, {\n            strong: true,\n            children: \"\\u4E0A\\u4F20\\u6587\\u4EF6\\uFF1A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1008,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Dragger, {\n            ...uploadProps,\n            style: {\n              marginTop: 8\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"ant-upload-drag-icon\",\n              children: /*#__PURE__*/_jsxDEV(InboxOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1011,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1010,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"ant-upload-text\",\n              children: \"\\u70B9\\u51FB\\u6216\\u62D6\\u62FDCSV\\u6587\\u4EF6\\u5230\\u6B64\\u533A\\u57DF\\u4E0A\\u4F20\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1013,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"ant-upload-hint\",\n              children: \"\\u652F\\u6301CSV\\u683C\\u5F0F\\u7684\\u6D41\\u91CF\\u6570\\u636E\\u6587\\u4EF6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1014,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1009,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1007,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 947,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 824,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      title: \"\\u534F\\u8BAE\\u548C\\u6570\\u636E\\u7C7B\\u578B\\u9009\\u62E9\",\n      className: \"function-card\",\n      children: /*#__PURE__*/_jsxDEV(Space, {\n        direction: \"vertical\",\n        size: \"large\",\n        style: {\n          width: '100%'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Text, {\n            strong: true,\n            children: \"\\u9009\\u62E9\\u534F\\u8BAE\\uFF1A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1028,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Select, {\n            mode: \"multiple\",\n            value: selectedProts,\n            onChange: handleProtocolChange,\n            placeholder: \"\\u8BF7\\u9009\\u62E9\\u534F\\u8BAE\",\n            style: {\n              width: '100%',\n              marginTop: 8\n            },\n            children: protocolOptions.map(prot => /*#__PURE__*/_jsxDEV(Option, {\n              value: prot,\n              children: prot\n            }, prot, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1037,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1029,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1027,\n          columnNumber: 11\n        }, this), selectedProts.map(prot => /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Text, {\n            strong: true,\n            children: [prot, \" \\u6570\\u636E\\u7C7B\\u578B\\uFF1A\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1046,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Checkbox.Group, {\n            value: selectedDatatypes[prot] || [],\n            onChange: datatypes => handleDatatypeChange(prot, datatypes),\n            style: {\n              marginTop: 8\n            },\n            children: (datatypeOptions[prot] || []).map(datatype => /*#__PURE__*/_jsxDEV(Checkbox, {\n              value: datatype,\n              children: datatype\n            }, datatype, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1053,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1047,\n            columnNumber: 15\n          }, this)]\n        }, prot, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1045,\n          columnNumber: 13\n        }, this))]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1026,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1025,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      title: /*#__PURE__*/_jsxDEV(Space, {\n        children: [/*#__PURE__*/_jsxDEV(SettingOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1067,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"\\u8BAD\\u7EC3\\u53C2\\u6570\\u914D\\u7F6E\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1068,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1066,\n        columnNumber: 11\n      }, this),\n      className: \"function-card\",\n      children: [/*#__PURE__*/_jsxDEV(Row, {\n        gutter: [24, 24],\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          span: 12,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            size: \"small\",\n            title: /*#__PURE__*/_jsxDEV(Space, {\n              children: [/*#__PURE__*/_jsxDEV(ExperimentOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1080,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Text, {\n                strong: true,\n                children: \"\\u57FA\\u7840\\u53C2\\u6570\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1081,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1079,\n              columnNumber: 17\n            }, this),\n            children: /*#__PURE__*/_jsxDEV(Space, {\n              direction: \"vertical\",\n              size: \"middle\",\n              style: {\n                width: '100%'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Row, {\n                align: \"middle\",\n                children: [/*#__PURE__*/_jsxDEV(Col, {\n                  span: 10,\n                  children: /*#__PURE__*/_jsxDEV(Text, {\n                    strong: true,\n                    children: \"\\u5B66\\u4E60\\u7387\\uFF1A\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1088,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1087,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Col, {\n                  span: 14,\n                  children: /*#__PURE__*/_jsxDEV(InputNumber, {\n                    value: learningRate,\n                    onChange: value => setLearningRate(value || 0.0001),\n                    min: 0.0001,\n                    max: 1,\n                    step: 0.0001,\n                    style: {\n                      width: '100%'\n                    },\n                    placeholder: \"0.0001\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1091,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1090,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1086,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Row, {\n                align: \"middle\",\n                children: [/*#__PURE__*/_jsxDEV(Col, {\n                  span: 10,\n                  children: /*#__PURE__*/_jsxDEV(Text, {\n                    strong: true,\n                    children: \"\\u6279\\u91CF\\u5927\\u5C0F\\uFF1A\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1104,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1103,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Col, {\n                  span: 14,\n                  children: /*#__PURE__*/_jsxDEV(InputNumber, {\n                    value: batchSize,\n                    onChange: value => setBatchSize(value || 64),\n                    min: 1,\n                    max: 512,\n                    style: {\n                      width: '100%'\n                    },\n                    placeholder: \"64\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1107,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1106,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1102,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Row, {\n                align: \"middle\",\n                children: [/*#__PURE__*/_jsxDEV(Col, {\n                  span: 10,\n                  children: /*#__PURE__*/_jsxDEV(Text, {\n                    strong: true,\n                    children: \"\\u8BAD\\u7EC3\\u8F6E\\u6570\\uFF1A\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1119,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1118,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Col, {\n                  span: 14,\n                  children: /*#__PURE__*/_jsxDEV(InputNumber, {\n                    value: epochs,\n                    onChange: value => setEpochs(value || 100),\n                    min: 1,\n                    max: 1000,\n                    style: {\n                      width: '100%'\n                    },\n                    placeholder: \"100\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1122,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1121,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1117,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1085,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1076,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1075,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          span: 12,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            size: \"small\",\n            title: /*#__PURE__*/_jsxDEV(Space, {\n              children: [/*#__PURE__*/_jsxDEV(SettingOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1142,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Text, {\n                strong: true,\n                children: \"\\u6A21\\u578B\\u53C2\\u6570\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1143,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1141,\n              columnNumber: 17\n            }, this),\n            children: /*#__PURE__*/_jsxDEV(Space, {\n              direction: \"vertical\",\n              size: \"middle\",\n              style: {\n                width: '100%'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Row, {\n                align: \"middle\",\n                children: [/*#__PURE__*/_jsxDEV(Col, {\n                  span: 10,\n                  children: /*#__PURE__*/_jsxDEV(Text, {\n                    strong: true,\n                    children: \"\\u5E8F\\u5217\\u957F\\u5EA6\\uFF1A\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1150,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1149,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Col, {\n                  span: 14,\n                  children: /*#__PURE__*/_jsxDEV(InputNumber, {\n                    value: sequenceLength,\n                    onChange: value => setSequenceLength(value || 10),\n                    min: 1,\n                    max: 100,\n                    style: {\n                      width: '100%'\n                    },\n                    placeholder: \"10\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1153,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1152,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1148,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Row, {\n                align: \"middle\",\n                children: [/*#__PURE__*/_jsxDEV(Col, {\n                  span: 10,\n                  children: /*#__PURE__*/_jsxDEV(Text, {\n                    strong: true,\n                    children: \"\\u9690\\u85CF\\u5C42\\u5927\\u5C0F\\uFF1A\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1165,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1164,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Col, {\n                  span: 14,\n                  children: /*#__PURE__*/_jsxDEV(InputNumber, {\n                    value: hiddenSize,\n                    onChange: value => setHiddenSize(value || 50),\n                    min: 10,\n                    max: 512,\n                    step: 10,\n                    style: {\n                      width: '100%'\n                    },\n                    placeholder: \"50\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1168,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1167,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1163,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Row, {\n                align: \"middle\",\n                children: [/*#__PURE__*/_jsxDEV(Col, {\n                  span: 10,\n                  children: /*#__PURE__*/_jsxDEV(Text, {\n                    strong: true,\n                    children: \"\\u5C42\\u6570\\uFF1A\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1181,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1180,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Col, {\n                  span: 14,\n                  children: /*#__PURE__*/_jsxDEV(InputNumber, {\n                    value: numLayers,\n                    onChange: value => setNumLayers(value || 2),\n                    min: 1,\n                    max: 10,\n                    style: {\n                      width: '100%'\n                    },\n                    placeholder: \"2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1184,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1183,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1179,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  width: '100%'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Row, {\n                  align: \"middle\",\n                  style: {\n                    marginBottom: 8\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Col, {\n                    span: 12,\n                    children: /*#__PURE__*/_jsxDEV(Text, {\n                      strong: true,\n                      children: \"Dropout \\u6982\\u7387\\uFF1A\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1197,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1196,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Col, {\n                    span: 12,\n                    style: {\n                      textAlign: 'right'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(Text, {\n                      code: true,\n                      children: dropout\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1200,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1199,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1195,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Slider, {\n                  value: dropout,\n                  onChange: setDropout,\n                  min: 0,\n                  max: 0.9,\n                  step: 0.05,\n                  marks: {\n                    0: '0',\n                    0.2: '0.2',\n                    0.5: '0.5',\n                    0.9: '0.9'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1203,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1194,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1147,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1138,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1137,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1073,\n        columnNumber: 9\n      }, this), trainingMode === 'single' && /*#__PURE__*/_jsxDEV(Row, {\n        style: {\n          marginTop: 24\n        },\n        children: /*#__PURE__*/_jsxDEV(Col, {\n          span: 24,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            size: \"small\",\n            title: /*#__PURE__*/_jsxDEV(Space, {\n              children: [/*#__PURE__*/_jsxDEV(InboxOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1230,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Text, {\n                strong: true,\n                children: \"\\u6A21\\u578B\\u4FDD\\u5B58\\u8DEF\\u5F84\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1231,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1229,\n              columnNumber: 19\n            }, this),\n            children: /*#__PURE__*/_jsxDEV(Input, {\n              value: outputFolder,\n              onChange: e => setOutputFolder(e.target.value),\n              placeholder: \"\\u4F8B\\u5982: /data/output\",\n              size: \"large\",\n              prefix: /*#__PURE__*/_jsxDEV(InboxOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1240,\n                columnNumber: 27\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1235,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1226,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1225,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1224,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1064,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      className: \"function-card\",\n      title: \"\\u6E05\\u6D17\\u6A21\\u677F\\u751F\\u6210\",\n      children: /*#__PURE__*/_jsxDEV(Space, {\n        direction: \"vertical\",\n        size: \"middle\",\n        style: {\n          width: '100%'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Text, {\n            strong: true,\n            children: \"\\u81EA\\u52A8\\u751F\\u6210\\u6E05\\u6D17\\u6A21\\u677F\\uFF1A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1252,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginTop: 8\n            },\n            children: /*#__PURE__*/_jsxDEV(Checkbox, {\n              checked: autoGenerateTemplate,\n              onChange: e => setAutoGenerateTemplate(e.target.checked),\n              children: \"\\u8BAD\\u7EC3\\u5B8C\\u6210\\u540E\\u81EA\\u52A8\\u751F\\u6210\\u6E05\\u6D17\\u6A21\\u677F\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1254,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1253,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginTop: 8\n            },\n            children: /*#__PURE__*/_jsxDEV(Text, {\n              type: \"secondary\",\n              style: {\n                fontSize: '12px'\n              },\n              children: \"\\u9009\\u62E9\\u6B64\\u9009\\u9879\\u540E\\uFF0C\\u7CFB\\u7EDF\\u5C06\\u5728\\u6A21\\u578B\\u8BAD\\u7EC3\\u5B8C\\u6210\\u540E\\u81EA\\u52A8\\u8C03\\u7528\\u6E05\\u6D17\\u6A21\\u677F\\u751F\\u6210\\u529F\\u80FD\\uFF0C \\u6839\\u636E\\u8BAD\\u7EC3\\u7ED3\\u679C\\u4E2D\\u7684\\u9608\\u503C\\u4FE1\\u606F\\u751F\\u6210\\u76F8\\u5E94\\u7684\\u6E05\\u6D17\\u6A21\\u677F\\u6587\\u4EF6\\u3002\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1262,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1261,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1251,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1250,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1249,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      className: \"function-card\",\n      title: \"\\u8BAD\\u7EC3\\u6A21\\u5F0F\",\n      children: /*#__PURE__*/_jsxDEV(Space, {\n        direction: \"vertical\",\n        size: \"middle\",\n        style: {\n          width: '100%'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Text, {\n            strong: true,\n            children: \"\\u9009\\u62E9\\u8BAD\\u7EC3\\u6A21\\u5F0F\\uFF1A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1275,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Radio.Group, {\n            value: useAsyncTraining,\n            onChange: e => setUseAsyncTraining(e.target.value),\n            style: {\n              marginTop: 8\n            },\n            children: [/*#__PURE__*/_jsxDEV(Radio, {\n              value: true,\n              children: /*#__PURE__*/_jsxDEV(Space, {\n                children: [\"\\u5F02\\u6B65\\u8BAD\\u7EC3\\uFF08\\u63A8\\u8350\\uFF09\", /*#__PURE__*/_jsxDEV(Text, {\n                  type: \"secondary\",\n                  style: {\n                    fontSize: 12\n                  },\n                  children: \"- \\u540E\\u53F0\\u8FD0\\u884C\\uFF0C\\u4E0D\\u963B\\u585E\\u5176\\u4ED6\\u64CD\\u4F5C\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1284,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1282,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1281,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Radio, {\n              value: false,\n              children: /*#__PURE__*/_jsxDEV(Space, {\n                children: [\"\\u540C\\u6B65\\u8BAD\\u7EC3\", /*#__PURE__*/_jsxDEV(Text, {\n                  type: \"secondary\",\n                  style: {\n                    fontSize: 12\n                  },\n                  children: \"- \\u7B49\\u5F85\\u8BAD\\u7EC3\\u5B8C\\u6210\\uFF0C\\u671F\\u95F4\\u65E0\\u6CD5\\u4F7F\\u7528\\u5176\\u4ED6\\u529F\\u80FD\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1292,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1290,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1289,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1276,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1274,\n          columnNumber: 11\n        }, this), useAsyncTraining && /*#__PURE__*/_jsxDEV(Alert, {\n          message: \"\\u5F02\\u6B65\\u8BAD\\u7EC3\\u6A21\\u5F0F\",\n          description: /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [\"\\u8BAD\\u7EC3\\u4EFB\\u52A1\\u5C06\\u5728\\u540E\\u53F0\\u8FD0\\u884C\\uFF0C\\u60A8\\u53EF\\u4EE5\\u7EE7\\u7EED\\u4F7F\\u7528\\u7CFB\\u7EDF\\u7684\\u5176\\u4ED6\\u529F\\u80FD\\u3002\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1306,\n              columnNumber: 19\n            }, this), \"\\u4EFB\\u52A1\\u5B8C\\u6210\\u540E\\u4F1A\\u6536\\u5230\\u901A\\u77E5\\uFF0C\\u53EF\\u5728 \", /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\u4FA7\\u8FB9\\u680F\\u83DC\\u5355 \\u2192 \\u4EFB\\u52A1\\u7BA1\\u7406\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1307,\n              columnNumber: 33\n            }, this), \" \\u4E2D\\u67E5\\u770B\\u8FDB\\u5EA6\\u548C\\u7ED3\\u679C\\u3002\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1304,\n            columnNumber: 17\n          }, this),\n          type: \"info\",\n          showIcon: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1301,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1273,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1272,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      className: \"function-card\",\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        type: \"primary\",\n        size: \"large\",\n        icon: /*#__PURE__*/_jsxDEV(PlayCircleOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1322,\n          columnNumber: 17\n        }, this),\n        onClick: handleStartTraining,\n        loading: training,\n        disabled: !isFormValid(),\n        className: \"action-button\",\n        children: training ? '正在训练...' : '开始训练预测'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1319,\n        columnNumber: 9\n      }, this), training && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"progress-section\",\n        children: [/*#__PURE__*/_jsxDEV(Text, {\n          children: \"\\u8BAD\\u7EC3\\u8FDB\\u5EA6\\uFF1A\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1334,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Progress, {\n          percent: progress,\n          status: \"active\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1335,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1333,\n        columnNumber: 11\n      }, this), trainingResults && trainingResults.results && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginTop: 24\n        },\n        children: /*#__PURE__*/_jsxDEV(Alert, {\n          message: \"\\u8BAD\\u7EC3\\u5B8C\\u6210\",\n          description: /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"\\u6240\\u6709\\u6A21\\u578B\\u8BAD\\u7EC3\\u5B8C\\u6210\\uFF01\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1346,\n              columnNumber: 19\n            }, this), trainingResults.result_path && /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"\\u7ED3\\u679C\\u5DF2\\u66F4\\u65B0\\u81F3:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1348,\n                columnNumber: 24\n              }, this), \" \", trainingResults.result_path]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1348,\n              columnNumber: 21\n            }, this), trainingResults.template_info && /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginTop: 8,\n                padding: 8,\n                backgroundColor: '#f6ffed',\n                border: '1px solid #b7eb8f',\n                borderRadius: 4\n              },\n              children: trainingResults.template_info.template_generated ? /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  style: {\n                    color: '#52c41a',\n                    fontWeight: 'bold'\n                  },\n                  children: \"\\u2705 \\u6E05\\u6D17\\u6A21\\u677F\\u5DF2\\u81EA\\u52A8\\u751F\\u6210\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1354,\n                  columnNumber: 27\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"\\u6A21\\u677F\\u8DEF\\u5F84:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1355,\n                    columnNumber: 30\n                  }, this), \" \", trainingResults.template_info.template_path]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1355,\n                  columnNumber: 27\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"\\u66F4\\u65B0\\u9608\\u503C\\u6570\\u91CF:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1356,\n                    columnNumber: 30\n                  }, this), \" \", trainingResults.template_info.updated_thresholds]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1356,\n                  columnNumber: 27\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1353,\n                columnNumber: 25\n              }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  style: {\n                    color: '#ff4d4f',\n                    fontWeight: 'bold'\n                  },\n                  children: \"\\u274C \\u6E05\\u6D17\\u6A21\\u677F\\u751F\\u6210\\u5931\\u8D25\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1360,\n                  columnNumber: 27\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"\\u9519\\u8BEF\\u4FE1\\u606F:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1361,\n                    columnNumber: 30\n                  }, this), \" \", trainingResults.template_info.error]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1361,\n                  columnNumber: 27\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1359,\n                columnNumber: 25\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1351,\n              columnNumber: 21\n            }, this), Object.entries(trainingResults.results).map(([key, result]) => /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginTop: 8\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"\\u534F\\u8BAE\\u4E0E\\u6570\\u636E\\u7C7B\\u578B:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1368,\n                  columnNumber: 26\n                }, this), \" \", key]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1368,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [\"\\u6A21\\u578B\\u5DF2\\u4FDD\\u5B58\\u81F3: \", result.model_save_path]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1369,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [\"\\u6807\\u51C6\\u5316\\u5668\\u5DF2\\u4FDD\\u5B58\\u81F3: \", result.scaler_y_save_path]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1370,\n                columnNumber: 23\n              }, this)]\n            }, key, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1367,\n              columnNumber: 21\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1345,\n            columnNumber: 17\n          }, this),\n          type: \"success\",\n          showIcon: true,\n          style: {\n            marginTop: 16\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1342,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1341,\n        columnNumber: 11\n      }, this), trainingResults && trainingResults.results && Object.keys(trainingResults.results).length > 0 && /*#__PURE__*/_jsxDEV(Card, {\n        title: \"\\u67E5\\u770B\\u6A21\\u578B\\u8BAD\\u7EC3\\u53CA\\u7279\\u5F81\\u9884\\u6D4B\\u7ED3\\u679C\",\n        className: \"function-card\",\n        children: /*#__PURE__*/_jsxDEV(Space, {\n          direction: \"vertical\",\n          size: \"large\",\n          style: {\n            width: '100%'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: \"\\u9009\\u62E9\\u8981\\u67E5\\u770B\\u7684\\u534F\\u8BAE\\u548C\\u6570\\u636E\\u7C7B\\u578B\\uFF1A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1387,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select, {\n              value: selectedResultKey,\n              onChange: setSelectedResultKey,\n              style: {\n                width: '100%',\n                marginTop: 8\n              },\n              placeholder: \"\\u8BF7\\u9009\\u62E9\\u534F\\u8BAE\\u548C\\u6570\\u636E\\u7C7B\\u578B\",\n              children: Object.keys(trainingResults.results).map(key => /*#__PURE__*/_jsxDEV(Option, {\n                value: key,\n                children: key\n              }, key, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1395,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1388,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1386,\n            columnNumber: 13\n          }, this), selectedResultKey && trainingResults.results[selectedResultKey] && /*#__PURE__*/_jsxDEV(TrainingResultDisplay, {\n            resultKey: selectedResultKey,\n            result: trainingResults.results[selectedResultKey]\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1403,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1385,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1384,\n        columnNumber: 9\n      }, this), multiTrainingResults && multiTrainingResults.results && Object.keys(multiTrainingResults.results).length > 0 && /*#__PURE__*/_jsxDEV(Card, {\n        title: \"\\u591A\\u6587\\u4EF6\\u8BAD\\u7EC3\\u7ED3\\u679C\",\n        className: \"function-card\",\n        children: [/*#__PURE__*/_jsxDEV(Alert, {\n          message: \"\\u591A\\u6587\\u4EF6\\u8BAD\\u7EC3\\u5B8C\\u6210\",\n          description: `成功处理 ${Object.keys(multiTrainingResults.results).length} 个数据源的训练任务`,\n          type: \"success\",\n          showIcon: true,\n          style: {\n            marginBottom: 16\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1415,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Tabs, {\n          children: Object.entries(multiTrainingResults.results).map(([sourceId, sourceResult]) => /*#__PURE__*/_jsxDEV(TabPane, {\n            tab: `数据源 ${sourceId}`,\n            children: sourceResult.error ? /*#__PURE__*/_jsxDEV(Alert, {\n              message: \"\\u8BAD\\u7EC3\\u5931\\u8D25\",\n              description: sourceResult.error,\n              type: \"error\",\n              showIcon: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1426,\n              columnNumber: 19\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              children: sourceResult.results && Object.keys(sourceResult.results).length > 0 ? /*#__PURE__*/_jsxDEV(Space, {\n                direction: \"vertical\",\n                size: \"large\",\n                style: {\n                  width: '100%'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(Text, {\n                    strong: true,\n                    children: \"\\u9009\\u62E9\\u8981\\u67E5\\u770B\\u7684\\u534F\\u8BAE\\u548C\\u6570\\u636E\\u7C7B\\u578B\\uFF1A\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1438,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(Select, {\n                    value: selectedMultiSourceKey === sourceId ? selectedMultiResultKey : '',\n                    onChange: value => {\n                      setSelectedMultiSourceKey(sourceId);\n                      setSelectedMultiResultKey(value);\n                    },\n                    style: {\n                      width: '100%',\n                      marginTop: 8\n                    },\n                    placeholder: \"\\u8BF7\\u9009\\u62E9\\u534F\\u8BAE\\u548C\\u6570\\u636E\\u7C7B\\u578B\",\n                    children: Object.keys(sourceResult.results).map(key => /*#__PURE__*/_jsxDEV(Option, {\n                      value: key,\n                      children: key\n                    }, key, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1449,\n                      columnNumber: 31\n                    }, this))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1439,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1437,\n                  columnNumber: 25\n                }, this), selectedMultiSourceKey === sourceId && selectedMultiResultKey && sourceResult.results[selectedMultiResultKey] && /*#__PURE__*/_jsxDEV(TrainingResultDisplay, {\n                  resultKey: selectedMultiResultKey,\n                  result: sourceResult.results[selectedMultiResultKey]\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1457,\n                  columnNumber: 27\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1436,\n                columnNumber: 23\n              }, this) : /*#__PURE__*/_jsxDEV(Alert, {\n                message: \"\\u65E0\\u8BAD\\u7EC3\\u7ED3\\u679C\",\n                description: \"\\u8BE5\\u6570\\u636E\\u6E90\\u6CA1\\u6709\\u751F\\u6210\\u6709\\u6548\\u7684\\u8BAD\\u7EC3\\u7ED3\\u679C\",\n                type: \"warning\",\n                showIcon: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1464,\n                columnNumber: 23\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1433,\n              columnNumber: 19\n            }, this)\n          }, sourceId, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1424,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1422,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1414,\n        columnNumber: 9\n      }, this), completedTrainingTasks.length > 0 && /*#__PURE__*/_jsxDEV(Card, {\n        title: \"\\u5F02\\u6B65\\u8BAD\\u7EC3\\u7ED3\\u679C\",\n        className: \"function-card\",\n        style: {\n          marginTop: 24\n        },\n        children: [/*#__PURE__*/_jsxDEV(Alert, {\n          message: \"\\u5F02\\u6B65\\u8BAD\\u7EC3\\u5DF2\\u5B8C\\u6210\",\n          description: \"\\u4EE5\\u4E0B\\u662F\\u540E\\u53F0\\u8BAD\\u7EC3\\u4EFB\\u52A1\\u7684\\u7ED3\\u679C\\uFF0C\\u60A8\\u53EF\\u4EE5\\u67E5\\u770B\\u4E0D\\u540C\\u534F\\u8BAE\\u548C\\u6570\\u636E\\u7C7B\\u578B\\u7684\\u8BAD\\u7EC3\\u6548\\u679C\\u3002\",\n          type: \"success\",\n          showIcon: true,\n          style: {\n            marginBottom: 16\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1482,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Space, {\n          direction: \"vertical\",\n          size: \"large\",\n          style: {\n            width: '100%'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: \"\\u9009\\u62E9\\u8BAD\\u7EC3\\u4EFB\\u52A1\\uFF1A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1492,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select, {\n              value: selectedAsyncTaskId,\n              onChange: handleAsyncTaskSelect,\n              style: {\n                width: '100%',\n                marginTop: 8\n              },\n              placeholder: \"\\u8BF7\\u9009\\u62E9\\u8981\\u67E5\\u770B\\u7684\\u8BAD\\u7EC3\\u4EFB\\u52A1\",\n              children: completedTrainingTasks.map(task => /*#__PURE__*/_jsxDEV(Option, {\n                value: task.task_id,\n                children: task.task_id.includes('_') ? `${task.task_id.split('_')[0]} (${new Date(task.updated_at || task.created_at).toLocaleString()})` : `任务 ${task.task_id.substring(0, 8)}... (${new Date(task.updated_at || task.created_at).toLocaleString()})`\n              }, task.task_id, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1500,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1493,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1491,\n            columnNumber: 13\n          }, this), asyncTrainingResults && asyncTrainingResults.template_info && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginTop: 8,\n              padding: 8,\n              backgroundColor: '#f6ffed',\n              border: '1px solid #b7eb8f',\n              borderRadius: 4\n            },\n            children: asyncTrainingResults.template_info.template_generated ? /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  color: '#52c41a',\n                  fontWeight: 'bold'\n                },\n                children: \"\\u2705 \\u6E05\\u6D17\\u6A21\\u677F\\u5DF2\\u81EA\\u52A8\\u751F\\u6210\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1515,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"\\u6A21\\u677F\\u8DEF\\u5F84:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1516,\n                  columnNumber: 24\n                }, this), \" \", asyncTrainingResults.template_info.template_path]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1516,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"\\u66F4\\u65B0\\u9608\\u503C\\u6570\\u91CF:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1517,\n                  columnNumber: 24\n                }, this), \" \", asyncTrainingResults.template_info.updated_thresholds]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1517,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1514,\n              columnNumber: 19\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  color: '#ff4d4f',\n                  fontWeight: 'bold'\n                },\n                children: \"\\u274C \\u6E05\\u6D17\\u6A21\\u677F\\u751F\\u6210\\u5931\\u8D25\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1521,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"\\u9519\\u8BEF\\u4FE1\\u606F:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1522,\n                  columnNumber: 24\n                }, this), \" \", asyncTrainingResults.template_info.error]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1522,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1520,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1512,\n            columnNumber: 15\n          }, this), asyncTrainingResults && asyncTrainingResults.results && /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: \"\\u9009\\u62E9\\u8981\\u67E5\\u770B\\u7684\\u534F\\u8BAE\\u548C\\u6570\\u636E\\u7C7B\\u578B\\uFF1A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1531,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Select, {\n              value: selectedAsyncResultKey,\n              onChange: setSelectedAsyncResultKey,\n              style: {\n                width: '100%',\n                marginTop: 8\n              },\n              placeholder: \"\\u8BF7\\u9009\\u62E9\\u534F\\u8BAE\\u548C\\u6570\\u636E\\u7C7B\\u578B\",\n              children: Object.keys(asyncTrainingResults.results).map(key => /*#__PURE__*/_jsxDEV(Option, {\n                value: key,\n                children: key\n              }, key, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1539,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1532,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1530,\n            columnNumber: 15\n          }, this), selectedAsyncResultKey && asyncTrainingResults && asyncTrainingResults.results[selectedAsyncResultKey] && /*#__PURE__*/_jsxDEV(TrainingResultDisplay, {\n            resultKey: selectedAsyncResultKey,\n            result: asyncTrainingResults.results[selectedAsyncResultKey]\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1549,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1489,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1481,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1318,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 789,\n    columnNumber: 5\n  }, this);\n};\n_s(ModelTrainingPage, \"lYpTMHNb6oN28MYKwVAfBNZn3xQ=\", false, function () {\n  return [useTaskManager];\n});\n_c2 = ModelTrainingPage;\nexport default ModelTrainingPage;\nvar _c, _c2;\n$RefreshReg$(_c, \"TrainingResultDisplay\");\n$RefreshReg$(_c2, \"ModelTrainingPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Card", "Radio", "Upload", "Input", "Select", "<PERSON><PERSON>", "Typography", "Space", "Divider", "message", "Spin", "InputNumber", "Slide<PERSON>", "Checkbox", "Progress", "<PERSON><PERSON>", "Row", "Col", "Statistic", "Tabs", "InboxOutlined", "PlayCircleOutlined", "SettingOutlined", "ExperimentOutlined", "PlusOutlined", "DeleteOutlined", "UploadOutlined", "Line<PERSON>hart", "Line", "XAxis", "YA<PERSON>s", "Cartesian<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Legend", "ResponsiveContainer", "modelTrainingAPI", "useTaskManager", "startMultiTrainingAsync", "jsxDEV", "_jsxDEV", "Title", "Text", "<PERSON><PERSON>", "Option", "TabPane", "TrainingResultDisplay", "<PERSON><PERSON><PERSON>", "result", "_result$train_losses", "_result$val_losses", "<PERSON><PERSON><PERSON>", "selectedDatatype", "split", "children", "direction", "size", "style", "width", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "gutter", "span", "title", "value", "train_shape", "test_shape", "r2_score", "toFixed", "r2", "precision", "valueStyle", "color", "static_anomaly_threshold", "marginTop", "cpu_percent", "memory_mb", "gpu_memory_mb", "gpu_memory", "gpu_utilization_percent", "gpu_utilization", "train_losses", "val_losses", "strong", "height", "data", "map", "trainLoss", "index", "epoch", "训练损失", "验证损失", "margin", "top", "right", "left", "bottom", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dataKey", "type", "stroke", "strokeWidth", "dot", "length", "y_test_actual", "y_pred", "actual", "实际值", "预测值", "model_save_path", "description", "scaler_y_save_path", "params_save_path", "test_save_path", "finished_time", "duration_seconds", "showIcon", "_c", "ModelTrainingPage", "_s", "trainingMode", "setTrainingMode", "dataSource", "setDataSource", "uploadedFile", "setUploadedFile", "csvDir", "setCsvDir", "availableFiles", "setAvailableFiles", "selectedFile", "setSelectedFile", "filesLoading", "setFilesLoading", "dataSources", "setDataSources", "id", "outputFolder", "enabled", "selected<PERSON><PERSON>", "setSelectedProts", "selectedDatatypes", "setSelectedDatatypes", "TCP", "learningRate", "setLearningRate", "batchSize", "setBatchSize", "epochs", "setEpochs", "sequenceLength", "setSequenceLength", "hiddenSize", "setHiddenSize", "numLayers", "setNumLayers", "dropout", "setDropout", "setOutputFolder", "autoGenerateTemplate", "setAutoGenerateTemplate", "training", "setTraining", "progress", "setProgress", "trainingResults", "setTrainingResults", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "setSelectedResultKey", "multiTrainingResults", "setMultiTrainingResults", "selectedMultiSourceKey", "setSelectedMultiSourceKey", "selectedMultiResultKey", "setSelectedMultiResultKey", "submitTrainingTask", "getCompletedTasksByType", "fetchCompletedTasks", "useAsyncTraining", "setUseAsyncTraining", "addDataSource", "newSource", "Date", "now", "toString", "removeDataSource", "filter", "source", "updateDataSource", "updates", "asyncTrainingResults", "setAsyncTrainingResults", "selectedAsyncResult<PERSON>ey", "setSelectedAsyncResultKey", "selectedAsyncTaskId", "setSelectedAsyncTaskId", "completedTrainingTasks", "handleAsyncTaskSelect", "taskId", "selectedTask", "find", "task", "task_id", "results", "Object", "keys", "latestTask", "protocolOptions", "datatypeOptions", "UDP", "ICMP", "fetchCsvFiles", "response", "listCsvFiles", "files", "error", "_error$response", "_error$response$data", "detail", "fetchCsvFilesForSource", "sourceId", "_error$response2", "_error$response2$data", "timer", "setTimeout", "clearTimeout", "uploadProps", "name", "multiple", "accept", "beforeUpload", "onChange", "info", "fileList", "getUploadPropsForSource", "file", "undefined", "handleProtocolChange", "prots", "newDatatypes", "for<PERSON>ach", "prot", "includes", "handleDatatypeChange", "protocol", "datatypes", "prev", "validateMultiTraining", "validSources", "outputPaths", "s", "uniquePaths", "Set", "handleStartTraining", "hasValidDatatypes", "some", "formData", "FormData", "append", "originFileObj", "JSON", "stringify", "success", "progressInterval", "setInterval", "clearInterval", "trainModelMulti", "firstSourceKey", "trainModel", "localTrainingData", "csv_dir", "selected_file", "selected_prots", "selected_datatypes", "learning_rate", "batch_size", "sequence_length", "hidden_size", "num_layers", "output_folder", "auto_generate_template", "trainModelLocal", "console", "log", "result_path", "errorMessage", "_error$response$data2", "_error$response$data3", "statusText", "status", "isFormValid", "level", "fontSize", "fontWeight", "marginBottom", "className", "Group", "e", "target", "icon", "onClick", "extra", "danger", "compact", "display", "placeholder", "flex", "loading", "disabled", "marginLeft", "spinning", "prefix", "mode", "datatype", "align", "min", "max", "step", "textAlign", "code", "marks", "checked", "percent", "template_info", "padding", "backgroundColor", "border", "borderRadius", "template_generated", "template_path", "updated_thresholds", "entries", "key", "sourceResult", "tab", "updated_at", "created_at", "toLocaleString", "substring", "_c2", "$RefreshReg$"], "sources": ["/home/<USER>/frontend-react-stable/src/pages/ModelTrainingPage.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Card,\n  Radio,\n  Upload,\n  Input,\n  Select,\n  Button,\n  Typography,\n  Space,\n  Divider,\n  message,\n  Spin,\n  InputNumber,\n  Slider,\n  Checkbox,\n  Progress,\n  Alert,\n  Row,\n  Col,\n  Statistic,\n  Tabs,\n} from 'antd';\nimport { InboxOutlined, PlayCircleOutlined, SettingOutlined, ExperimentOutlined, PlusOutlined, DeleteOutlined, UploadOutlined } from '@ant-design/icons';\nimport { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';\nimport { modelTrainingAPI } from '../services/api';\nimport useTaskManager from '../hooks/useTaskManager';\nimport { startMultiTrainingAsync } from '../services/taskApi';\n\nconst { Title, Text } = Typography;\nconst { Dragger } = Upload;\nconst { Option } = Select;\nconst { TabPane } = Tabs;\n\n// 数据源类型定义\ninterface TrainingDataSource {\n  id: string;\n  type: 'upload' | 'local';\n  file?: any;\n  csvDir?: string;\n  selectedFile?: string;\n  outputFolder: string;\n  enabled: boolean;\n  availableFiles?: string[];\n  filesLoading?: boolean;\n}\n\n// 训练结果展示组件\nconst TrainingResultDisplay: React.FC<{ resultKey: string; result: any }> = ({ resultKey, result }) => {\n  const [selectedProt, selectedDatatype] = resultKey.split('_', 2);\n\n  return (\n    <div>\n      <Space direction=\"vertical\" size=\"middle\" style={{ width: '100%' }}>\n        <div>\n          <Text><strong>协议:</strong> {selectedProt}</Text>\n          <br />\n          <Text><strong>数据类型:</strong> {selectedDatatype}</Text>\n        </div>\n\n        <Row gutter={16}>\n          <Col span={6}>\n            <Statistic\n              title=\"训练集数据形状\"\n              value={result.train_shape ? `${result.train_shape[0]} × ${result.train_shape[1]}` : 'N/A'}\n            />\n          </Col>\n          <Col span={6}>\n            <Statistic\n              title=\"测试集数据形状\"\n              value={result.test_shape ? `${result.test_shape[0]} × ${result.test_shape[1]}` : 'N/A'}\n            />\n          </Col>\n          <Col span={6}>\n            <Statistic\n              title=\"R² 分数\"\n              value={result.r2_score ? result.r2_score.toFixed(4) : (result.r2 ? result.r2.toFixed(4) : 'N/A')}\n              precision={4}\n              valueStyle={{ color: result.r2_score > 0.8 || result.r2 > 0.8 ? '#3f8600' : '#cf1322' }}\n            />\n          </Col>\n          <Col span={6}>\n            <Statistic\n              title=\"建议清洗阈值\"\n              value={result.static_anomaly_threshold ? result.static_anomaly_threshold.toFixed(2) : 'N/A'}\n              precision={2}\n            />\n          </Col>\n        </Row>\n\n        <Row gutter={16} style={{ marginTop: 16 }}>\n          <Col span={6}>\n            <Statistic\n              title=\"CPU使用率\"\n              value={result.cpu_percent ? `${result.cpu_percent.toFixed(2)}%` : 'N/A'}\n              valueStyle={{ color: '#1890ff' }}\n            />\n          </Col>\n          <Col span={6}>\n            <Statistic\n              title=\"内存使用\"\n              value={result.memory_mb ? `${result.memory_mb.toFixed(2)} MB` : 'N/A'}\n              valueStyle={{ color: '#1890ff' }}\n            />\n          </Col>\n          <Col span={6}>\n            <Statistic\n              title=\"GPU内存\"\n              value={result.gpu_memory_mb ? `${result.gpu_memory_mb.toFixed(2)} MB` : (result.gpu_memory ? `${result.gpu_memory.toFixed(2)} MB` : 'N/A')}\n              valueStyle={{ color: '#1890ff' }}\n            />\n          </Col>\n          <Col span={6}>\n            <Statistic\n              title=\"GPU使用率\"\n              value={result.gpu_utilization_percent ? `${result.gpu_utilization_percent.toFixed(2)}%` : (result.gpu_utilization ? `${result.gpu_utilization.toFixed(2)}%` : 'N/A')}\n              valueStyle={{ color: '#1890ff' }}\n            />\n          </Col>\n        </Row>\n\n        {result.train_losses && result.val_losses && (\n          <div>\n            <Text strong>训练损失曲线</Text>\n            <div style={{ height: 300, marginTop: 8 }}>\n              <ResponsiveContainer width=\"100%\" height=\"100%\">\n                <LineChart\n                  data={result.train_losses.map((trainLoss: number, index: number) => ({\n                    epoch: index + 1,\n                    训练损失: trainLoss,\n                    验证损失: result.val_losses[index] || null,\n                  }))}\n                  margin={{ top: 5, right: 30, left: 20, bottom: 5 }}\n                >\n                  <CartesianGrid strokeDasharray=\"3 3\" />\n                  <XAxis dataKey=\"epoch\" />\n                  <YAxis />\n                  <Tooltip />\n                  <Legend />\n                  <Line\n                    type=\"monotone\"\n                    dataKey=\"训练损失\"\n                    stroke=\"#8884d8\"\n                    strokeWidth={2}\n                    dot={false}\n                  />\n                  <Line\n                    type=\"monotone\"\n                    dataKey=\"验证损失\"\n                    stroke=\"#82ca9d\"\n                    strokeWidth={2}\n                    dot={false}\n                  />\n                </LineChart>\n              </ResponsiveContainer>\n            </div>\n            <div style={{ marginTop: 8 }}>\n              <Text type=\"secondary\">\n                训练轮数: {result.train_losses.length} epochs |\n                最终训练损失: {result.train_losses[result.train_losses.length - 1]?.toFixed(6)} |\n                最终验证损失: {result.val_losses[result.val_losses.length - 1]?.toFixed(6)}\n              </Text>\n            </div>\n          </div>\n        )}\n\n        {result.y_test_actual && result.y_pred && result.y_test_actual.length > 0 && (\n          <div>\n            <Text strong>实际值 vs 预测值对比图</Text>\n            <div style={{ height: 300, marginTop: 8 }}>\n              <ResponsiveContainer width=\"100%\" height=\"100%\">\n                <LineChart\n                  data={result.y_test_actual.map((actual: number, index: number) => ({\n                    index: index + 1,\n                    实际值: actual,\n                    预测值: result.y_pred[index],\n                  }))}\n                  margin={{ top: 5, right: 30, left: 20, bottom: 5 }}\n                >\n                  <CartesianGrid strokeDasharray=\"3 3\" />\n                  <XAxis dataKey=\"index\" />\n                  <YAxis />\n                  <Tooltip />\n                  <Legend />\n                  <Line\n                    type=\"monotone\"\n                    dataKey=\"实际值\"\n                    stroke=\"#8884d8\"\n                    strokeWidth={2}\n                    dot={false}\n                  />\n                  <Line\n                    type=\"monotone\"\n                    dataKey=\"预测值\"\n                    stroke=\"#82ca9d\"\n                    strokeWidth={2}\n                    dot={false}\n                  />\n                </LineChart>\n              </ResponsiveContainer>\n            </div>\n            <div style={{ marginTop: 8 }}>\n              <Text type=\"secondary\">\n                显示所有 {result.y_test_actual.length} 个测试样本的预测对比 |\n                R² 分数: {result.r2_score ? result.r2_score.toFixed(4) : (result.r2 ? result.r2.toFixed(4) : 'N/A')} |\n                建议阈值: {result.static_anomaly_threshold ? result.static_anomaly_threshold.toFixed(2) : 'N/A'}\n              </Text>\n            </div>\n          </div>\n        )}\n\n\n\n        {result.model_save_path && (\n          <Alert\n            message=\"模型文件信息\"\n            description={\n              <div>\n                <p><strong>模型保存路径:</strong> {result.model_save_path}</p>\n                {result.scaler_y_save_path && (\n                  <p><strong>标准化器保存路径:</strong> {result.scaler_y_save_path}</p>\n                )}\n                {result.params_save_path && (\n                  <p><strong>参数保存路径:</strong> {result.params_save_path}</p>\n                )}\n                {result.test_save_path && (\n                  <p><strong>测试数据保存路径:</strong> {result.test_save_path}</p>\n                )}\n                {result.static_anomaly_threshold && (\n                  <p><strong>建议清洗阈值:</strong> {result.static_anomaly_threshold.toFixed(2)}</p>\n                )}\n                {result.finished_time && (\n                  <p><strong>训练完成时间:</strong> {result.finished_time}</p>\n                )}\n                {result.duration_seconds && (\n                  <p><strong>训练耗时:</strong> {result.duration_seconds.toFixed(2)} 秒</p>\n                )}\n              </div>\n            }\n            type=\"info\"\n            showIcon\n          />\n        )}\n      </Space>\n    </div>\n  );\n};\n\nconst ModelTrainingPage: React.FC = () => {\n  // 训练模式：single（单文件）或 multi（多文件）\n  const [trainingMode, setTrainingMode] = useState<'single' | 'multi'>('single');\n\n  // 单文件模式的状态（保持向后兼容）\n  const [dataSource, setDataSource] = useState<'upload' | 'local'>('local');\n  const [uploadedFile, setUploadedFile] = useState<any>(null);\n  const [csvDir, setCsvDir] = useState('');\n  const [availableFiles, setAvailableFiles] = useState<string[]>([]);\n  const [selectedFile, setSelectedFile] = useState<string>('');\n  const [filesLoading, setFilesLoading] = useState(false);\n\n  // 多文件模式的状态\n  const [dataSources, setDataSources] = useState<TrainingDataSource[]>([\n    {\n      id: '1',\n      type: 'local',\n      outputFolder: '/data/output',\n      enabled: true,\n      availableFiles: [],\n      filesLoading: false\n    }\n  ]);\n\n  // 协议和数据类型选择（与Streamlit版本一致）\n  const [selectedProts, setSelectedProts] = useState<string[]>(['TCP']);\n  const [selectedDatatypes, setSelectedDatatypes] = useState<{[key: string]: string[]}>({\n    TCP: ['spt_sip_dip']\n  });\n\n  // 训练参数\n  const [learningRate, setLearningRate] = useState(0.0001);\n  const [batchSize, setBatchSize] = useState(64);\n  const [epochs, setEpochs] = useState(100);\n  const [sequenceLength, setSequenceLength] = useState(10);\n  const [hiddenSize, setHiddenSize] = useState(50);\n  const [numLayers, setNumLayers] = useState(2);\n  const [dropout, setDropout] = useState(0.2);\n  const [outputFolder, setOutputFolder] = useState('');\n  const [autoGenerateTemplate, setAutoGenerateTemplate] = useState(false); // 新增：是否自动生成清洗模板\n\n  // 训练状态\n  const [training, setTraining] = useState(false);\n  const [progress, setProgress] = useState(0);\n  const [trainingResults, setTrainingResults] = useState<any>(null);\n  const [selectedResultKey, setSelectedResultKey] = useState<string>('');\n\n  // 多文件训练结果状态\n  const [multiTrainingResults, setMultiTrainingResults] = useState<any>(null);\n  const [selectedMultiSourceKey, setSelectedMultiSourceKey] = useState<string>('');\n  const [selectedMultiResultKey, setSelectedMultiResultKey] = useState<string>('');\n\n  // 任务管理\n  const { submitTrainingTask, getCompletedTasksByType, fetchCompletedTasks } = useTaskManager();\n  const [useAsyncTraining, setUseAsyncTraining] = useState(true); // 默认使用异步训练\n\n  // 多数据源管理函数\n  const addDataSource = () => {\n    const newSource: TrainingDataSource = {\n      id: Date.now().toString(),\n      type: 'local',\n      outputFolder: `/data/output/model_${dataSources.length + 1}`,\n      enabled: true,\n      availableFiles: [],\n      filesLoading: false\n    };\n    setDataSources([...dataSources, newSource]);\n  };\n\n  const removeDataSource = (id: string) => {\n    setDataSources(dataSources.filter(source => source.id !== id));\n  };\n\n  const updateDataSource = (id: string, updates: Partial<TrainingDataSource>) => {\n    setDataSources(dataSources.map(source =>\n      source.id === id ? { ...source, ...updates } : source\n    ));\n  };\n\n  // 异步任务结果状态\n  const [asyncTrainingResults, setAsyncTrainingResults] = useState<any>(null);\n  const [selectedAsyncResultKey, setSelectedAsyncResultKey] = useState<string>('');\n  const [selectedAsyncTaskId, setSelectedAsyncTaskId] = useState<string>('');\n\n  // 获取已完成的训练任务\n  const completedTrainingTasks = getCompletedTasksByType('training');\n\n  // 处理异步任务选择\n  const handleAsyncTaskSelect = (taskId: string) => {\n    setSelectedAsyncTaskId(taskId);\n    const selectedTask = completedTrainingTasks.find(task => task.task_id === taskId);\n    if (selectedTask && selectedTask.result && selectedTask.result.results) {\n      setAsyncTrainingResults(selectedTask.result);\n      setSelectedAsyncResultKey(Object.keys(selectedTask.result.results)[0]);\n    }\n  };\n\n  // 页面加载时获取已完成任务\n  useEffect(() => {\n    fetchCompletedTasks();\n  }, [fetchCompletedTasks]);\n\n  // 自动选择最新的训练任务\n  useEffect(() => {\n    if (completedTrainingTasks.length > 0 && !selectedAsyncTaskId) {\n      const latestTask = completedTrainingTasks[completedTrainingTasks.length - 1];\n      handleAsyncTaskSelect(latestTask.task_id);\n    }\n  }, [completedTrainingTasks, selectedAsyncTaskId, handleAsyncTaskSelect]);\n\n  // 协议和数据类型配置（与Streamlit版本完全一致）\n  const protocolOptions = ['TCP', 'UDP', 'ICMP'];\n  const datatypeOptions = {\n    TCP: ['spt_sip_dip', 'dpt_sip_dip', 'len_dpt_syn', 'seq_ack_dip'],\n    UDP: ['spt_sip_dip', 'dpt_sip_dip'],\n    ICMP: ['dip']\n  };\n\n  // 获取CSV文件列表（单文件模式）\n  const fetchCsvFiles = async () => {\n    if (!csvDir) return;\n\n    setFilesLoading(true);\n    try {\n      const response = await modelTrainingAPI.listCsvFiles(csvDir);\n      setAvailableFiles(response.data.files || []);\n    } catch (error: any) {\n      message.error(error.response?.data?.detail || '获取文件列表失败');\n      setAvailableFiles([]);\n    } finally {\n      setFilesLoading(false);\n    }\n  };\n\n  // 获取多数据源的CSV文件列表\n  const fetchCsvFilesForSource = async (sourceId: string, csvDir: string) => {\n    if (!csvDir) return;\n\n    updateDataSource(sourceId, { filesLoading: true });\n    try {\n      const response = await modelTrainingAPI.listCsvFiles(csvDir);\n      updateDataSource(sourceId, {\n        availableFiles: response.data.files || [],\n        filesLoading: false\n      });\n    } catch (error: any) {\n      message.error(error.response?.data?.detail || '获取文件列表失败');\n      updateDataSource(sourceId, {\n        availableFiles: [],\n        filesLoading: false\n      });\n    }\n  };\n\n  // 使用防抖来避免频繁请求 - 只在用户停止输入1.5秒后才发起请求\n  useEffect(() => {\n    if (dataSource === 'local' && csvDir && csvDir.length > 3) { // 至少输入4个字符才开始请求\n      const timer = setTimeout(() => {\n        fetchCsvFiles();\n      }, 1500); // 1.5秒延迟，给用户足够时间输入完整路径\n\n      return () => clearTimeout(timer);\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [dataSource, csvDir]);\n\n  // 文件上传配置（单文件模式）\n  const uploadProps = {\n    name: 'file',\n    multiple: false,\n    accept: '.csv',\n    beforeUpload: () => false,\n    onChange: (info: any) => {\n      if (info.fileList.length > 0) {\n        setUploadedFile(info.fileList[0]);\n      } else {\n        setUploadedFile(null);\n      }\n    },\n  };\n\n  // 多数据源文件上传配置\n  const getUploadPropsForSource = (sourceId: string) => ({\n    name: 'file',\n    multiple: false,\n    accept: '.csv',\n    beforeUpload: () => false,\n    onChange: (info: any) => {\n      if (info.fileList.length > 0) {\n        updateDataSource(sourceId, { file: info.fileList[0] });\n      } else {\n        updateDataSource(sourceId, { file: undefined });\n      }\n    },\n  });\n\n  // 协议选择变化处理（与Streamlit版本一致）\n  const handleProtocolChange = (prots: string[]) => {\n    setSelectedProts(prots);\n    // 为新选择的协议添加默认数据类型\n    const newDatatypes = { ...selectedDatatypes };\n    prots.forEach(prot => {\n      if (!newDatatypes[prot] && datatypeOptions[prot as keyof typeof datatypeOptions]) {\n        newDatatypes[prot] = [datatypeOptions[prot as keyof typeof datatypeOptions][0]];\n      }\n    });\n    // 移除未选择协议的数据类型\n    Object.keys(newDatatypes).forEach(prot => {\n      if (!prots.includes(prot)) {\n        delete newDatatypes[prot];\n      }\n    });\n    setSelectedDatatypes(newDatatypes);\n  };\n\n  // 数据类型选择变化处理\n  const handleDatatypeChange = (protocol: string, datatypes: string[]) => {\n    setSelectedDatatypes(prev => ({\n      ...prev,\n      [protocol]: datatypes\n    }));\n  };\n\n  // 验证多文件训练输入\n  const validateMultiTraining = (): boolean => {\n    // 检查是否至少有一个有效的数据源\n    const validSources = dataSources.filter(source => {\n      if (source.type === 'upload') {\n        return source.file && source.outputFolder;\n      } else {\n        return source.csvDir && source.selectedFile && source.outputFolder;\n      }\n    });\n\n    if (validSources.length === 0) {\n      message.error('请至少配置一个有效的数据源');\n      return false;\n    }\n\n    // 检查输出路径是否重复\n    const outputPaths = validSources.map(s => s.outputFolder);\n    const uniquePaths = new Set(outputPaths);\n    if (outputPaths.length !== uniquePaths.size) {\n      message.error('模型保存路径不能重复');\n      return false;\n    }\n\n    return true;\n  };\n\n  // 开始训练\n  const handleStartTraining = async () => {\n    // 根据训练模式进行不同的验证\n    if (trainingMode === 'multi') {\n      if (!validateMultiTraining()) {\n        return;\n      }\n    } else {\n      // 单文件模式验证（保持原有逻辑）\n      if (dataSource === 'upload' && !uploadedFile) {\n        message.error('请上传CSV文件');\n        return;\n      }\n\n      if (dataSource === 'local' && (!csvDir || !selectedFile)) {\n        message.error('请选择CSV文件');\n        return;\n      }\n    }\n\n    if (selectedProts.length === 0) {\n      message.error('请至少选择一种协议');\n      return;\n    }\n\n    const hasValidDatatypes = selectedProts.some(prot =>\n      selectedDatatypes[prot] && selectedDatatypes[prot].length > 0\n    );\n\n    if (!hasValidDatatypes) {\n      message.error('请为每个协议至少选择一种数据类型');\n      return;\n    }\n\n    setTraining(true);\n    setProgress(0);\n    setTrainingResults(null);\n    setSelectedResultKey('');\n\n    try {\n      if (useAsyncTraining) {\n        // 异步训练模式\n        if (trainingMode === 'multi') {\n          // 多文件异步训练\n          const formData = new FormData();\n\n          // 添加文件\n          dataSources.forEach((source) => {\n            if (source.type === 'upload' && source.file) {\n              formData.append('files', source.file.originFileObj);\n            }\n          });\n\n          // 添加数据源配置\n          formData.append('data_sources', JSON.stringify(dataSources));\n          formData.append('selected_prots', JSON.stringify(selectedProts));\n          formData.append('selected_datatypes', JSON.stringify(selectedDatatypes));\n          formData.append('learning_rate', learningRate.toString());\n          formData.append('batch_size', batchSize.toString());\n          formData.append('epochs', epochs.toString());\n          formData.append('sequence_length', sequenceLength.toString());\n          formData.append('hidden_size', hiddenSize.toString());\n          formData.append('num_layers', numLayers.toString());\n          formData.append('dropout', dropout.toString());\n          formData.append('auto_generate_template', autoGenerateTemplate.toString());\n\n          // 提交多文件异步任务\n          const taskId = await startMultiTrainingAsync(formData);\n\n          if (taskId) {\n            message.success(`多文件训练任务已启动（共${dataSources.length}个数据源），您可以继续使用其他功能，任务完成后会收到通知`);\n            // 重置状态\n            setTraining(false);\n            setProgress(0);\n          }\n\n          return; // 异步模式下直接返回\n        } else {\n          // 单文件异步训练（保持原有逻辑）\n          const formData = new FormData();\n\n          if (dataSource === 'upload') {\n            formData.append('file', uploadedFile.originFileObj);\n          } else {\n            formData.append('csv_dir', csvDir);\n            formData.append('selected_file', selectedFile);\n          }\n\n          formData.append('selected_prots', JSON.stringify(selectedProts));\n          formData.append('selected_datatypes', JSON.stringify(selectedDatatypes));\n          formData.append('learning_rate', learningRate.toString());\n          formData.append('batch_size', batchSize.toString());\n          formData.append('epochs', epochs.toString());\n          formData.append('sequence_length', sequenceLength.toString());\n          formData.append('hidden_size', hiddenSize.toString());\n          formData.append('num_layers', numLayers.toString());\n          formData.append('dropout', dropout.toString());\n          formData.append('output_folder', outputFolder);\n          formData.append('auto_generate_template', autoGenerateTemplate.toString());\n\n          // 提交异步任务\n          const taskId = await submitTrainingTask(formData);\n\n          if (taskId) {\n            message.success('训练任务已启动，您可以继续使用其他功能，任务完成后会收到通知');\n            // 重置状态\n            setTraining(false);\n            setProgress(0);\n          }\n\n          return; // 异步模式下直接返回\n        }\n      }\n\n      // 同步训练模式\n      let response;\n\n      if (trainingMode === 'multi') {\n        // 多文件同步训练\n        const formData = new FormData();\n\n        // 添加文件\n        dataSources.forEach((source) => {\n          if (source.type === 'upload' && source.file) {\n            formData.append('files', source.file.originFileObj);\n          }\n        });\n\n        // 添加数据源配置\n        formData.append('data_sources', JSON.stringify(dataSources));\n        formData.append('selected_prots', JSON.stringify(selectedProts));\n        formData.append('selected_datatypes', JSON.stringify(selectedDatatypes));\n        formData.append('learning_rate', learningRate.toString());\n        formData.append('batch_size', batchSize.toString());\n        formData.append('epochs', epochs.toString());\n        formData.append('sequence_length', sequenceLength.toString());\n        formData.append('hidden_size', hiddenSize.toString());\n        formData.append('num_layers', numLayers.toString());\n        formData.append('dropout', dropout.toString());\n        formData.append('auto_generate_template', autoGenerateTemplate.toString());\n\n        // 模拟进度更新\n        const progressInterval = setInterval(() => {\n          setProgress((prev) => {\n            if (prev >= 90) {\n              clearInterval(progressInterval);\n              return prev;\n            }\n            return prev + 5;\n          });\n        }, 1000);\n\n        response = await modelTrainingAPI.trainModelMulti(formData);\n        clearInterval(progressInterval);\n\n        // 设置多文件训练结果\n        setMultiTrainingResults(response.data);\n        if (response.data.results && Object.keys(response.data.results).length > 0) {\n          const firstSourceKey = Object.keys(response.data.results)[0];\n          setSelectedMultiSourceKey(firstSourceKey);\n          if (response.data.results[firstSourceKey] && response.data.results[firstSourceKey].results) {\n            setSelectedMultiResultKey(Object.keys(response.data.results[firstSourceKey].results)[0]);\n          }\n        }\n      } else if (dataSource === 'upload') {\n        const formData = new FormData();\n        formData.append('file', uploadedFile.originFileObj);\n        formData.append('selected_prots', JSON.stringify(selectedProts));\n        formData.append('selected_datatypes', JSON.stringify(selectedDatatypes));\n        formData.append('learning_rate', learningRate.toString());\n        formData.append('batch_size', batchSize.toString());\n        formData.append('epochs', epochs.toString());\n        formData.append('sequence_length', sequenceLength.toString());\n        formData.append('hidden_size', hiddenSize.toString());\n        formData.append('num_layers', numLayers.toString());\n        formData.append('dropout', dropout.toString());\n        formData.append('output_folder', outputFolder);\n        formData.append('auto_generate_template', autoGenerateTemplate.toString());\n\n        // 模拟进度更新\n        const progressInterval = setInterval(() => {\n          setProgress((prev) => {\n            if (prev >= 90) {\n              clearInterval(progressInterval);\n              return prev;\n            }\n            return prev + 5;\n          });\n        }, 1000);\n\n        response = await modelTrainingAPI.trainModel(formData);\n        clearInterval(progressInterval);\n      } else {\n        // 本地文件训练逻辑\n        const localTrainingData = {\n          csv_dir: csvDir,\n          selected_file: selectedFile,\n          selected_prots: selectedProts,\n          selected_datatypes: selectedDatatypes,\n          learning_rate: learningRate,\n          batch_size: batchSize,\n          epochs: epochs,\n          sequence_length: sequenceLength,\n          hidden_size: hiddenSize,\n          num_layers: numLayers,\n          dropout: dropout,\n          output_folder: outputFolder,\n          auto_generate_template: autoGenerateTemplate\n        };\n\n        // 模拟进度更新\n        const progressInterval = setInterval(() => {\n          setProgress((prev) => {\n            if (prev >= 90) {\n              clearInterval(progressInterval);\n              return prev;\n            }\n            return prev + 5;\n          });\n        }, 1000);\n\n        response = await modelTrainingAPI.trainModelLocal(localTrainingData);\n        clearInterval(progressInterval);\n      }\n\n      setProgress(100);\n\n      // 添加调试信息\n      console.log('训练响应数据:', response.data);\n\n      setTrainingResults(response.data);\n\n      // 设置默认选择第一个结果\n      if (response.data.results && Object.keys(response.data.results).length > 0) {\n        setSelectedResultKey(Object.keys(response.data.results)[0]);\n      }\n\n      message.success('模型训练完成！');\n\n      // 显示训练结果路径信息\n      if (response.data.result_path) {\n        message.info(`结果已保存至: ${response.data.result_path}`);\n      }\n\n    } catch (error: any) {\n      console.error('训练错误详情:', error);\n      console.error('错误响应:', error.response);\n\n      // 更详细的错误信息\n      let errorMessage = '模型训练失败';\n      if (error.response) {\n        if (error.response.data?.detail) {\n          errorMessage = error.response.data.detail;\n        } else if (error.response.data?.message) {\n          errorMessage = error.response.data.message;\n        } else if (error.response.statusText) {\n          errorMessage = `请求失败: ${error.response.status} ${error.response.statusText}`;\n        }\n      } else if (error.message) {\n        errorMessage = error.message;\n      }\n\n      message.error(errorMessage);\n    } finally {\n      setTraining(false);\n    }\n  };\n\n  const isFormValid = () => {\n    if (trainingMode === 'multi') {\n      // 多文件模式验证\n      const validSources = dataSources.filter(source => {\n        if (source.type === 'upload') {\n          return source.file && source.outputFolder;\n        } else {\n          return source.csvDir && source.selectedFile && source.outputFolder;\n        }\n      });\n      return validSources.length > 0 && selectedProts.length > 0;\n    } else {\n      // 单文件模式验证（保持原有逻辑）\n      if (dataSource === 'upload') {\n        return uploadedFile && selectedProts.length > 0;\n      } else {\n        return csvDir && selectedFile && selectedProts.length > 0;\n      }\n    }\n  };\n\n  return (\n    <div>\n      <Title level={3} style={{ fontSize: '20px', fontWeight: 600, marginBottom: '8px' }}>模型训练与特征预测</Title>\n      <Text type=\"secondary\">\n        上传或选择CSV文件，配置训练参数，根据多维特征训练流量检测模型，并进行特征预测。\n      </Text>\n\n      <Divider />\n\n      {/* 训练模式选择 */}\n      <Card title=\"训练模式\" className=\"function-card\">\n        <Space direction=\"vertical\" size=\"large\" style={{ width: '100%' }}>\n          <div>\n            <Text strong>选择训练模式：</Text>\n            <Radio.Group\n              value={trainingMode}\n              onChange={(e) => setTrainingMode(e.target.value)}\n              style={{ marginTop: 8 }}\n            >\n              <Radio value=\"single\">单文件训练</Radio>\n              <Radio value=\"multi\">多文件批量训练</Radio>\n            </Radio.Group>\n          </div>\n\n          {trainingMode === 'multi' && (\n            <Alert\n              message=\"多文件批量训练模式\"\n              description=\"您可以添加多个数据源（本地文件或上传文件），为每个数据源配置独立的模型保存路径，系统将为每个数据源训练独立的模型。\"\n              type=\"info\"\n              showIcon\n            />\n          )}\n        </Space>\n      </Card>\n\n      {/* 数据源配置 */}\n      <Card title={trainingMode === 'multi' ? \"数据源配置\" : \"数据源\"} className=\"function-card\">\n        {trainingMode === 'multi' ? (\n          <Space direction=\"vertical\" size=\"large\" style={{ width: '100%' }}>\n            {/* 添加数据源按钮 */}\n            <div style={{ marginBottom: 16 }}>\n              <Button\n                type=\"dashed\"\n                icon={<PlusOutlined />}\n                onClick={addDataSource}\n                style={{ width: '100%' }}\n              >\n                添加数据源\n              </Button>\n            </div>\n\n            {/* 数据源列表 */}\n            {dataSources.map((source, index) => (\n              <Card\n                key={source.id}\n                size=\"small\"\n                title={`数据源 ${index + 1}`}\n                extra={\n                  dataSources.length > 1 && (\n                    <Button\n                      type=\"text\"\n                      danger\n                      icon={<DeleteOutlined />}\n                      onClick={() => removeDataSource(source.id)}\n                    />\n                  )\n                }\n                style={{ marginBottom: 16 }}\n              >\n                <Space direction=\"vertical\" style={{ width: '100%' }}>\n                  {/* 数据源类型选择 */}\n                  <div>\n                    <Text strong>数据源类型：</Text>\n                    <Radio.Group\n                      value={source.type}\n                      onChange={(e) => updateDataSource(source.id, { type: e.target.value })}\n                      style={{ marginTop: 8 }}\n                    >\n                      <Radio value=\"local\">本地文件</Radio>\n                      <Radio value=\"upload\">上传文件</Radio>\n                    </Radio.Group>\n                  </div>\n\n                  {/* 本地文件选择 */}\n                  {source.type === 'local' && (\n                    <Space direction=\"vertical\" style={{ width: '100%' }}>\n                      <div>\n                        <Text strong>CSV文件目录：</Text>\n                        <Input.Group compact style={{ marginTop: 8, display: 'flex' }}>\n                          <Input\n                            value={source.csvDir}\n                            onChange={(e) => updateDataSource(source.id, { csvDir: e.target.value })}\n                            placeholder=\"例如: /home/<USER>\"\n                            style={{ flex: 1 }}\n                          />\n                          <Button\n                            type=\"primary\"\n                            onClick={() => fetchCsvFilesForSource(source.id, source.csvDir || '')}\n                            loading={source.filesLoading}\n                            disabled={!source.csvDir}\n                            style={{ marginLeft: 8 }}\n                          >\n                            刷新\n                          </Button>\n                        </Input.Group>\n                      </div>\n\n                      <div>\n                        <Text strong>选择文件：</Text>\n                        <Spin spinning={source.filesLoading || false}>\n                          <Select\n                            value={source.selectedFile}\n                            onChange={(value) => updateDataSource(source.id, { selectedFile: value })}\n                            placeholder=\"请选择CSV文件\"\n                            style={{ width: '100%', marginTop: 8 }}\n                            loading={source.filesLoading}\n                          >\n                            {(source.availableFiles || []).map((file) => (\n                              <Option key={file} value={file}>\n                                {file}\n                              </Option>\n                            ))}\n                          </Select>\n                        </Spin>\n                      </div>\n                    </Space>\n                  )}\n\n                  {/* 文件上传 */}\n                  {source.type === 'upload' && (\n                    <div>\n                      <Text strong>上传文件：</Text>\n                      <Upload {...getUploadPropsForSource(source.id)} style={{ marginTop: 8 }}>\n                        <Button icon={<UploadOutlined />}>选择CSV文件</Button>\n                      </Upload>\n                      {source.file && (\n                        <div style={{ marginTop: 8 }}>\n                          <Text type=\"secondary\">已选择: {source.file.name}</Text>\n                        </div>\n                      )}\n                    </div>\n                  )}\n\n                  {/* 输出路径配置 */}\n                  <div>\n                    <Text strong>模型保存路径：</Text>\n                    <Input\n                      value={source.outputFolder}\n                      onChange={(e) => updateDataSource(source.id, { outputFolder: e.target.value })}\n                      placeholder=\"例如: /data/output/model1\"\n                      style={{ marginTop: 8 }}\n                      prefix={<InboxOutlined />}\n                    />\n                  </div>\n                </Space>\n              </Card>\n            ))}\n          </Space>\n        ) : (\n          <Space direction=\"vertical\" size=\"large\" style={{ width: '100%' }}>\n            <div>\n              <Text strong>训练数据源：</Text>\n              <Radio.Group\n                value={dataSource}\n                onChange={(e) => setDataSource(e.target.value)}\n                style={{ marginTop: 8 }}\n              >\n                <Radio value=\"local\">选择本地CSV文件</Radio>\n                <Radio value=\"upload\">上传CSV文件</Radio>\n              </Radio.Group>\n            </div>\n\n            {/* 本地文件选择 */}\n            {dataSource === 'local' && (\n            <Space direction=\"vertical\" style={{ width: '100%' }}>\n              <div>\n                <Text strong>CSV文件目录：</Text>\n                <Input.Group compact style={{ marginTop: 8, display: 'flex' }}>\n                  <Input\n                    value={csvDir}\n                    onChange={(e) => setCsvDir(e.target.value)}\n                    placeholder=\"例如: /home/<USER>\"\n                    style={{ flex: 1 }}\n                  />\n                  <Button\n                    type=\"primary\"\n                    onClick={fetchCsvFiles}\n                    loading={filesLoading}\n                    disabled={!csvDir}\n                    style={{ marginLeft: 8 }}\n                  >\n                    刷新\n                  </Button>\n                </Input.Group>\n              </div>\n\n              <div>\n                <Text strong>选择文件：</Text>\n                <Spin spinning={filesLoading}>\n                  <Select\n                    value={selectedFile}\n                    onChange={setSelectedFile}\n                    placeholder=\"请选择CSV文件\"\n                    style={{ width: '100%', marginTop: 8 }}\n                    loading={filesLoading}\n                  >\n                    {availableFiles.map((file) => (\n                      <Option key={file} value={file}>\n                        {file}\n                      </Option>\n                    ))}\n                  </Select>\n                </Spin>\n              </div>\n            </Space>\n          )}\n\n          {/* 文件上传 */}\n          {dataSource === 'upload' && (\n            <div>\n              <Text strong>上传文件：</Text>\n              <Dragger {...uploadProps} style={{ marginTop: 8 }}>\n                <p className=\"ant-upload-drag-icon\">\n                  <InboxOutlined />\n                </p>\n                <p className=\"ant-upload-text\">点击或拖拽CSV文件到此区域上传</p>\n                <p className=\"ant-upload-hint\">\n                  支持CSV格式的流量数据文件\n                </p>\n              </Dragger>\n            </div>\n          )}\n          </Space>\n        )}\n      </Card>\n\n      {/* 协议和数据类型选择 */}\n      <Card title=\"协议和数据类型选择\" className=\"function-card\">\n        <Space direction=\"vertical\" size=\"large\" style={{ width: '100%' }}>\n          <div>\n            <Text strong>选择协议：</Text>\n            <Select\n              mode=\"multiple\"\n              value={selectedProts}\n              onChange={handleProtocolChange}\n              placeholder=\"请选择协议\"\n              style={{ width: '100%', marginTop: 8 }}\n            >\n              {protocolOptions.map((prot) => (\n                <Option key={prot} value={prot}>\n                  {prot}\n                </Option>\n              ))}\n            </Select>\n          </div>\n\n          {selectedProts.map((prot) => (\n            <div key={prot}>\n              <Text strong>{prot} 数据类型：</Text>\n              <Checkbox.Group\n                value={selectedDatatypes[prot] || []}\n                onChange={(datatypes) => handleDatatypeChange(prot, datatypes as string[])}\n                style={{ marginTop: 8 }}\n              >\n                {(datatypeOptions[prot as keyof typeof datatypeOptions] || []).map((datatype) => (\n                  <Checkbox key={datatype} value={datatype}>\n                    {datatype}\n                  </Checkbox>\n                ))}\n              </Checkbox.Group>\n            </div>\n          ))}\n        </Space>\n      </Card>\n\n      {/* 训练参数配置 */}\n      <Card\n        title={\n          <Space>\n            <SettingOutlined />\n            <span>训练参数配置</span>\n          </Space>\n        }\n        className=\"function-card\"\n      >\n        <Row gutter={[24, 24]}>\n          {/* 基础参数 */}\n          <Col span={12}>\n            <Card\n              size=\"small\"\n              title={\n                <Space>\n                  <ExperimentOutlined />\n                  <Text strong>基础参数</Text>\n                </Space>\n              }\n            >\n              <Space direction=\"vertical\" size=\"middle\" style={{ width: '100%' }}>\n                <Row align=\"middle\">\n                  <Col span={10}>\n                    <Text strong>学习率：</Text>\n                  </Col>\n                  <Col span={14}>\n                    <InputNumber\n                      value={learningRate}\n                      onChange={(value) => setLearningRate(value || 0.0001)}\n                      min={0.0001}\n                      max={1}\n                      step={0.0001}\n                      style={{ width: '100%' }}\n                      placeholder=\"0.0001\"\n                    />\n                  </Col>\n                </Row>\n                <Row align=\"middle\">\n                  <Col span={10}>\n                    <Text strong>批量大小：</Text>\n                  </Col>\n                  <Col span={14}>\n                    <InputNumber\n                      value={batchSize}\n                      onChange={(value) => setBatchSize(value || 64)}\n                      min={1}\n                      max={512}\n                      style={{ width: '100%' }}\n                      placeholder=\"64\"\n                    />\n                  </Col>\n                </Row>\n                <Row align=\"middle\">\n                  <Col span={10}>\n                    <Text strong>训练轮数：</Text>\n                  </Col>\n                  <Col span={14}>\n                    <InputNumber\n                      value={epochs}\n                      onChange={(value) => setEpochs(value || 100)}\n                      min={1}\n                      max={1000}\n                      style={{ width: '100%' }}\n                      placeholder=\"100\"\n                    />\n                  </Col>\n                </Row>\n              </Space>\n            </Card>\n          </Col>\n\n          {/* 模型参数 */}\n          <Col span={12}>\n            <Card\n              size=\"small\"\n              title={\n                <Space>\n                  <SettingOutlined />\n                  <Text strong>模型参数</Text>\n                </Space>\n              }\n            >\n              <Space direction=\"vertical\" size=\"middle\" style={{ width: '100%' }}>\n                <Row align=\"middle\">\n                  <Col span={10}>\n                    <Text strong>序列长度：</Text>\n                  </Col>\n                  <Col span={14}>\n                    <InputNumber\n                      value={sequenceLength}\n                      onChange={(value) => setSequenceLength(value || 10)}\n                      min={1}\n                      max={100}\n                      style={{ width: '100%' }}\n                      placeholder=\"10\"\n                    />\n                  </Col>\n                </Row>\n                <Row align=\"middle\">\n                  <Col span={10}>\n                    <Text strong>隐藏层大小：</Text>\n                  </Col>\n                  <Col span={14}>\n                    <InputNumber\n                      value={hiddenSize}\n                      onChange={(value) => setHiddenSize(value || 50)}\n                      min={10}\n                      max={512}\n                      step={10}\n                      style={{ width: '100%' }}\n                      placeholder=\"50\"\n                    />\n                  </Col>\n                </Row>\n                <Row align=\"middle\">\n                  <Col span={10}>\n                    <Text strong>层数：</Text>\n                  </Col>\n                  <Col span={14}>\n                    <InputNumber\n                      value={numLayers}\n                      onChange={(value) => setNumLayers(value || 2)}\n                      min={1}\n                      max={10}\n                      style={{ width: '100%' }}\n                      placeholder=\"2\"\n                    />\n                  </Col>\n                </Row>\n                <div style={{ width: '100%' }}>\n                  <Row align=\"middle\" style={{ marginBottom: 8 }}>\n                    <Col span={12}>\n                      <Text strong>Dropout 概率：</Text>\n                    </Col>\n                    <Col span={12} style={{ textAlign: 'right' }}>\n                      <Text code>{dropout}</Text>\n                    </Col>\n                  </Row>\n                  <Slider\n                    value={dropout}\n                    onChange={setDropout}\n                    min={0}\n                    max={0.9}\n                    step={0.05}\n                    marks={{\n                      0: '0',\n                      0.2: '0.2',\n                      0.5: '0.5',\n                      0.9: '0.9'\n                    }}\n                  />\n                </div>\n              </Space>\n            </Card>\n          </Col>\n        </Row>\n\n        {/* 模型保存路径（仅在单文件模式下显示） */}\n        {trainingMode === 'single' && (\n          <Row style={{ marginTop: 24 }}>\n            <Col span={24}>\n              <Card\n                size=\"small\"\n                title={\n                  <Space>\n                    <InboxOutlined />\n                    <Text strong>模型保存路径</Text>\n                  </Space>\n                }\n              >\n                <Input\n                  value={outputFolder}\n                  onChange={(e) => setOutputFolder(e.target.value)}\n                  placeholder=\"例如: /data/output\"\n                  size=\"large\"\n                  prefix={<InboxOutlined />}\n                />\n              </Card>\n            </Col>\n          </Row>\n        )}\n      </Card>\n\n      {/* 清洗模板生成选项 */}\n      <Card className=\"function-card\" title=\"清洗模板生成\">\n        <Space direction=\"vertical\" size=\"middle\" style={{ width: '100%' }}>\n          <div>\n            <Text strong>自动生成清洗模板：</Text>\n            <div style={{ marginTop: 8 }}>\n              <Checkbox\n                checked={autoGenerateTemplate}\n                onChange={(e) => setAutoGenerateTemplate(e.target.checked)}\n              >\n                训练完成后自动生成清洗模板\n              </Checkbox>\n            </div>\n            <div style={{ marginTop: 8 }}>\n              <Text type=\"secondary\" style={{ fontSize: '12px' }}>\n                选择此选项后，系统将在模型训练完成后自动调用清洗模板生成功能，\n                根据训练结果中的阈值信息生成相应的清洗模板文件。\n              </Text>\n            </div>\n          </div>\n        </Space>\n      </Card>\n\n      {/* 训练模式选择 */}\n      <Card className=\"function-card\" title=\"训练模式\">\n        <Space direction=\"vertical\" size=\"middle\" style={{ width: '100%' }}>\n          <div>\n            <Text strong>选择训练模式：</Text>\n            <Radio.Group\n              value={useAsyncTraining}\n              onChange={(e) => setUseAsyncTraining(e.target.value)}\n              style={{ marginTop: 8 }}\n            >\n              <Radio value={true}>\n                <Space>\n                  异步训练（推荐）\n                  <Text type=\"secondary\" style={{ fontSize: 12 }}>\n                    - 后台运行，不阻塞其他操作\n                  </Text>\n                </Space>\n              </Radio>\n              <Radio value={false}>\n                <Space>\n                  同步训练\n                  <Text type=\"secondary\" style={{ fontSize: 12 }}>\n                    - 等待训练完成，期间无法使用其他功能\n                  </Text>\n                </Space>\n              </Radio>\n            </Radio.Group>\n          </div>\n\n          {useAsyncTraining && (\n            <Alert\n              message=\"异步训练模式\"\n              description={\n                <div>\n                  训练任务将在后台运行，您可以继续使用系统的其他功能。\n                  <br />\n                  任务完成后会收到通知，可在 <strong>侧边栏菜单 → 任务管理</strong> 中查看进度和结果。\n                </div>\n              }\n              type=\"info\"\n              showIcon\n            />\n          )}\n        </Space>\n      </Card>\n\n      {/* 开始训练按钮 */}\n      <Card className=\"function-card\">\n        <Button\n          type=\"primary\"\n          size=\"large\"\n          icon={<PlayCircleOutlined />}\n          onClick={handleStartTraining}\n          loading={training}\n          disabled={!isFormValid()}\n          className=\"action-button\"\n        >\n          {training ? '正在训练...' : '开始训练预测'}\n        </Button>\n\n        {/* 训练进度 */}\n        {training && (\n          <div className=\"progress-section\">\n            <Text>训练进度：</Text>\n            <Progress percent={progress} status=\"active\" />\n          </div>\n        )}\n\n        {/* 训练结果展示 */}\n        {trainingResults && trainingResults.results && (\n          <div style={{ marginTop: 24 }}>\n            <Alert\n              message=\"训练完成\"\n              description={\n                <div>\n                  <p>所有模型训练完成！</p>\n                  {trainingResults.result_path && (\n                    <p><strong>结果已更新至:</strong> {trainingResults.result_path}</p>\n                  )}\n                  {trainingResults.template_info && (\n                    <div style={{ marginTop: 8, padding: 8, backgroundColor: '#f6ffed', border: '1px solid #b7eb8f', borderRadius: 4 }}>\n                      {trainingResults.template_info.template_generated ? (\n                        <div>\n                          <p style={{ color: '#52c41a', fontWeight: 'bold' }}>✅ 清洗模板已自动生成</p>\n                          <p><strong>模板路径:</strong> {trainingResults.template_info.template_path}</p>\n                          <p><strong>更新阈值数量:</strong> {trainingResults.template_info.updated_thresholds}</p>\n                        </div>\n                      ) : (\n                        <div>\n                          <p style={{ color: '#ff4d4f', fontWeight: 'bold' }}>❌ 清洗模板生成失败</p>\n                          <p><strong>错误信息:</strong> {trainingResults.template_info.error}</p>\n                        </div>\n                      )}\n                    </div>\n                  )}\n                  {Object.entries(trainingResults.results).map(([key, result]: [string, any]) => (\n                    <div key={key} style={{ marginTop: 8 }}>\n                      <p><strong>协议与数据类型:</strong> {key}</p>\n                      <p>模型已保存至: {result.model_save_path}</p>\n                      <p>标准化器已保存至: {result.scaler_y_save_path}</p>\n                    </div>\n                  ))}\n                </div>\n              }\n              type=\"success\"\n              showIcon\n              style={{ marginTop: 16 }}\n            />\n          </div>\n        )}\n\n      {/* 查看模型训练及特征预测结果（单文件模式） */}\n      {trainingResults && trainingResults.results && Object.keys(trainingResults.results).length > 0 && (\n        <Card title=\"查看模型训练及特征预测结果\" className=\"function-card\">\n          <Space direction=\"vertical\" size=\"large\" style={{ width: '100%' }}>\n            <div>\n              <Text strong>选择要查看的协议和数据类型：</Text>\n              <Select\n                value={selectedResultKey}\n                onChange={setSelectedResultKey}\n                style={{ width: '100%', marginTop: 8 }}\n                placeholder=\"请选择协议和数据类型\"\n              >\n                {Object.keys(trainingResults.results).map((key) => (\n                  <Option key={key} value={key}>\n                    {key}\n                  </Option>\n                ))}\n              </Select>\n            </div>\n\n            {selectedResultKey && trainingResults.results[selectedResultKey] && (\n              <TrainingResultDisplay\n                resultKey={selectedResultKey}\n                result={trainingResults.results[selectedResultKey]}\n              />\n            )}\n          </Space>\n        </Card>\n      )}\n\n      {/* 多文件训练结果展示 */}\n      {multiTrainingResults && multiTrainingResults.results && Object.keys(multiTrainingResults.results).length > 0 && (\n        <Card title=\"多文件训练结果\" className=\"function-card\">\n          <Alert\n            message=\"多文件训练完成\"\n            description={`成功处理 ${Object.keys(multiTrainingResults.results).length} 个数据源的训练任务`}\n            type=\"success\"\n            showIcon\n            style={{ marginBottom: 16 }}\n          />\n          <Tabs>\n            {Object.entries(multiTrainingResults.results).map(([sourceId, sourceResult]: [string, any]) => (\n              <TabPane tab={`数据源 ${sourceId}`} key={sourceId}>\n                {sourceResult.error ? (\n                  <Alert\n                    message=\"训练失败\"\n                    description={sourceResult.error}\n                    type=\"error\"\n                    showIcon\n                  />\n                ) : (\n                  <div>\n                    {/* 显示该数据源的所有协议/数据类型结果 */}\n                    {sourceResult.results && Object.keys(sourceResult.results).length > 0 ? (\n                      <Space direction=\"vertical\" size=\"large\" style={{ width: '100%' }}>\n                        <div>\n                          <Text strong>选择要查看的协议和数据类型：</Text>\n                          <Select\n                            value={selectedMultiSourceKey === sourceId ? selectedMultiResultKey : ''}\n                            onChange={(value) => {\n                              setSelectedMultiSourceKey(sourceId);\n                              setSelectedMultiResultKey(value);\n                            }}\n                            style={{ width: '100%', marginTop: 8 }}\n                            placeholder=\"请选择协议和数据类型\"\n                          >\n                            {Object.keys(sourceResult.results).map((key) => (\n                              <Option key={key} value={key}>\n                                {key}\n                              </Option>\n                            ))}\n                          </Select>\n                        </div>\n\n                        {selectedMultiSourceKey === sourceId && selectedMultiResultKey && sourceResult.results[selectedMultiResultKey] && (\n                          <TrainingResultDisplay\n                            resultKey={selectedMultiResultKey}\n                            result={sourceResult.results[selectedMultiResultKey]}\n                          />\n                        )}\n                      </Space>\n                    ) : (\n                      <Alert\n                        message=\"无训练结果\"\n                        description=\"该数据源没有生成有效的训练结果\"\n                        type=\"warning\"\n                        showIcon\n                      />\n                    )}\n                  </div>\n                )}\n              </TabPane>\n            ))}\n          </Tabs>\n        </Card>\n      )}\n\n      {/* 异步训练结果展示 */}\n      {completedTrainingTasks.length > 0 && (\n        <Card title=\"异步训练结果\" className=\"function-card\" style={{ marginTop: 24 }}>\n          <Alert\n            message=\"异步训练已完成\"\n            description=\"以下是后台训练任务的结果，您可以查看不同协议和数据类型的训练效果。\"\n            type=\"success\"\n            showIcon\n            style={{ marginBottom: 16 }}\n          />\n          <Space direction=\"vertical\" size=\"large\" style={{ width: '100%' }}>\n            {/* 任务选择器 */}\n            <div>\n              <Text strong>选择训练任务：</Text>\n              <Select\n                value={selectedAsyncTaskId}\n                onChange={handleAsyncTaskSelect}\n                style={{ width: '100%', marginTop: 8 }}\n                placeholder=\"请选择要查看的训练任务\"\n              >\n                {completedTrainingTasks.map((task) => (\n                  <Option key={task.task_id} value={task.task_id}>\n                    {task.task_id.includes('_') ?\n                      `${task.task_id.split('_')[0]} (${new Date(task.updated_at || task.created_at).toLocaleString()})` :\n                      `任务 ${task.task_id.substring(0, 8)}... (${new Date(task.updated_at || task.created_at).toLocaleString()})`\n                    }\n                  </Option>\n                ))}\n              </Select>\n            </div>\n\n            {/* 模板生成信息显示 */}\n            {asyncTrainingResults && asyncTrainingResults.template_info && (\n              <div style={{ marginTop: 8, padding: 8, backgroundColor: '#f6ffed', border: '1px solid #b7eb8f', borderRadius: 4 }}>\n                {asyncTrainingResults.template_info.template_generated ? (\n                  <div>\n                    <p style={{ color: '#52c41a', fontWeight: 'bold' }}>✅ 清洗模板已自动生成</p>\n                    <p><strong>模板路径:</strong> {asyncTrainingResults.template_info.template_path}</p>\n                    <p><strong>更新阈值数量:</strong> {asyncTrainingResults.template_info.updated_thresholds}</p>\n                  </div>\n                ) : (\n                  <div>\n                    <p style={{ color: '#ff4d4f', fontWeight: 'bold' }}>❌ 清洗模板生成失败</p>\n                    <p><strong>错误信息:</strong> {asyncTrainingResults.template_info.error}</p>\n                  </div>\n                )}\n              </div>\n            )}\n\n            {/* 协议和数据类型选择器 */}\n            {asyncTrainingResults && asyncTrainingResults.results && (\n              <div>\n                <Text strong>选择要查看的协议和数据类型：</Text>\n                <Select\n                  value={selectedAsyncResultKey}\n                  onChange={setSelectedAsyncResultKey}\n                  style={{ width: '100%', marginTop: 8 }}\n                  placeholder=\"请选择协议和数据类型\"\n                >\n                  {Object.keys(asyncTrainingResults.results).map((key) => (\n                    <Option key={key} value={key}>\n                      {key}\n                    </Option>\n                  ))}\n                </Select>\n              </div>\n            )}\n\n            {/* 结果展示 */}\n            {selectedAsyncResultKey && asyncTrainingResults && asyncTrainingResults.results[selectedAsyncResultKey] && (\n              <TrainingResultDisplay\n                resultKey={selectedAsyncResultKey}\n                result={asyncTrainingResults.results[selectedAsyncResultKey]}\n              />\n            )}\n          </Space>\n        </Card>\n      )}\n      </Card>\n    </div>\n  );\n};\n\nexport default ModelTrainingPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,KAAK,EACLC,MAAM,EACNC,MAAM,EACNC,UAAU,EACVC,KAAK,EACLC,OAAO,EACPC,OAAO,EACPC,IAAI,EACJC,WAAW,EACXC,MAAM,EACNC,QAAQ,EACRC,QAAQ,EACRC,KAAK,EACLC,GAAG,EACHC,GAAG,EACHC,SAAS,EACTC,IAAI,QACC,MAAM;AACb,SAASC,aAAa,EAAEC,kBAAkB,EAAEC,eAAe,EAAEC,kBAAkB,EAAEC,YAAY,EAAEC,cAAc,EAAEC,cAAc,QAAQ,mBAAmB;AACxJ,SAASC,SAAS,EAAEC,IAAI,EAAEC,KAAK,EAAEC,KAAK,EAAEC,aAAa,EAAEC,OAAO,EAAEC,MAAM,EAAEC,mBAAmB,QAAQ,UAAU;AAC7G,SAASC,gBAAgB,QAAQ,iBAAiB;AAClD,OAAOC,cAAc,MAAM,yBAAyB;AACpD,SAASC,uBAAuB,QAAQ,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9D,MAAM;EAAEC,KAAK;EAAEC;AAAK,CAAC,GAAGnC,UAAU;AAClC,MAAM;EAAEoC;AAAQ,CAAC,GAAGxC,MAAM;AAC1B,MAAM;EAAEyC;AAAO,CAAC,GAAGvC,MAAM;AACzB,MAAM;EAAEwC;AAAQ,CAAC,GAAGzB,IAAI;;AAExB;;AAaA;AACA,MAAM0B,qBAAmE,GAAGA,CAAC;EAAEC,SAAS;EAAEC;AAAO,CAAC,KAAK;EAAA,IAAAC,oBAAA,EAAAC,kBAAA;EACrG,MAAM,CAACC,YAAY,EAAEC,gBAAgB,CAAC,GAAGL,SAAS,CAACM,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC;EAEhE,oBACEb,OAAA;IAAAc,QAAA,eACEd,OAAA,CAAChC,KAAK;MAAC+C,SAAS,EAAC,UAAU;MAACC,IAAI,EAAC,QAAQ;MAACC,KAAK,EAAE;QAAEC,KAAK,EAAE;MAAO,CAAE;MAAAJ,QAAA,gBACjEd,OAAA;QAAAc,QAAA,gBACEd,OAAA,CAACE,IAAI;UAAAY,QAAA,gBAACd,OAAA;YAAAc,QAAA,EAAQ;UAAG;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAACX,YAAY;QAAA;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAChDtB,OAAA;UAAAmB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACNtB,OAAA,CAACE,IAAI;UAAAY,QAAA,gBAACd,OAAA;YAAAc,QAAA,EAAQ;UAAK;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAACV,gBAAgB;QAAA;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnD,CAAC,eAENtB,OAAA,CAACvB,GAAG;QAAC8C,MAAM,EAAE,EAAG;QAAAT,QAAA,gBACdd,OAAA,CAACtB,GAAG;UAAC8C,IAAI,EAAE,CAAE;UAAAV,QAAA,eACXd,OAAA,CAACrB,SAAS;YACR8C,KAAK,EAAC,4CAAS;YACfC,KAAK,EAAElB,MAAM,CAACmB,WAAW,GAAG,GAAGnB,MAAM,CAACmB,WAAW,CAAC,CAAC,CAAC,MAAMnB,MAAM,CAACmB,WAAW,CAAC,CAAC,CAAC,EAAE,GAAG;UAAM;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3F;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACNtB,OAAA,CAACtB,GAAG;UAAC8C,IAAI,EAAE,CAAE;UAAAV,QAAA,eACXd,OAAA,CAACrB,SAAS;YACR8C,KAAK,EAAC,4CAAS;YACfC,KAAK,EAAElB,MAAM,CAACoB,UAAU,GAAG,GAAGpB,MAAM,CAACoB,UAAU,CAAC,CAAC,CAAC,MAAMpB,MAAM,CAACoB,UAAU,CAAC,CAAC,CAAC,EAAE,GAAG;UAAM;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACNtB,OAAA,CAACtB,GAAG;UAAC8C,IAAI,EAAE,CAAE;UAAAV,QAAA,eACXd,OAAA,CAACrB,SAAS;YACR8C,KAAK,EAAC,oBAAO;YACbC,KAAK,EAAElB,MAAM,CAACqB,QAAQ,GAAGrB,MAAM,CAACqB,QAAQ,CAACC,OAAO,CAAC,CAAC,CAAC,GAAItB,MAAM,CAACuB,EAAE,GAAGvB,MAAM,CAACuB,EAAE,CAACD,OAAO,CAAC,CAAC,CAAC,GAAG,KAAO;YACjGE,SAAS,EAAE,CAAE;YACbC,UAAU,EAAE;cAAEC,KAAK,EAAE1B,MAAM,CAACqB,QAAQ,GAAG,GAAG,IAAIrB,MAAM,CAACuB,EAAE,GAAG,GAAG,GAAG,SAAS,GAAG;YAAU;UAAE;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACNtB,OAAA,CAACtB,GAAG;UAAC8C,IAAI,EAAE,CAAE;UAAAV,QAAA,eACXd,OAAA,CAACrB,SAAS;YACR8C,KAAK,EAAC,sCAAQ;YACdC,KAAK,EAAElB,MAAM,CAAC2B,wBAAwB,GAAG3B,MAAM,CAAC2B,wBAAwB,CAACL,OAAO,CAAC,CAAC,CAAC,GAAG,KAAM;YAC5FE,SAAS,EAAE;UAAE;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACd;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENtB,OAAA,CAACvB,GAAG;QAAC8C,MAAM,EAAE,EAAG;QAACN,KAAK,EAAE;UAAEmB,SAAS,EAAE;QAAG,CAAE;QAAAtB,QAAA,gBACxCd,OAAA,CAACtB,GAAG;UAAC8C,IAAI,EAAE,CAAE;UAAAV,QAAA,eACXd,OAAA,CAACrB,SAAS;YACR8C,KAAK,EAAC,uBAAQ;YACdC,KAAK,EAAElB,MAAM,CAAC6B,WAAW,GAAG,GAAG7B,MAAM,CAAC6B,WAAW,CAACP,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG,KAAM;YACxEG,UAAU,EAAE;cAAEC,KAAK,EAAE;YAAU;UAAE;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACNtB,OAAA,CAACtB,GAAG;UAAC8C,IAAI,EAAE,CAAE;UAAAV,QAAA,eACXd,OAAA,CAACrB,SAAS;YACR8C,KAAK,EAAC,0BAAM;YACZC,KAAK,EAAElB,MAAM,CAAC8B,SAAS,GAAG,GAAG9B,MAAM,CAAC8B,SAAS,CAACR,OAAO,CAAC,CAAC,CAAC,KAAK,GAAG,KAAM;YACtEG,UAAU,EAAE;cAAEC,KAAK,EAAE;YAAU;UAAE;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACNtB,OAAA,CAACtB,GAAG;UAAC8C,IAAI,EAAE,CAAE;UAAAV,QAAA,eACXd,OAAA,CAACrB,SAAS;YACR8C,KAAK,EAAC,iBAAO;YACbC,KAAK,EAAElB,MAAM,CAAC+B,aAAa,GAAG,GAAG/B,MAAM,CAAC+B,aAAa,CAACT,OAAO,CAAC,CAAC,CAAC,KAAK,GAAItB,MAAM,CAACgC,UAAU,GAAG,GAAGhC,MAAM,CAACgC,UAAU,CAACV,OAAO,CAAC,CAAC,CAAC,KAAK,GAAG,KAAO;YAC3IG,UAAU,EAAE;cAAEC,KAAK,EAAE;YAAU;UAAE;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACNtB,OAAA,CAACtB,GAAG;UAAC8C,IAAI,EAAE,CAAE;UAAAV,QAAA,eACXd,OAAA,CAACrB,SAAS;YACR8C,KAAK,EAAC,uBAAQ;YACdC,KAAK,EAAElB,MAAM,CAACiC,uBAAuB,GAAG,GAAGjC,MAAM,CAACiC,uBAAuB,CAACX,OAAO,CAAC,CAAC,CAAC,GAAG,GAAItB,MAAM,CAACkC,eAAe,GAAG,GAAGlC,MAAM,CAACkC,eAAe,CAACZ,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG,KAAO;YACrKG,UAAU,EAAE;cAAEC,KAAK,EAAE;YAAU;UAAE;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAELd,MAAM,CAACmC,YAAY,IAAInC,MAAM,CAACoC,UAAU,iBACvC5C,OAAA;QAAAc,QAAA,gBACEd,OAAA,CAACE,IAAI;UAAC2C,MAAM;UAAA/B,QAAA,EAAC;QAAM;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC1BtB,OAAA;UAAKiB,KAAK,EAAE;YAAE6B,MAAM,EAAE,GAAG;YAAEV,SAAS,EAAE;UAAE,CAAE;UAAAtB,QAAA,eACxCd,OAAA,CAACL,mBAAmB;YAACuB,KAAK,EAAC,MAAM;YAAC4B,MAAM,EAAC,MAAM;YAAAhC,QAAA,eAC7Cd,OAAA,CAACZ,SAAS;cACR2D,IAAI,EAAEvC,MAAM,CAACmC,YAAY,CAACK,GAAG,CAAC,CAACC,SAAiB,EAAEC,KAAa,MAAM;gBACnEC,KAAK,EAAED,KAAK,GAAG,CAAC;gBAChBE,IAAI,EAAEH,SAAS;gBACfI,IAAI,EAAE7C,MAAM,CAACoC,UAAU,CAACM,KAAK,CAAC,IAAI;cACpC,CAAC,CAAC,CAAE;cACJI,MAAM,EAAE;gBAAEC,GAAG,EAAE,CAAC;gBAAEC,KAAK,EAAE,EAAE;gBAAEC,IAAI,EAAE,EAAE;gBAAEC,MAAM,EAAE;cAAE,CAAE;cAAA5C,QAAA,gBAEnDd,OAAA,CAACR,aAAa;gBAACmE,eAAe,EAAC;cAAK;gBAAAxC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACvCtB,OAAA,CAACV,KAAK;gBAACsE,OAAO,EAAC;cAAO;gBAAAzC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACzBtB,OAAA,CAACT,KAAK;gBAAA4B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACTtB,OAAA,CAACP,OAAO;gBAAA0B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACXtB,OAAA,CAACN,MAAM;gBAAAyB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACVtB,OAAA,CAACX,IAAI;gBACHwE,IAAI,EAAC,UAAU;gBACfD,OAAO,EAAC,0BAAM;gBACdE,MAAM,EAAC,SAAS;gBAChBC,WAAW,EAAE,CAAE;gBACfC,GAAG,EAAE;cAAM;gBAAA7C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACZ,CAAC,eACFtB,OAAA,CAACX,IAAI;gBACHwE,IAAI,EAAC,UAAU;gBACfD,OAAO,EAAC,0BAAM;gBACdE,MAAM,EAAC,SAAS;gBAChBC,WAAW,EAAE,CAAE;gBACfC,GAAG,EAAE;cAAM;gBAAA7C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACZ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CAAC,eACNtB,OAAA;UAAKiB,KAAK,EAAE;YAAEmB,SAAS,EAAE;UAAE,CAAE;UAAAtB,QAAA,eAC3Bd,OAAA,CAACE,IAAI;YAAC2D,IAAI,EAAC,WAAW;YAAA/C,QAAA,GAAC,4BACf,EAACN,MAAM,CAACmC,YAAY,CAACsB,MAAM,EAAC,kDAC1B,GAAAxD,oBAAA,GAACD,MAAM,CAACmC,YAAY,CAACnC,MAAM,CAACmC,YAAY,CAACsB,MAAM,GAAG,CAAC,CAAC,cAAAxD,oBAAA,uBAAnDA,oBAAA,CAAqDqB,OAAO,CAAC,CAAC,CAAC,EAAC,2CACjE,GAAApB,kBAAA,GAACF,MAAM,CAACoC,UAAU,CAACpC,MAAM,CAACoC,UAAU,CAACqB,MAAM,GAAG,CAAC,CAAC,cAAAvD,kBAAA,uBAA/CA,kBAAA,CAAiDoB,OAAO,CAAC,CAAC,CAAC;UAAA;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAEAd,MAAM,CAAC0D,aAAa,IAAI1D,MAAM,CAAC2D,MAAM,IAAI3D,MAAM,CAAC0D,aAAa,CAACD,MAAM,GAAG,CAAC,iBACvEjE,OAAA;QAAAc,QAAA,gBACEd,OAAA,CAACE,IAAI;UAAC2C,MAAM;UAAA/B,QAAA,EAAC;QAAa;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACjCtB,OAAA;UAAKiB,KAAK,EAAE;YAAE6B,MAAM,EAAE,GAAG;YAAEV,SAAS,EAAE;UAAE,CAAE;UAAAtB,QAAA,eACxCd,OAAA,CAACL,mBAAmB;YAACuB,KAAK,EAAC,MAAM;YAAC4B,MAAM,EAAC,MAAM;YAAAhC,QAAA,eAC7Cd,OAAA,CAACZ,SAAS;cACR2D,IAAI,EAAEvC,MAAM,CAAC0D,aAAa,CAAClB,GAAG,CAAC,CAACoB,MAAc,EAAElB,KAAa,MAAM;gBACjEA,KAAK,EAAEA,KAAK,GAAG,CAAC;gBAChBmB,GAAG,EAAED,MAAM;gBACXE,GAAG,EAAE9D,MAAM,CAAC2D,MAAM,CAACjB,KAAK;cAC1B,CAAC,CAAC,CAAE;cACJI,MAAM,EAAE;gBAAEC,GAAG,EAAE,CAAC;gBAAEC,KAAK,EAAE,EAAE;gBAAEC,IAAI,EAAE,EAAE;gBAAEC,MAAM,EAAE;cAAE,CAAE;cAAA5C,QAAA,gBAEnDd,OAAA,CAACR,aAAa;gBAACmE,eAAe,EAAC;cAAK;gBAAAxC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACvCtB,OAAA,CAACV,KAAK;gBAACsE,OAAO,EAAC;cAAO;gBAAAzC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACzBtB,OAAA,CAACT,KAAK;gBAAA4B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACTtB,OAAA,CAACP,OAAO;gBAAA0B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACXtB,OAAA,CAACN,MAAM;gBAAAyB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACVtB,OAAA,CAACX,IAAI;gBACHwE,IAAI,EAAC,UAAU;gBACfD,OAAO,EAAC,oBAAK;gBACbE,MAAM,EAAC,SAAS;gBAChBC,WAAW,EAAE,CAAE;gBACfC,GAAG,EAAE;cAAM;gBAAA7C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACZ,CAAC,eACFtB,OAAA,CAACX,IAAI;gBACHwE,IAAI,EAAC,UAAU;gBACfD,OAAO,EAAC,oBAAK;gBACbE,MAAM,EAAC,SAAS;gBAChBC,WAAW,EAAE,CAAE;gBACfC,GAAG,EAAE;cAAM;gBAAA7C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACZ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CAAC,eACNtB,OAAA;UAAKiB,KAAK,EAAE;YAAEmB,SAAS,EAAE;UAAE,CAAE;UAAAtB,QAAA,eAC3Bd,OAAA,CAACE,IAAI;YAAC2D,IAAI,EAAC,WAAW;YAAA/C,QAAA,GAAC,2BAChB,EAACN,MAAM,CAAC0D,aAAa,CAACD,MAAM,EAAC,sFAC3B,EAACzD,MAAM,CAACqB,QAAQ,GAAGrB,MAAM,CAACqB,QAAQ,CAACC,OAAO,CAAC,CAAC,CAAC,GAAItB,MAAM,CAACuB,EAAE,GAAGvB,MAAM,CAACuB,EAAE,CAACD,OAAO,CAAC,CAAC,CAAC,GAAG,KAAM,EAAC,+BAC5F,EAACtB,MAAM,CAAC2B,wBAAwB,GAAG3B,MAAM,CAAC2B,wBAAwB,CAACL,OAAO,CAAC,CAAC,CAAC,GAAG,KAAK;UAAA;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAIAd,MAAM,CAAC+D,eAAe,iBACrBvE,OAAA,CAACxB,KAAK;QACJN,OAAO,EAAC,sCAAQ;QAChBsG,WAAW,eACTxE,OAAA;UAAAc,QAAA,gBACEd,OAAA;YAAAc,QAAA,gBAAGd,OAAA;cAAAc,QAAA,EAAQ;YAAO;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACd,MAAM,CAAC+D,eAAe;UAAA;YAAApD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,EACvDd,MAAM,CAACiE,kBAAkB,iBACxBzE,OAAA;YAAAc,QAAA,gBAAGd,OAAA;cAAAc,QAAA,EAAQ;YAAS;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACd,MAAM,CAACiE,kBAAkB;UAAA;YAAAtD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAC7D,EACAd,MAAM,CAACkE,gBAAgB,iBACtB1E,OAAA;YAAAc,QAAA,gBAAGd,OAAA;cAAAc,QAAA,EAAQ;YAAO;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACd,MAAM,CAACkE,gBAAgB;UAAA;YAAAvD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CACzD,EACAd,MAAM,CAACmE,cAAc,iBACpB3E,OAAA;YAAAc,QAAA,gBAAGd,OAAA;cAAAc,QAAA,EAAQ;YAAS;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACd,MAAM,CAACmE,cAAc;UAAA;YAAAxD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CACzD,EACAd,MAAM,CAAC2B,wBAAwB,iBAC9BnC,OAAA;YAAAc,QAAA,gBAAGd,OAAA;cAAAc,QAAA,EAAQ;YAAO;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACd,MAAM,CAAC2B,wBAAwB,CAACL,OAAO,CAAC,CAAC,CAAC;UAAA;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAC5E,EACAd,MAAM,CAACoE,aAAa,iBACnB5E,OAAA;YAAAc,QAAA,gBAAGd,OAAA;cAAAc,QAAA,EAAQ;YAAO;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACd,MAAM,CAACoE,aAAa;UAAA;YAAAzD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CACtD,EACAd,MAAM,CAACqE,gBAAgB,iBACtB7E,OAAA;YAAAc,QAAA,gBAAGd,OAAA;cAAAc,QAAA,EAAQ;YAAK;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACd,MAAM,CAACqE,gBAAgB,CAAC/C,OAAO,CAAC,CAAC,CAAC,EAAC,SAAE;UAAA;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CACpE;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACN;QACDuC,IAAI,EAAC,MAAM;QACXiB,QAAQ;MAAA;QAAA3D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CACF;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAACyD,EAAA,GAtMIzE,qBAAmE;AAwMzE,MAAM0E,iBAA2B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACxC;EACA,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAG5H,QAAQ,CAAqB,QAAQ,CAAC;;EAE9E;EACA,MAAM,CAAC6H,UAAU,EAAEC,aAAa,CAAC,GAAG9H,QAAQ,CAAqB,OAAO,CAAC;EACzE,MAAM,CAAC+H,YAAY,EAAEC,eAAe,CAAC,GAAGhI,QAAQ,CAAM,IAAI,CAAC;EAC3D,MAAM,CAACiI,MAAM,EAAEC,SAAS,CAAC,GAAGlI,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACmI,cAAc,EAAEC,iBAAiB,CAAC,GAAGpI,QAAQ,CAAW,EAAE,CAAC;EAClE,MAAM,CAACqI,YAAY,EAAEC,eAAe,CAAC,GAAGtI,QAAQ,CAAS,EAAE,CAAC;EAC5D,MAAM,CAACuI,YAAY,EAAEC,eAAe,CAAC,GAAGxI,QAAQ,CAAC,KAAK,CAAC;;EAEvD;EACA,MAAM,CAACyI,WAAW,EAAEC,cAAc,CAAC,GAAG1I,QAAQ,CAAuB,CACnE;IACE2I,EAAE,EAAE,GAAG;IACPrC,IAAI,EAAE,OAAO;IACbsC,YAAY,EAAE,cAAc;IAC5BC,OAAO,EAAE,IAAI;IACbV,cAAc,EAAE,EAAE;IAClBI,YAAY,EAAE;EAChB,CAAC,CACF,CAAC;;EAEF;EACA,MAAM,CAACO,aAAa,EAAEC,gBAAgB,CAAC,GAAG/I,QAAQ,CAAW,CAAC,KAAK,CAAC,CAAC;EACrE,MAAM,CAACgJ,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGjJ,QAAQ,CAA4B;IACpFkJ,GAAG,EAAE,CAAC,aAAa;EACrB,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGpJ,QAAQ,CAAC,MAAM,CAAC;EACxD,MAAM,CAACqJ,SAAS,EAAEC,YAAY,CAAC,GAAGtJ,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACuJ,MAAM,EAAEC,SAAS,CAAC,GAAGxJ,QAAQ,CAAC,GAAG,CAAC;EACzC,MAAM,CAACyJ,cAAc,EAAEC,iBAAiB,CAAC,GAAG1J,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAAC2J,UAAU,EAAEC,aAAa,CAAC,GAAG5J,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC6J,SAAS,EAAEC,YAAY,CAAC,GAAG9J,QAAQ,CAAC,CAAC,CAAC;EAC7C,MAAM,CAAC+J,OAAO,EAAEC,UAAU,CAAC,GAAGhK,QAAQ,CAAC,GAAG,CAAC;EAC3C,MAAM,CAAC4I,YAAY,EAAEqB,eAAe,CAAC,GAAGjK,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACkK,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGnK,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;;EAEzE;EACA,MAAM,CAACoK,QAAQ,EAAEC,WAAW,CAAC,GAAGrK,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAACsK,QAAQ,EAAEC,WAAW,CAAC,GAAGvK,QAAQ,CAAC,CAAC,CAAC;EAC3C,MAAM,CAACwK,eAAe,EAAEC,kBAAkB,CAAC,GAAGzK,QAAQ,CAAM,IAAI,CAAC;EACjE,MAAM,CAAC0K,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG3K,QAAQ,CAAS,EAAE,CAAC;;EAEtE;EACA,MAAM,CAAC4K,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG7K,QAAQ,CAAM,IAAI,CAAC;EAC3E,MAAM,CAAC8K,sBAAsB,EAAEC,yBAAyB,CAAC,GAAG/K,QAAQ,CAAS,EAAE,CAAC;EAChF,MAAM,CAACgL,sBAAsB,EAAEC,yBAAyB,CAAC,GAAGjL,QAAQ,CAAS,EAAE,CAAC;;EAEhF;EACA,MAAM;IAAEkL,kBAAkB;IAAEC,uBAAuB;IAAEC;EAAoB,CAAC,GAAG9I,cAAc,CAAC,CAAC;EAC7F,MAAM,CAAC+I,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGtL,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;;EAEhE;EACA,MAAMuL,aAAa,GAAGA,CAAA,KAAM;IAC1B,MAAMC,SAA6B,GAAG;MACpC7C,EAAE,EAAE8C,IAAI,CAACC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC;MACzBrF,IAAI,EAAE,OAAO;MACbsC,YAAY,EAAE,sBAAsBH,WAAW,CAAC/B,MAAM,GAAG,CAAC,EAAE;MAC5DmC,OAAO,EAAE,IAAI;MACbV,cAAc,EAAE,EAAE;MAClBI,YAAY,EAAE;IAChB,CAAC;IACDG,cAAc,CAAC,CAAC,GAAGD,WAAW,EAAE+C,SAAS,CAAC,CAAC;EAC7C,CAAC;EAED,MAAMI,gBAAgB,GAAIjD,EAAU,IAAK;IACvCD,cAAc,CAACD,WAAW,CAACoD,MAAM,CAACC,MAAM,IAAIA,MAAM,CAACnD,EAAE,KAAKA,EAAE,CAAC,CAAC;EAChE,CAAC;EAED,MAAMoD,gBAAgB,GAAGA,CAACpD,EAAU,EAAEqD,OAAoC,KAAK;IAC7EtD,cAAc,CAACD,WAAW,CAAChD,GAAG,CAACqG,MAAM,IACnCA,MAAM,CAACnD,EAAE,KAAKA,EAAE,GAAG;MAAE,GAAGmD,MAAM;MAAE,GAAGE;IAAQ,CAAC,GAAGF,MACjD,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAM,CAACG,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGlM,QAAQ,CAAM,IAAI,CAAC;EAC3E,MAAM,CAACmM,sBAAsB,EAAEC,yBAAyB,CAAC,GAAGpM,QAAQ,CAAS,EAAE,CAAC;EAChF,MAAM,CAACqM,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGtM,QAAQ,CAAS,EAAE,CAAC;;EAE1E;EACA,MAAMuM,sBAAsB,GAAGpB,uBAAuB,CAAC,UAAU,CAAC;;EAElE;EACA,MAAMqB,qBAAqB,GAAIC,MAAc,IAAK;IAChDH,sBAAsB,CAACG,MAAM,CAAC;IAC9B,MAAMC,YAAY,GAAGH,sBAAsB,CAACI,IAAI,CAACC,IAAI,IAAIA,IAAI,CAACC,OAAO,KAAKJ,MAAM,CAAC;IACjF,IAAIC,YAAY,IAAIA,YAAY,CAACzJ,MAAM,IAAIyJ,YAAY,CAACzJ,MAAM,CAAC6J,OAAO,EAAE;MACtEZ,uBAAuB,CAACQ,YAAY,CAACzJ,MAAM,CAAC;MAC5CmJ,yBAAyB,CAACW,MAAM,CAACC,IAAI,CAACN,YAAY,CAACzJ,MAAM,CAAC6J,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;IACxE;EACF,CAAC;;EAED;EACA7M,SAAS,CAAC,MAAM;IACdmL,mBAAmB,CAAC,CAAC;EACvB,CAAC,EAAE,CAACA,mBAAmB,CAAC,CAAC;;EAEzB;EACAnL,SAAS,CAAC,MAAM;IACd,IAAIsM,sBAAsB,CAAC7F,MAAM,GAAG,CAAC,IAAI,CAAC2F,mBAAmB,EAAE;MAC7D,MAAMY,UAAU,GAAGV,sBAAsB,CAACA,sBAAsB,CAAC7F,MAAM,GAAG,CAAC,CAAC;MAC5E8F,qBAAqB,CAACS,UAAU,CAACJ,OAAO,CAAC;IAC3C;EACF,CAAC,EAAE,CAACN,sBAAsB,EAAEF,mBAAmB,EAAEG,qBAAqB,CAAC,CAAC;;EAExE;EACA,MAAMU,eAAe,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,CAAC;EAC9C,MAAMC,eAAe,GAAG;IACtBjE,GAAG,EAAE,CAAC,aAAa,EAAE,aAAa,EAAE,aAAa,EAAE,aAAa,CAAC;IACjEkE,GAAG,EAAE,CAAC,aAAa,EAAE,aAAa,CAAC;IACnCC,IAAI,EAAE,CAAC,KAAK;EACd,CAAC;;EAED;EACA,MAAMC,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI,CAACrF,MAAM,EAAE;IAEbO,eAAe,CAAC,IAAI,CAAC;IACrB,IAAI;MACF,MAAM+E,QAAQ,GAAG,MAAMlL,gBAAgB,CAACmL,YAAY,CAACvF,MAAM,CAAC;MAC5DG,iBAAiB,CAACmF,QAAQ,CAAC/H,IAAI,CAACiI,KAAK,IAAI,EAAE,CAAC;IAC9C,CAAC,CAAC,OAAOC,KAAU,EAAE;MAAA,IAAAC,eAAA,EAAAC,oBAAA;MACnBjN,OAAO,CAAC+M,KAAK,CAAC,EAAAC,eAAA,GAAAD,KAAK,CAACH,QAAQ,cAAAI,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBnI,IAAI,cAAAoI,oBAAA,uBAApBA,oBAAA,CAAsBC,MAAM,KAAI,UAAU,CAAC;MACzDzF,iBAAiB,CAAC,EAAE,CAAC;IACvB,CAAC,SAAS;MACRI,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;;EAED;EACA,MAAMsF,sBAAsB,GAAG,MAAAA,CAAOC,QAAgB,EAAE9F,MAAc,KAAK;IACzE,IAAI,CAACA,MAAM,EAAE;IAEb8D,gBAAgB,CAACgC,QAAQ,EAAE;MAAExF,YAAY,EAAE;IAAK,CAAC,CAAC;IAClD,IAAI;MACF,MAAMgF,QAAQ,GAAG,MAAMlL,gBAAgB,CAACmL,YAAY,CAACvF,MAAM,CAAC;MAC5D8D,gBAAgB,CAACgC,QAAQ,EAAE;QACzB5F,cAAc,EAAEoF,QAAQ,CAAC/H,IAAI,CAACiI,KAAK,IAAI,EAAE;QACzClF,YAAY,EAAE;MAChB,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOmF,KAAU,EAAE;MAAA,IAAAM,gBAAA,EAAAC,qBAAA;MACnBtN,OAAO,CAAC+M,KAAK,CAAC,EAAAM,gBAAA,GAAAN,KAAK,CAACH,QAAQ,cAAAS,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBxI,IAAI,cAAAyI,qBAAA,uBAApBA,qBAAA,CAAsBJ,MAAM,KAAI,UAAU,CAAC;MACzD9B,gBAAgB,CAACgC,QAAQ,EAAE;QACzB5F,cAAc,EAAE,EAAE;QAClBI,YAAY,EAAE;MAChB,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;EACAtI,SAAS,CAAC,MAAM;IACd,IAAI4H,UAAU,KAAK,OAAO,IAAII,MAAM,IAAIA,MAAM,CAACvB,MAAM,GAAG,CAAC,EAAE;MAAE;MAC3D,MAAMwH,KAAK,GAAGC,UAAU,CAAC,MAAM;QAC7Bb,aAAa,CAAC,CAAC;MACjB,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;;MAEV,OAAO,MAAMc,YAAY,CAACF,KAAK,CAAC;IAClC;IACA;EACF,CAAC,EAAE,CAACrG,UAAU,EAAEI,MAAM,CAAC,CAAC;;EAExB;EACA,MAAMoG,WAAW,GAAG;IAClBC,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE,KAAK;IACfC,MAAM,EAAE,MAAM;IACdC,YAAY,EAAEA,CAAA,KAAM,KAAK;IACzBC,QAAQ,EAAGC,IAAS,IAAK;MACvB,IAAIA,IAAI,CAACC,QAAQ,CAAClI,MAAM,GAAG,CAAC,EAAE;QAC5BsB,eAAe,CAAC2G,IAAI,CAACC,QAAQ,CAAC,CAAC,CAAC,CAAC;MACnC,CAAC,MAAM;QACL5G,eAAe,CAAC,IAAI,CAAC;MACvB;IACF;EACF,CAAC;;EAED;EACA,MAAM6G,uBAAuB,GAAId,QAAgB,KAAM;IACrDO,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE,KAAK;IACfC,MAAM,EAAE,MAAM;IACdC,YAAY,EAAEA,CAAA,KAAM,KAAK;IACzBC,QAAQ,EAAGC,IAAS,IAAK;MACvB,IAAIA,IAAI,CAACC,QAAQ,CAAClI,MAAM,GAAG,CAAC,EAAE;QAC5BqF,gBAAgB,CAACgC,QAAQ,EAAE;UAAEe,IAAI,EAAEH,IAAI,CAACC,QAAQ,CAAC,CAAC;QAAE,CAAC,CAAC;MACxD,CAAC,MAAM;QACL7C,gBAAgB,CAACgC,QAAQ,EAAE;UAAEe,IAAI,EAAEC;QAAU,CAAC,CAAC;MACjD;IACF;EACF,CAAC,CAAC;;EAEF;EACA,MAAMC,oBAAoB,GAAIC,KAAe,IAAK;IAChDlG,gBAAgB,CAACkG,KAAK,CAAC;IACvB;IACA,MAAMC,YAAY,GAAG;MAAE,GAAGlG;IAAkB,CAAC;IAC7CiG,KAAK,CAACE,OAAO,CAACC,IAAI,IAAI;MACpB,IAAI,CAACF,YAAY,CAACE,IAAI,CAAC,IAAIjC,eAAe,CAACiC,IAAI,CAAiC,EAAE;QAChFF,YAAY,CAACE,IAAI,CAAC,GAAG,CAACjC,eAAe,CAACiC,IAAI,CAAiC,CAAC,CAAC,CAAC,CAAC;MACjF;IACF,CAAC,CAAC;IACF;IACArC,MAAM,CAACC,IAAI,CAACkC,YAAY,CAAC,CAACC,OAAO,CAACC,IAAI,IAAI;MACxC,IAAI,CAACH,KAAK,CAACI,QAAQ,CAACD,IAAI,CAAC,EAAE;QACzB,OAAOF,YAAY,CAACE,IAAI,CAAC;MAC3B;IACF,CAAC,CAAC;IACFnG,oBAAoB,CAACiG,YAAY,CAAC;EACpC,CAAC;;EAED;EACA,MAAMI,oBAAoB,GAAGA,CAACC,QAAgB,EAAEC,SAAmB,KAAK;IACtEvG,oBAAoB,CAACwG,IAAI,KAAK;MAC5B,GAAGA,IAAI;MACP,CAACF,QAAQ,GAAGC;IACd,CAAC,CAAC,CAAC;EACL,CAAC;;EAED;EACA,MAAME,qBAAqB,GAAGA,CAAA,KAAe;IAC3C;IACA,MAAMC,YAAY,GAAGlH,WAAW,CAACoD,MAAM,CAACC,MAAM,IAAI;MAChD,IAAIA,MAAM,CAACxF,IAAI,KAAK,QAAQ,EAAE;QAC5B,OAAOwF,MAAM,CAACgD,IAAI,IAAIhD,MAAM,CAAClD,YAAY;MAC3C,CAAC,MAAM;QACL,OAAOkD,MAAM,CAAC7D,MAAM,IAAI6D,MAAM,CAACzD,YAAY,IAAIyD,MAAM,CAAClD,YAAY;MACpE;IACF,CAAC,CAAC;IAEF,IAAI+G,YAAY,CAACjJ,MAAM,KAAK,CAAC,EAAE;MAC7B/F,OAAO,CAAC+M,KAAK,CAAC,eAAe,CAAC;MAC9B,OAAO,KAAK;IACd;;IAEA;IACA,MAAMkC,WAAW,GAAGD,YAAY,CAAClK,GAAG,CAACoK,CAAC,IAAIA,CAAC,CAACjH,YAAY,CAAC;IACzD,MAAMkH,WAAW,GAAG,IAAIC,GAAG,CAACH,WAAW,CAAC;IACxC,IAAIA,WAAW,CAAClJ,MAAM,KAAKoJ,WAAW,CAACrM,IAAI,EAAE;MAC3C9C,OAAO,CAAC+M,KAAK,CAAC,YAAY,CAAC;MAC3B,OAAO,KAAK;IACd;IAEA,OAAO,IAAI;EACb,CAAC;;EAED;EACA,MAAMsC,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC;IACA,IAAIrI,YAAY,KAAK,OAAO,EAAE;MAC5B,IAAI,CAAC+H,qBAAqB,CAAC,CAAC,EAAE;QAC5B;MACF;IACF,CAAC,MAAM;MACL;MACA,IAAI7H,UAAU,KAAK,QAAQ,IAAI,CAACE,YAAY,EAAE;QAC5CpH,OAAO,CAAC+M,KAAK,CAAC,UAAU,CAAC;QACzB;MACF;MAEA,IAAI7F,UAAU,KAAK,OAAO,KAAK,CAACI,MAAM,IAAI,CAACI,YAAY,CAAC,EAAE;QACxD1H,OAAO,CAAC+M,KAAK,CAAC,UAAU,CAAC;QACzB;MACF;IACF;IAEA,IAAI5E,aAAa,CAACpC,MAAM,KAAK,CAAC,EAAE;MAC9B/F,OAAO,CAAC+M,KAAK,CAAC,WAAW,CAAC;MAC1B;IACF;IAEA,MAAMuC,iBAAiB,GAAGnH,aAAa,CAACoH,IAAI,CAACd,IAAI,IAC/CpG,iBAAiB,CAACoG,IAAI,CAAC,IAAIpG,iBAAiB,CAACoG,IAAI,CAAC,CAAC1I,MAAM,GAAG,CAC9D,CAAC;IAED,IAAI,CAACuJ,iBAAiB,EAAE;MACtBtP,OAAO,CAAC+M,KAAK,CAAC,kBAAkB,CAAC;MACjC;IACF;IAEArD,WAAW,CAAC,IAAI,CAAC;IACjBE,WAAW,CAAC,CAAC,CAAC;IACdE,kBAAkB,CAAC,IAAI,CAAC;IACxBE,oBAAoB,CAAC,EAAE,CAAC;IAExB,IAAI;MACF,IAAIU,gBAAgB,EAAE;QACpB;QACA,IAAI1D,YAAY,KAAK,OAAO,EAAE;UAC5B;UACA,MAAMwI,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;;UAE/B;UACA3H,WAAW,CAAC0G,OAAO,CAAErD,MAAM,IAAK;YAC9B,IAAIA,MAAM,CAACxF,IAAI,KAAK,QAAQ,IAAIwF,MAAM,CAACgD,IAAI,EAAE;cAC3CqB,QAAQ,CAACE,MAAM,CAAC,OAAO,EAAEvE,MAAM,CAACgD,IAAI,CAACwB,aAAa,CAAC;YACrD;UACF,CAAC,CAAC;;UAEF;UACAH,QAAQ,CAACE,MAAM,CAAC,cAAc,EAAEE,IAAI,CAACC,SAAS,CAAC/H,WAAW,CAAC,CAAC;UAC5D0H,QAAQ,CAACE,MAAM,CAAC,gBAAgB,EAAEE,IAAI,CAACC,SAAS,CAAC1H,aAAa,CAAC,CAAC;UAChEqH,QAAQ,CAACE,MAAM,CAAC,oBAAoB,EAAEE,IAAI,CAACC,SAAS,CAACxH,iBAAiB,CAAC,CAAC;UACxEmH,QAAQ,CAACE,MAAM,CAAC,eAAe,EAAElH,YAAY,CAACwC,QAAQ,CAAC,CAAC,CAAC;UACzDwE,QAAQ,CAACE,MAAM,CAAC,YAAY,EAAEhH,SAAS,CAACsC,QAAQ,CAAC,CAAC,CAAC;UACnDwE,QAAQ,CAACE,MAAM,CAAC,QAAQ,EAAE9G,MAAM,CAACoC,QAAQ,CAAC,CAAC,CAAC;UAC5CwE,QAAQ,CAACE,MAAM,CAAC,iBAAiB,EAAE5G,cAAc,CAACkC,QAAQ,CAAC,CAAC,CAAC;UAC7DwE,QAAQ,CAACE,MAAM,CAAC,aAAa,EAAE1G,UAAU,CAACgC,QAAQ,CAAC,CAAC,CAAC;UACrDwE,QAAQ,CAACE,MAAM,CAAC,YAAY,EAAExG,SAAS,CAAC8B,QAAQ,CAAC,CAAC,CAAC;UACnDwE,QAAQ,CAACE,MAAM,CAAC,SAAS,EAAEtG,OAAO,CAAC4B,QAAQ,CAAC,CAAC,CAAC;UAC9CwE,QAAQ,CAACE,MAAM,CAAC,wBAAwB,EAAEnG,oBAAoB,CAACyB,QAAQ,CAAC,CAAC,CAAC;;UAE1E;UACA,MAAMc,MAAM,GAAG,MAAMlK,uBAAuB,CAAC4N,QAAQ,CAAC;UAEtD,IAAI1D,MAAM,EAAE;YACV9L,OAAO,CAAC8P,OAAO,CAAC,eAAehI,WAAW,CAAC/B,MAAM,8BAA8B,CAAC;YAChF;YACA2D,WAAW,CAAC,KAAK,CAAC;YAClBE,WAAW,CAAC,CAAC,CAAC;UAChB;UAEA,OAAO,CAAC;QACV,CAAC,MAAM;UACL;UACA,MAAM4F,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;UAE/B,IAAIvI,UAAU,KAAK,QAAQ,EAAE;YAC3BsI,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAEtI,YAAY,CAACuI,aAAa,CAAC;UACrD,CAAC,MAAM;YACLH,QAAQ,CAACE,MAAM,CAAC,SAAS,EAAEpI,MAAM,CAAC;YAClCkI,QAAQ,CAACE,MAAM,CAAC,eAAe,EAAEhI,YAAY,CAAC;UAChD;UAEA8H,QAAQ,CAACE,MAAM,CAAC,gBAAgB,EAAEE,IAAI,CAACC,SAAS,CAAC1H,aAAa,CAAC,CAAC;UAChEqH,QAAQ,CAACE,MAAM,CAAC,oBAAoB,EAAEE,IAAI,CAACC,SAAS,CAACxH,iBAAiB,CAAC,CAAC;UACxEmH,QAAQ,CAACE,MAAM,CAAC,eAAe,EAAElH,YAAY,CAACwC,QAAQ,CAAC,CAAC,CAAC;UACzDwE,QAAQ,CAACE,MAAM,CAAC,YAAY,EAAEhH,SAAS,CAACsC,QAAQ,CAAC,CAAC,CAAC;UACnDwE,QAAQ,CAACE,MAAM,CAAC,QAAQ,EAAE9G,MAAM,CAACoC,QAAQ,CAAC,CAAC,CAAC;UAC5CwE,QAAQ,CAACE,MAAM,CAAC,iBAAiB,EAAE5G,cAAc,CAACkC,QAAQ,CAAC,CAAC,CAAC;UAC7DwE,QAAQ,CAACE,MAAM,CAAC,aAAa,EAAE1G,UAAU,CAACgC,QAAQ,CAAC,CAAC,CAAC;UACrDwE,QAAQ,CAACE,MAAM,CAAC,YAAY,EAAExG,SAAS,CAAC8B,QAAQ,CAAC,CAAC,CAAC;UACnDwE,QAAQ,CAACE,MAAM,CAAC,SAAS,EAAEtG,OAAO,CAAC4B,QAAQ,CAAC,CAAC,CAAC;UAC9CwE,QAAQ,CAACE,MAAM,CAAC,eAAe,EAAEzH,YAAY,CAAC;UAC9CuH,QAAQ,CAACE,MAAM,CAAC,wBAAwB,EAAEnG,oBAAoB,CAACyB,QAAQ,CAAC,CAAC,CAAC;;UAE1E;UACA,MAAMc,MAAM,GAAG,MAAMvB,kBAAkB,CAACiF,QAAQ,CAAC;UAEjD,IAAI1D,MAAM,EAAE;YACV9L,OAAO,CAAC8P,OAAO,CAAC,gCAAgC,CAAC;YACjD;YACApG,WAAW,CAAC,KAAK,CAAC;YAClBE,WAAW,CAAC,CAAC,CAAC;UAChB;UAEA,OAAO,CAAC;QACV;MACF;;MAEA;MACA,IAAIgD,QAAQ;MAEZ,IAAI5F,YAAY,KAAK,OAAO,EAAE;QAC5B;QACA,MAAMwI,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;;QAE/B;QACA3H,WAAW,CAAC0G,OAAO,CAAErD,MAAM,IAAK;UAC9B,IAAIA,MAAM,CAACxF,IAAI,KAAK,QAAQ,IAAIwF,MAAM,CAACgD,IAAI,EAAE;YAC3CqB,QAAQ,CAACE,MAAM,CAAC,OAAO,EAAEvE,MAAM,CAACgD,IAAI,CAACwB,aAAa,CAAC;UACrD;QACF,CAAC,CAAC;;QAEF;QACAH,QAAQ,CAACE,MAAM,CAAC,cAAc,EAAEE,IAAI,CAACC,SAAS,CAAC/H,WAAW,CAAC,CAAC;QAC5D0H,QAAQ,CAACE,MAAM,CAAC,gBAAgB,EAAEE,IAAI,CAACC,SAAS,CAAC1H,aAAa,CAAC,CAAC;QAChEqH,QAAQ,CAACE,MAAM,CAAC,oBAAoB,EAAEE,IAAI,CAACC,SAAS,CAACxH,iBAAiB,CAAC,CAAC;QACxEmH,QAAQ,CAACE,MAAM,CAAC,eAAe,EAAElH,YAAY,CAACwC,QAAQ,CAAC,CAAC,CAAC;QACzDwE,QAAQ,CAACE,MAAM,CAAC,YAAY,EAAEhH,SAAS,CAACsC,QAAQ,CAAC,CAAC,CAAC;QACnDwE,QAAQ,CAACE,MAAM,CAAC,QAAQ,EAAE9G,MAAM,CAACoC,QAAQ,CAAC,CAAC,CAAC;QAC5CwE,QAAQ,CAACE,MAAM,CAAC,iBAAiB,EAAE5G,cAAc,CAACkC,QAAQ,CAAC,CAAC,CAAC;QAC7DwE,QAAQ,CAACE,MAAM,CAAC,aAAa,EAAE1G,UAAU,CAACgC,QAAQ,CAAC,CAAC,CAAC;QACrDwE,QAAQ,CAACE,MAAM,CAAC,YAAY,EAAExG,SAAS,CAAC8B,QAAQ,CAAC,CAAC,CAAC;QACnDwE,QAAQ,CAACE,MAAM,CAAC,SAAS,EAAEtG,OAAO,CAAC4B,QAAQ,CAAC,CAAC,CAAC;QAC9CwE,QAAQ,CAACE,MAAM,CAAC,wBAAwB,EAAEnG,oBAAoB,CAACyB,QAAQ,CAAC,CAAC,CAAC;;QAE1E;QACA,MAAM+E,gBAAgB,GAAGC,WAAW,CAAC,MAAM;UACzCpG,WAAW,CAAEkF,IAAI,IAAK;YACpB,IAAIA,IAAI,IAAI,EAAE,EAAE;cACdmB,aAAa,CAACF,gBAAgB,CAAC;cAC/B,OAAOjB,IAAI;YACb;YACA,OAAOA,IAAI,GAAG,CAAC;UACjB,CAAC,CAAC;QACJ,CAAC,EAAE,IAAI,CAAC;QAERlC,QAAQ,GAAG,MAAMlL,gBAAgB,CAACwO,eAAe,CAACV,QAAQ,CAAC;QAC3DS,aAAa,CAACF,gBAAgB,CAAC;;QAE/B;QACA7F,uBAAuB,CAAC0C,QAAQ,CAAC/H,IAAI,CAAC;QACtC,IAAI+H,QAAQ,CAAC/H,IAAI,CAACsH,OAAO,IAAIC,MAAM,CAACC,IAAI,CAACO,QAAQ,CAAC/H,IAAI,CAACsH,OAAO,CAAC,CAACpG,MAAM,GAAG,CAAC,EAAE;UAC1E,MAAMoK,cAAc,GAAG/D,MAAM,CAACC,IAAI,CAACO,QAAQ,CAAC/H,IAAI,CAACsH,OAAO,CAAC,CAAC,CAAC,CAAC;UAC5D/B,yBAAyB,CAAC+F,cAAc,CAAC;UACzC,IAAIvD,QAAQ,CAAC/H,IAAI,CAACsH,OAAO,CAACgE,cAAc,CAAC,IAAIvD,QAAQ,CAAC/H,IAAI,CAACsH,OAAO,CAACgE,cAAc,CAAC,CAAChE,OAAO,EAAE;YAC1F7B,yBAAyB,CAAC8B,MAAM,CAACC,IAAI,CAACO,QAAQ,CAAC/H,IAAI,CAACsH,OAAO,CAACgE,cAAc,CAAC,CAAChE,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;UAC1F;QACF;MACF,CAAC,MAAM,IAAIjF,UAAU,KAAK,QAAQ,EAAE;QAClC,MAAMsI,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;QAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAEtI,YAAY,CAACuI,aAAa,CAAC;QACnDH,QAAQ,CAACE,MAAM,CAAC,gBAAgB,EAAEE,IAAI,CAACC,SAAS,CAAC1H,aAAa,CAAC,CAAC;QAChEqH,QAAQ,CAACE,MAAM,CAAC,oBAAoB,EAAEE,IAAI,CAACC,SAAS,CAACxH,iBAAiB,CAAC,CAAC;QACxEmH,QAAQ,CAACE,MAAM,CAAC,eAAe,EAAElH,YAAY,CAACwC,QAAQ,CAAC,CAAC,CAAC;QACzDwE,QAAQ,CAACE,MAAM,CAAC,YAAY,EAAEhH,SAAS,CAACsC,QAAQ,CAAC,CAAC,CAAC;QACnDwE,QAAQ,CAACE,MAAM,CAAC,QAAQ,EAAE9G,MAAM,CAACoC,QAAQ,CAAC,CAAC,CAAC;QAC5CwE,QAAQ,CAACE,MAAM,CAAC,iBAAiB,EAAE5G,cAAc,CAACkC,QAAQ,CAAC,CAAC,CAAC;QAC7DwE,QAAQ,CAACE,MAAM,CAAC,aAAa,EAAE1G,UAAU,CAACgC,QAAQ,CAAC,CAAC,CAAC;QACrDwE,QAAQ,CAACE,MAAM,CAAC,YAAY,EAAExG,SAAS,CAAC8B,QAAQ,CAAC,CAAC,CAAC;QACnDwE,QAAQ,CAACE,MAAM,CAAC,SAAS,EAAEtG,OAAO,CAAC4B,QAAQ,CAAC,CAAC,CAAC;QAC9CwE,QAAQ,CAACE,MAAM,CAAC,eAAe,EAAEzH,YAAY,CAAC;QAC9CuH,QAAQ,CAACE,MAAM,CAAC,wBAAwB,EAAEnG,oBAAoB,CAACyB,QAAQ,CAAC,CAAC,CAAC;;QAE1E;QACA,MAAM+E,gBAAgB,GAAGC,WAAW,CAAC,MAAM;UACzCpG,WAAW,CAAEkF,IAAI,IAAK;YACpB,IAAIA,IAAI,IAAI,EAAE,EAAE;cACdmB,aAAa,CAACF,gBAAgB,CAAC;cAC/B,OAAOjB,IAAI;YACb;YACA,OAAOA,IAAI,GAAG,CAAC;UACjB,CAAC,CAAC;QACJ,CAAC,EAAE,IAAI,CAAC;QAERlC,QAAQ,GAAG,MAAMlL,gBAAgB,CAAC0O,UAAU,CAACZ,QAAQ,CAAC;QACtDS,aAAa,CAACF,gBAAgB,CAAC;MACjC,CAAC,MAAM;QACL;QACA,MAAMM,iBAAiB,GAAG;UACxBC,OAAO,EAAEhJ,MAAM;UACfiJ,aAAa,EAAE7I,YAAY;UAC3B8I,cAAc,EAAErI,aAAa;UAC7BsI,kBAAkB,EAAEpI,iBAAiB;UACrCqI,aAAa,EAAElI,YAAY;UAC3BmI,UAAU,EAAEjI,SAAS;UACrBE,MAAM,EAAEA,MAAM;UACdgI,eAAe,EAAE9H,cAAc;UAC/B+H,WAAW,EAAE7H,UAAU;UACvB8H,UAAU,EAAE5H,SAAS;UACrBE,OAAO,EAAEA,OAAO;UAChB2H,aAAa,EAAE9I,YAAY;UAC3B+I,sBAAsB,EAAEzH;QAC1B,CAAC;;QAED;QACA,MAAMwG,gBAAgB,GAAGC,WAAW,CAAC,MAAM;UACzCpG,WAAW,CAAEkF,IAAI,IAAK;YACpB,IAAIA,IAAI,IAAI,EAAE,EAAE;cACdmB,aAAa,CAACF,gBAAgB,CAAC;cAC/B,OAAOjB,IAAI;YACb;YACA,OAAOA,IAAI,GAAG,CAAC;UACjB,CAAC,CAAC;QACJ,CAAC,EAAE,IAAI,CAAC;QAERlC,QAAQ,GAAG,MAAMlL,gBAAgB,CAACuP,eAAe,CAACZ,iBAAiB,CAAC;QACpEJ,aAAa,CAACF,gBAAgB,CAAC;MACjC;MAEAnG,WAAW,CAAC,GAAG,CAAC;;MAEhB;MACAsH,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEvE,QAAQ,CAAC/H,IAAI,CAAC;MAErCiF,kBAAkB,CAAC8C,QAAQ,CAAC/H,IAAI,CAAC;;MAEjC;MACA,IAAI+H,QAAQ,CAAC/H,IAAI,CAACsH,OAAO,IAAIC,MAAM,CAACC,IAAI,CAACO,QAAQ,CAAC/H,IAAI,CAACsH,OAAO,CAAC,CAACpG,MAAM,GAAG,CAAC,EAAE;QAC1EiE,oBAAoB,CAACoC,MAAM,CAACC,IAAI,CAACO,QAAQ,CAAC/H,IAAI,CAACsH,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;MAC7D;MAEAnM,OAAO,CAAC8P,OAAO,CAAC,SAAS,CAAC;;MAE1B;MACA,IAAIlD,QAAQ,CAAC/H,IAAI,CAACuM,WAAW,EAAE;QAC7BpR,OAAO,CAACgO,IAAI,CAAC,WAAWpB,QAAQ,CAAC/H,IAAI,CAACuM,WAAW,EAAE,CAAC;MACtD;IAEF,CAAC,CAAC,OAAOrE,KAAU,EAAE;MACnBmE,OAAO,CAACnE,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/BmE,OAAO,CAACnE,KAAK,CAAC,OAAO,EAAEA,KAAK,CAACH,QAAQ,CAAC;;MAEtC;MACA,IAAIyE,YAAY,GAAG,QAAQ;MAC3B,IAAItE,KAAK,CAACH,QAAQ,EAAE;QAAA,IAAA0E,qBAAA,EAAAC,qBAAA;QAClB,KAAAD,qBAAA,GAAIvE,KAAK,CAACH,QAAQ,CAAC/H,IAAI,cAAAyM,qBAAA,eAAnBA,qBAAA,CAAqBpE,MAAM,EAAE;UAC/BmE,YAAY,GAAGtE,KAAK,CAACH,QAAQ,CAAC/H,IAAI,CAACqI,MAAM;QAC3C,CAAC,MAAM,KAAAqE,qBAAA,GAAIxE,KAAK,CAACH,QAAQ,CAAC/H,IAAI,cAAA0M,qBAAA,eAAnBA,qBAAA,CAAqBvR,OAAO,EAAE;UACvCqR,YAAY,GAAGtE,KAAK,CAACH,QAAQ,CAAC/H,IAAI,CAAC7E,OAAO;QAC5C,CAAC,MAAM,IAAI+M,KAAK,CAACH,QAAQ,CAAC4E,UAAU,EAAE;UACpCH,YAAY,GAAG,SAAStE,KAAK,CAACH,QAAQ,CAAC6E,MAAM,IAAI1E,KAAK,CAACH,QAAQ,CAAC4E,UAAU,EAAE;QAC9E;MACF,CAAC,MAAM,IAAIzE,KAAK,CAAC/M,OAAO,EAAE;QACxBqR,YAAY,GAAGtE,KAAK,CAAC/M,OAAO;MAC9B;MAEAA,OAAO,CAAC+M,KAAK,CAACsE,YAAY,CAAC;IAC7B,CAAC,SAAS;MACR3H,WAAW,CAAC,KAAK,CAAC;IACpB;EACF,CAAC;EAED,MAAMgI,WAAW,GAAGA,CAAA,KAAM;IACxB,IAAI1K,YAAY,KAAK,OAAO,EAAE;MAC5B;MACA,MAAMgI,YAAY,GAAGlH,WAAW,CAACoD,MAAM,CAACC,MAAM,IAAI;QAChD,IAAIA,MAAM,CAACxF,IAAI,KAAK,QAAQ,EAAE;UAC5B,OAAOwF,MAAM,CAACgD,IAAI,IAAIhD,MAAM,CAAClD,YAAY;QAC3C,CAAC,MAAM;UACL,OAAOkD,MAAM,CAAC7D,MAAM,IAAI6D,MAAM,CAACzD,YAAY,IAAIyD,MAAM,CAAClD,YAAY;QACpE;MACF,CAAC,CAAC;MACF,OAAO+G,YAAY,CAACjJ,MAAM,GAAG,CAAC,IAAIoC,aAAa,CAACpC,MAAM,GAAG,CAAC;IAC5D,CAAC,MAAM;MACL;MACA,IAAImB,UAAU,KAAK,QAAQ,EAAE;QAC3B,OAAOE,YAAY,IAAIe,aAAa,CAACpC,MAAM,GAAG,CAAC;MACjD,CAAC,MAAM;QACL,OAAOuB,MAAM,IAAII,YAAY,IAAIS,aAAa,CAACpC,MAAM,GAAG,CAAC;MAC3D;IACF;EACF,CAAC;EAED,oBACEjE,OAAA;IAAAc,QAAA,gBACEd,OAAA,CAACC,KAAK;MAAC4P,KAAK,EAAE,CAAE;MAAC5O,KAAK,EAAE;QAAE6O,QAAQ,EAAE,MAAM;QAAEC,UAAU,EAAE,GAAG;QAAEC,YAAY,EAAE;MAAM,CAAE;MAAAlP,QAAA,EAAC;IAAS;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC,eACrGtB,OAAA,CAACE,IAAI;MAAC2D,IAAI,EAAC,WAAW;MAAA/C,QAAA,EAAC;IAEvB;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eAEPtB,OAAA,CAAC/B,OAAO;MAAAkD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGXtB,OAAA,CAACvC,IAAI;MAACgE,KAAK,EAAC,0BAAM;MAACwO,SAAS,EAAC,eAAe;MAAAnP,QAAA,eAC1Cd,OAAA,CAAChC,KAAK;QAAC+C,SAAS,EAAC,UAAU;QAACC,IAAI,EAAC,OAAO;QAACC,KAAK,EAAE;UAAEC,KAAK,EAAE;QAAO,CAAE;QAAAJ,QAAA,gBAChEd,OAAA;UAAAc,QAAA,gBACEd,OAAA,CAACE,IAAI;YAAC2C,MAAM;YAAA/B,QAAA,EAAC;UAAO;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC3BtB,OAAA,CAACtC,KAAK,CAACwS,KAAK;YACVxO,KAAK,EAAEwD,YAAa;YACpB+G,QAAQ,EAAGkE,CAAC,IAAKhL,eAAe,CAACgL,CAAC,CAACC,MAAM,CAAC1O,KAAK,CAAE;YACjDT,KAAK,EAAE;cAAEmB,SAAS,EAAE;YAAE,CAAE;YAAAtB,QAAA,gBAExBd,OAAA,CAACtC,KAAK;cAACgE,KAAK,EAAC,QAAQ;cAAAZ,QAAA,EAAC;YAAK;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACnCtB,OAAA,CAACtC,KAAK;cAACgE,KAAK,EAAC,OAAO;cAAAZ,QAAA,EAAC;YAAO;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX,CAAC,EAEL4D,YAAY,KAAK,OAAO,iBACvBlF,OAAA,CAACxB,KAAK;UACJN,OAAO,EAAC,wDAAW;UACnBsG,WAAW,EAAC,wVAA2D;UACvEX,IAAI,EAAC,MAAM;UACXiB,QAAQ;QAAA;UAAA3D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CACF;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAGPtB,OAAA,CAACvC,IAAI;MAACgE,KAAK,EAAEyD,YAAY,KAAK,OAAO,GAAG,OAAO,GAAG,KAAM;MAAC+K,SAAS,EAAC,eAAe;MAAAnP,QAAA,EAC/EoE,YAAY,KAAK,OAAO,gBACvBlF,OAAA,CAAChC,KAAK;QAAC+C,SAAS,EAAC,UAAU;QAACC,IAAI,EAAC,OAAO;QAACC,KAAK,EAAE;UAAEC,KAAK,EAAE;QAAO,CAAE;QAAAJ,QAAA,gBAEhEd,OAAA;UAAKiB,KAAK,EAAE;YAAE+O,YAAY,EAAE;UAAG,CAAE;UAAAlP,QAAA,eAC/Bd,OAAA,CAAClC,MAAM;YACL+F,IAAI,EAAC,QAAQ;YACbwM,IAAI,eAAErQ,OAAA,CAACf,YAAY;cAAAkC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACvBgP,OAAO,EAAExH,aAAc;YACvB7H,KAAK,EAAE;cAAEC,KAAK,EAAE;YAAO,CAAE;YAAAJ,QAAA,EAC1B;UAED;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,EAGL0E,WAAW,CAAChD,GAAG,CAAC,CAACqG,MAAM,EAAEnG,KAAK,kBAC7BlD,OAAA,CAACvC,IAAI;UAEHuD,IAAI,EAAC,OAAO;UACZS,KAAK,EAAE,OAAOyB,KAAK,GAAG,CAAC,EAAG;UAC1BqN,KAAK,EACHvK,WAAW,CAAC/B,MAAM,GAAG,CAAC,iBACpBjE,OAAA,CAAClC,MAAM;YACL+F,IAAI,EAAC,MAAM;YACX2M,MAAM;YACNH,IAAI,eAAErQ,OAAA,CAACd,cAAc;cAAAiC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACzBgP,OAAO,EAAEA,CAAA,KAAMnH,gBAAgB,CAACE,MAAM,CAACnD,EAAE;UAAE;YAAA/E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5C,CAEJ;UACDL,KAAK,EAAE;YAAE+O,YAAY,EAAE;UAAG,CAAE;UAAAlP,QAAA,eAE5Bd,OAAA,CAAChC,KAAK;YAAC+C,SAAS,EAAC,UAAU;YAACE,KAAK,EAAE;cAAEC,KAAK,EAAE;YAAO,CAAE;YAAAJ,QAAA,gBAEnDd,OAAA;cAAAc,QAAA,gBACEd,OAAA,CAACE,IAAI;gBAAC2C,MAAM;gBAAA/B,QAAA,EAAC;cAAM;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC1BtB,OAAA,CAACtC,KAAK,CAACwS,KAAK;gBACVxO,KAAK,EAAE2H,MAAM,CAACxF,IAAK;gBACnBoI,QAAQ,EAAGkE,CAAC,IAAK7G,gBAAgB,CAACD,MAAM,CAACnD,EAAE,EAAE;kBAAErC,IAAI,EAAEsM,CAAC,CAACC,MAAM,CAAC1O;gBAAM,CAAC,CAAE;gBACvET,KAAK,EAAE;kBAAEmB,SAAS,EAAE;gBAAE,CAAE;gBAAAtB,QAAA,gBAExBd,OAAA,CAACtC,KAAK;kBAACgE,KAAK,EAAC,OAAO;kBAAAZ,QAAA,EAAC;gBAAI;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACjCtB,OAAA,CAACtC,KAAK;kBAACgE,KAAK,EAAC,QAAQ;kBAAAZ,QAAA,EAAC;gBAAI;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACX,CAAC,EAGL+H,MAAM,CAACxF,IAAI,KAAK,OAAO,iBACtB7D,OAAA,CAAChC,KAAK;cAAC+C,SAAS,EAAC,UAAU;cAACE,KAAK,EAAE;gBAAEC,KAAK,EAAE;cAAO,CAAE;cAAAJ,QAAA,gBACnDd,OAAA;gBAAAc,QAAA,gBACEd,OAAA,CAACE,IAAI;kBAAC2C,MAAM;kBAAA/B,QAAA,EAAC;gBAAQ;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC5BtB,OAAA,CAACpC,KAAK,CAACsS,KAAK;kBAACO,OAAO;kBAACxP,KAAK,EAAE;oBAAEmB,SAAS,EAAE,CAAC;oBAAEsO,OAAO,EAAE;kBAAO,CAAE;kBAAA5P,QAAA,gBAC5Dd,OAAA,CAACpC,KAAK;oBACJ8D,KAAK,EAAE2H,MAAM,CAAC7D,MAAO;oBACrByG,QAAQ,EAAGkE,CAAC,IAAK7G,gBAAgB,CAACD,MAAM,CAACnD,EAAE,EAAE;sBAAEV,MAAM,EAAE2K,CAAC,CAACC,MAAM,CAAC1O;oBAAM,CAAC,CAAE;oBACzEiP,WAAW,EAAC,4BAAkB;oBAC9B1P,KAAK,EAAE;sBAAE2P,IAAI,EAAE;oBAAE;kBAAE;oBAAAzP,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpB,CAAC,eACFtB,OAAA,CAAClC,MAAM;oBACL+F,IAAI,EAAC,SAAS;oBACdyM,OAAO,EAAEA,CAAA,KAAMjF,sBAAsB,CAAChC,MAAM,CAACnD,EAAE,EAAEmD,MAAM,CAAC7D,MAAM,IAAI,EAAE,CAAE;oBACtEqL,OAAO,EAAExH,MAAM,CAACvD,YAAa;oBAC7BgL,QAAQ,EAAE,CAACzH,MAAM,CAAC7D,MAAO;oBACzBvE,KAAK,EAAE;sBAAE8P,UAAU,EAAE;oBAAE,CAAE;oBAAAjQ,QAAA,EAC1B;kBAED;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX,CAAC,eAENtB,OAAA;gBAAAc,QAAA,gBACEd,OAAA,CAACE,IAAI;kBAAC2C,MAAM;kBAAA/B,QAAA,EAAC;gBAAK;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACzBtB,OAAA,CAAC7B,IAAI;kBAAC6S,QAAQ,EAAE3H,MAAM,CAACvD,YAAY,IAAI,KAAM;kBAAAhF,QAAA,eAC3Cd,OAAA,CAACnC,MAAM;oBACL6D,KAAK,EAAE2H,MAAM,CAACzD,YAAa;oBAC3BqG,QAAQ,EAAGvK,KAAK,IAAK4H,gBAAgB,CAACD,MAAM,CAACnD,EAAE,EAAE;sBAAEN,YAAY,EAAElE;oBAAM,CAAC,CAAE;oBAC1EiP,WAAW,EAAC,mCAAU;oBACtB1P,KAAK,EAAE;sBAAEC,KAAK,EAAE,MAAM;sBAAEkB,SAAS,EAAE;oBAAE,CAAE;oBACvCyO,OAAO,EAAExH,MAAM,CAACvD,YAAa;oBAAAhF,QAAA,EAE5B,CAACuI,MAAM,CAAC3D,cAAc,IAAI,EAAE,EAAE1C,GAAG,CAAEqJ,IAAI,iBACtCrM,OAAA,CAACI,MAAM;sBAAYsB,KAAK,EAAE2K,IAAK;sBAAAvL,QAAA,EAC5BuL;oBAAI,GADMA,IAAI;sBAAAlL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAET,CACT;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACI;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CACR,EAGA+H,MAAM,CAACxF,IAAI,KAAK,QAAQ,iBACvB7D,OAAA;cAAAc,QAAA,gBACEd,OAAA,CAACE,IAAI;gBAAC2C,MAAM;gBAAA/B,QAAA,EAAC;cAAK;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACzBtB,OAAA,CAACrC,MAAM;gBAAA,GAAKyO,uBAAuB,CAAC/C,MAAM,CAACnD,EAAE,CAAC;gBAAEjF,KAAK,EAAE;kBAAEmB,SAAS,EAAE;gBAAE,CAAE;gBAAAtB,QAAA,eACtEd,OAAA,CAAClC,MAAM;kBAACuS,IAAI,eAAErQ,OAAA,CAACb,cAAc;oBAAAgC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBAAAR,QAAA,EAAC;gBAAO;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5C,CAAC,EACR+H,MAAM,CAACgD,IAAI,iBACVrM,OAAA;gBAAKiB,KAAK,EAAE;kBAAEmB,SAAS,EAAE;gBAAE,CAAE;gBAAAtB,QAAA,eAC3Bd,OAAA,CAACE,IAAI;kBAAC2D,IAAI,EAAC,WAAW;kBAAA/C,QAAA,GAAC,sBAAK,EAACuI,MAAM,CAACgD,IAAI,CAACR,IAAI;gBAAA;kBAAA1K,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClD,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CACN,eAGDtB,OAAA;cAAAc,QAAA,gBACEd,OAAA,CAACE,IAAI;gBAAC2C,MAAM;gBAAA/B,QAAA,EAAC;cAAO;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC3BtB,OAAA,CAACpC,KAAK;gBACJ8D,KAAK,EAAE2H,MAAM,CAAClD,YAAa;gBAC3B8F,QAAQ,EAAGkE,CAAC,IAAK7G,gBAAgB,CAACD,MAAM,CAACnD,EAAE,EAAE;kBAAEC,YAAY,EAAEgK,CAAC,CAACC,MAAM,CAAC1O;gBAAM,CAAC,CAAE;gBAC/EiP,WAAW,EAAC,mCAAyB;gBACrC1P,KAAK,EAAE;kBAAEmB,SAAS,EAAE;gBAAE,CAAE;gBACxB6O,MAAM,eAAEjR,OAAA,CAACnB,aAAa;kBAAAsC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD;QAAC,GApGH+H,MAAM,CAACnD,EAAE;UAAA/E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAqGV,CACP,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,gBAERtB,OAAA,CAAChC,KAAK;QAAC+C,SAAS,EAAC,UAAU;QAACC,IAAI,EAAC,OAAO;QAACC,KAAK,EAAE;UAAEC,KAAK,EAAE;QAAO,CAAE;QAAAJ,QAAA,gBAChEd,OAAA;UAAAc,QAAA,gBACEd,OAAA,CAACE,IAAI;YAAC2C,MAAM;YAAA/B,QAAA,EAAC;UAAM;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC1BtB,OAAA,CAACtC,KAAK,CAACwS,KAAK;YACVxO,KAAK,EAAE0D,UAAW;YAClB6G,QAAQ,EAAGkE,CAAC,IAAK9K,aAAa,CAAC8K,CAAC,CAACC,MAAM,CAAC1O,KAAK,CAAE;YAC/CT,KAAK,EAAE;cAAEmB,SAAS,EAAE;YAAE,CAAE;YAAAtB,QAAA,gBAExBd,OAAA,CAACtC,KAAK;cAACgE,KAAK,EAAC,OAAO;cAAAZ,QAAA,EAAC;YAAS;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACtCtB,OAAA,CAACtC,KAAK;cAACgE,KAAK,EAAC,QAAQ;cAAAZ,QAAA,EAAC;YAAO;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX,CAAC,EAGL8D,UAAU,KAAK,OAAO,iBACvBpF,OAAA,CAAChC,KAAK;UAAC+C,SAAS,EAAC,UAAU;UAACE,KAAK,EAAE;YAAEC,KAAK,EAAE;UAAO,CAAE;UAAAJ,QAAA,gBACnDd,OAAA;YAAAc,QAAA,gBACEd,OAAA,CAACE,IAAI;cAAC2C,MAAM;cAAA/B,QAAA,EAAC;YAAQ;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC5BtB,OAAA,CAACpC,KAAK,CAACsS,KAAK;cAACO,OAAO;cAACxP,KAAK,EAAE;gBAAEmB,SAAS,EAAE,CAAC;gBAAEsO,OAAO,EAAE;cAAO,CAAE;cAAA5P,QAAA,gBAC5Dd,OAAA,CAACpC,KAAK;gBACJ8D,KAAK,EAAE8D,MAAO;gBACdyG,QAAQ,EAAGkE,CAAC,IAAK1K,SAAS,CAAC0K,CAAC,CAACC,MAAM,CAAC1O,KAAK,CAAE;gBAC3CiP,WAAW,EAAC,4BAAkB;gBAC9B1P,KAAK,EAAE;kBAAE2P,IAAI,EAAE;gBAAE;cAAE;gBAAAzP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB,CAAC,eACFtB,OAAA,CAAClC,MAAM;gBACL+F,IAAI,EAAC,SAAS;gBACdyM,OAAO,EAAEzF,aAAc;gBACvBgG,OAAO,EAAE/K,YAAa;gBACtBgL,QAAQ,EAAE,CAACtL,MAAO;gBAClBvE,KAAK,EAAE;kBAAE8P,UAAU,EAAE;gBAAE,CAAE;gBAAAjQ,QAAA,EAC1B;cAED;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CAAC,eAENtB,OAAA;YAAAc,QAAA,gBACEd,OAAA,CAACE,IAAI;cAAC2C,MAAM;cAAA/B,QAAA,EAAC;YAAK;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACzBtB,OAAA,CAAC7B,IAAI;cAAC6S,QAAQ,EAAElL,YAAa;cAAAhF,QAAA,eAC3Bd,OAAA,CAACnC,MAAM;gBACL6D,KAAK,EAAEkE,YAAa;gBACpBqG,QAAQ,EAAEpG,eAAgB;gBAC1B8K,WAAW,EAAC,mCAAU;gBACtB1P,KAAK,EAAE;kBAAEC,KAAK,EAAE,MAAM;kBAAEkB,SAAS,EAAE;gBAAE,CAAE;gBACvCyO,OAAO,EAAE/K,YAAa;gBAAAhF,QAAA,EAErB4E,cAAc,CAAC1C,GAAG,CAAEqJ,IAAI,iBACvBrM,OAAA,CAACI,MAAM;kBAAYsB,KAAK,EAAE2K,IAAK;kBAAAvL,QAAA,EAC5BuL;gBAAI,GADMA,IAAI;kBAAAlL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAET,CACT;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CACR,EAGA8D,UAAU,KAAK,QAAQ,iBACtBpF,OAAA;UAAAc,QAAA,gBACEd,OAAA,CAACE,IAAI;YAAC2C,MAAM;YAAA/B,QAAA,EAAC;UAAK;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACzBtB,OAAA,CAACG,OAAO;YAAA,GAAKyL,WAAW;YAAE3K,KAAK,EAAE;cAAEmB,SAAS,EAAE;YAAE,CAAE;YAAAtB,QAAA,gBAChDd,OAAA;cAAGiQ,SAAS,EAAC,sBAAsB;cAAAnP,QAAA,eACjCd,OAAA,CAACnB,aAAa;gBAAAsC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChB,CAAC,eACJtB,OAAA;cAAGiQ,SAAS,EAAC,iBAAiB;cAAAnP,QAAA,EAAC;YAAgB;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACnDtB,OAAA;cAAGiQ,SAAS,EAAC,iBAAiB;cAAAnP,QAAA,EAAC;YAE/B;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM;IACR;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC,eAGPtB,OAAA,CAACvC,IAAI;MAACgE,KAAK,EAAC,wDAAW;MAACwO,SAAS,EAAC,eAAe;MAAAnP,QAAA,eAC/Cd,OAAA,CAAChC,KAAK;QAAC+C,SAAS,EAAC,UAAU;QAACC,IAAI,EAAC,OAAO;QAACC,KAAK,EAAE;UAAEC,KAAK,EAAE;QAAO,CAAE;QAAAJ,QAAA,gBAChEd,OAAA;UAAAc,QAAA,gBACEd,OAAA,CAACE,IAAI;YAAC2C,MAAM;YAAA/B,QAAA,EAAC;UAAK;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACzBtB,OAAA,CAACnC,MAAM;YACLqT,IAAI,EAAC,UAAU;YACfxP,KAAK,EAAE2E,aAAc;YACrB4F,QAAQ,EAAEM,oBAAqB;YAC/BoE,WAAW,EAAC,gCAAO;YACnB1P,KAAK,EAAE;cAAEC,KAAK,EAAE,MAAM;cAAEkB,SAAS,EAAE;YAAE,CAAE;YAAAtB,QAAA,EAEtC2J,eAAe,CAACzH,GAAG,CAAE2J,IAAI,iBACxB3M,OAAA,CAACI,MAAM;cAAYsB,KAAK,EAAEiL,IAAK;cAAA7L,QAAA,EAC5B6L;YAAI,GADMA,IAAI;cAAAxL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAET,CACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,EAEL+E,aAAa,CAACrD,GAAG,CAAE2J,IAAI,iBACtB3M,OAAA;UAAAc,QAAA,gBACEd,OAAA,CAACE,IAAI;YAAC2C,MAAM;YAAA/B,QAAA,GAAE6L,IAAI,EAAC,iCAAM;UAAA;YAAAxL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAChCtB,OAAA,CAAC1B,QAAQ,CAAC4R,KAAK;YACbxO,KAAK,EAAE6E,iBAAiB,CAACoG,IAAI,CAAC,IAAI,EAAG;YACrCV,QAAQ,EAAGc,SAAS,IAAKF,oBAAoB,CAACF,IAAI,EAAEI,SAAqB,CAAE;YAC3E9L,KAAK,EAAE;cAAEmB,SAAS,EAAE;YAAE,CAAE;YAAAtB,QAAA,EAEvB,CAAC4J,eAAe,CAACiC,IAAI,CAAiC,IAAI,EAAE,EAAE3J,GAAG,CAAEmO,QAAQ,iBAC1EnR,OAAA,CAAC1B,QAAQ;cAAgBoD,KAAK,EAAEyP,QAAS;cAAArQ,QAAA,EACtCqQ;YAAQ,GADIA,QAAQ;cAAAhQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEb,CACX;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACY,CAAC;QAAA,GAZTqL,IAAI;UAAAxL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAaT,CACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAGPtB,OAAA,CAACvC,IAAI;MACHgE,KAAK,eACHzB,OAAA,CAAChC,KAAK;QAAA8C,QAAA,gBACJd,OAAA,CAACjB,eAAe;UAAAoC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACnBtB,OAAA;UAAAc,QAAA,EAAM;QAAM;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACd,CACR;MACD2O,SAAS,EAAC,eAAe;MAAAnP,QAAA,gBAEzBd,OAAA,CAACvB,GAAG;QAAC8C,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;QAAAT,QAAA,gBAEpBd,OAAA,CAACtB,GAAG;UAAC8C,IAAI,EAAE,EAAG;UAAAV,QAAA,eACZd,OAAA,CAACvC,IAAI;YACHuD,IAAI,EAAC,OAAO;YACZS,KAAK,eACHzB,OAAA,CAAChC,KAAK;cAAA8C,QAAA,gBACJd,OAAA,CAAChB,kBAAkB;gBAAAmC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACtBtB,OAAA,CAACE,IAAI;gBAAC2C,MAAM;gBAAA/B,QAAA,EAAC;cAAI;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CACR;YAAAR,QAAA,eAEDd,OAAA,CAAChC,KAAK;cAAC+C,SAAS,EAAC,UAAU;cAACC,IAAI,EAAC,QAAQ;cAACC,KAAK,EAAE;gBAAEC,KAAK,EAAE;cAAO,CAAE;cAAAJ,QAAA,gBACjEd,OAAA,CAACvB,GAAG;gBAAC2S,KAAK,EAAC,QAAQ;gBAAAtQ,QAAA,gBACjBd,OAAA,CAACtB,GAAG;kBAAC8C,IAAI,EAAE,EAAG;kBAAAV,QAAA,eACZd,OAAA,CAACE,IAAI;oBAAC2C,MAAM;oBAAA/B,QAAA,EAAC;kBAAI;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrB,CAAC,eACNtB,OAAA,CAACtB,GAAG;kBAAC8C,IAAI,EAAE,EAAG;kBAAAV,QAAA,eACZd,OAAA,CAAC5B,WAAW;oBACVsD,KAAK,EAAEgF,YAAa;oBACpBuF,QAAQ,EAAGvK,KAAK,IAAKiF,eAAe,CAACjF,KAAK,IAAI,MAAM,CAAE;oBACtD2P,GAAG,EAAE,MAAO;oBACZC,GAAG,EAAE,CAAE;oBACPC,IAAI,EAAE,MAAO;oBACbtQ,KAAK,EAAE;sBAAEC,KAAK,EAAE;oBAAO,CAAE;oBACzByP,WAAW,EAAC;kBAAQ;oBAAAxP,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNtB,OAAA,CAACvB,GAAG;gBAAC2S,KAAK,EAAC,QAAQ;gBAAAtQ,QAAA,gBACjBd,OAAA,CAACtB,GAAG;kBAAC8C,IAAI,EAAE,EAAG;kBAAAV,QAAA,eACZd,OAAA,CAACE,IAAI;oBAAC2C,MAAM;oBAAA/B,QAAA,EAAC;kBAAK;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtB,CAAC,eACNtB,OAAA,CAACtB,GAAG;kBAAC8C,IAAI,EAAE,EAAG;kBAAAV,QAAA,eACZd,OAAA,CAAC5B,WAAW;oBACVsD,KAAK,EAAEkF,SAAU;oBACjBqF,QAAQ,EAAGvK,KAAK,IAAKmF,YAAY,CAACnF,KAAK,IAAI,EAAE,CAAE;oBAC/C2P,GAAG,EAAE,CAAE;oBACPC,GAAG,EAAE,GAAI;oBACTrQ,KAAK,EAAE;sBAAEC,KAAK,EAAE;oBAAO,CAAE;oBACzByP,WAAW,EAAC;kBAAI;oBAAAxP,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNtB,OAAA,CAACvB,GAAG;gBAAC2S,KAAK,EAAC,QAAQ;gBAAAtQ,QAAA,gBACjBd,OAAA,CAACtB,GAAG;kBAAC8C,IAAI,EAAE,EAAG;kBAAAV,QAAA,eACZd,OAAA,CAACE,IAAI;oBAAC2C,MAAM;oBAAA/B,QAAA,EAAC;kBAAK;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtB,CAAC,eACNtB,OAAA,CAACtB,GAAG;kBAAC8C,IAAI,EAAE,EAAG;kBAAAV,QAAA,eACZd,OAAA,CAAC5B,WAAW;oBACVsD,KAAK,EAAEoF,MAAO;oBACdmF,QAAQ,EAAGvK,KAAK,IAAKqF,SAAS,CAACrF,KAAK,IAAI,GAAG,CAAE;oBAC7C2P,GAAG,EAAE,CAAE;oBACPC,GAAG,EAAE,IAAK;oBACVrQ,KAAK,EAAE;sBAAEC,KAAK,EAAE;oBAAO,CAAE;oBACzByP,WAAW,EAAC;kBAAK;oBAAAxP,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAGNtB,OAAA,CAACtB,GAAG;UAAC8C,IAAI,EAAE,EAAG;UAAAV,QAAA,eACZd,OAAA,CAACvC,IAAI;YACHuD,IAAI,EAAC,OAAO;YACZS,KAAK,eACHzB,OAAA,CAAChC,KAAK;cAAA8C,QAAA,gBACJd,OAAA,CAACjB,eAAe;gBAAAoC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACnBtB,OAAA,CAACE,IAAI;gBAAC2C,MAAM;gBAAA/B,QAAA,EAAC;cAAI;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CACR;YAAAR,QAAA,eAEDd,OAAA,CAAChC,KAAK;cAAC+C,SAAS,EAAC,UAAU;cAACC,IAAI,EAAC,QAAQ;cAACC,KAAK,EAAE;gBAAEC,KAAK,EAAE;cAAO,CAAE;cAAAJ,QAAA,gBACjEd,OAAA,CAACvB,GAAG;gBAAC2S,KAAK,EAAC,QAAQ;gBAAAtQ,QAAA,gBACjBd,OAAA,CAACtB,GAAG;kBAAC8C,IAAI,EAAE,EAAG;kBAAAV,QAAA,eACZd,OAAA,CAACE,IAAI;oBAAC2C,MAAM;oBAAA/B,QAAA,EAAC;kBAAK;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtB,CAAC,eACNtB,OAAA,CAACtB,GAAG;kBAAC8C,IAAI,EAAE,EAAG;kBAAAV,QAAA,eACZd,OAAA,CAAC5B,WAAW;oBACVsD,KAAK,EAAEsF,cAAe;oBACtBiF,QAAQ,EAAGvK,KAAK,IAAKuF,iBAAiB,CAACvF,KAAK,IAAI,EAAE,CAAE;oBACpD2P,GAAG,EAAE,CAAE;oBACPC,GAAG,EAAE,GAAI;oBACTrQ,KAAK,EAAE;sBAAEC,KAAK,EAAE;oBAAO,CAAE;oBACzByP,WAAW,EAAC;kBAAI;oBAAAxP,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNtB,OAAA,CAACvB,GAAG;gBAAC2S,KAAK,EAAC,QAAQ;gBAAAtQ,QAAA,gBACjBd,OAAA,CAACtB,GAAG;kBAAC8C,IAAI,EAAE,EAAG;kBAAAV,QAAA,eACZd,OAAA,CAACE,IAAI;oBAAC2C,MAAM;oBAAA/B,QAAA,EAAC;kBAAM;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvB,CAAC,eACNtB,OAAA,CAACtB,GAAG;kBAAC8C,IAAI,EAAE,EAAG;kBAAAV,QAAA,eACZd,OAAA,CAAC5B,WAAW;oBACVsD,KAAK,EAAEwF,UAAW;oBAClB+E,QAAQ,EAAGvK,KAAK,IAAKyF,aAAa,CAACzF,KAAK,IAAI,EAAE,CAAE;oBAChD2P,GAAG,EAAE,EAAG;oBACRC,GAAG,EAAE,GAAI;oBACTC,IAAI,EAAE,EAAG;oBACTtQ,KAAK,EAAE;sBAAEC,KAAK,EAAE;oBAAO,CAAE;oBACzByP,WAAW,EAAC;kBAAI;oBAAAxP,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNtB,OAAA,CAACvB,GAAG;gBAAC2S,KAAK,EAAC,QAAQ;gBAAAtQ,QAAA,gBACjBd,OAAA,CAACtB,GAAG;kBAAC8C,IAAI,EAAE,EAAG;kBAAAV,QAAA,eACZd,OAAA,CAACE,IAAI;oBAAC2C,MAAM;oBAAA/B,QAAA,EAAC;kBAAG;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpB,CAAC,eACNtB,OAAA,CAACtB,GAAG;kBAAC8C,IAAI,EAAE,EAAG;kBAAAV,QAAA,eACZd,OAAA,CAAC5B,WAAW;oBACVsD,KAAK,EAAE0F,SAAU;oBACjB6E,QAAQ,EAAGvK,KAAK,IAAK2F,YAAY,CAAC3F,KAAK,IAAI,CAAC,CAAE;oBAC9C2P,GAAG,EAAE,CAAE;oBACPC,GAAG,EAAE,EAAG;oBACRrQ,KAAK,EAAE;sBAAEC,KAAK,EAAE;oBAAO,CAAE;oBACzByP,WAAW,EAAC;kBAAG;oBAAAxP,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNtB,OAAA;gBAAKiB,KAAK,EAAE;kBAAEC,KAAK,EAAE;gBAAO,CAAE;gBAAAJ,QAAA,gBAC5Bd,OAAA,CAACvB,GAAG;kBAAC2S,KAAK,EAAC,QAAQ;kBAACnQ,KAAK,EAAE;oBAAE+O,YAAY,EAAE;kBAAE,CAAE;kBAAAlP,QAAA,gBAC7Cd,OAAA,CAACtB,GAAG;oBAAC8C,IAAI,EAAE,EAAG;oBAAAV,QAAA,eACZd,OAAA,CAACE,IAAI;sBAAC2C,MAAM;sBAAA/B,QAAA,EAAC;oBAAW;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5B,CAAC,eACNtB,OAAA,CAACtB,GAAG;oBAAC8C,IAAI,EAAE,EAAG;oBAACP,KAAK,EAAE;sBAAEuQ,SAAS,EAAE;oBAAQ,CAAE;oBAAA1Q,QAAA,eAC3Cd,OAAA,CAACE,IAAI;sBAACuR,IAAI;sBAAA3Q,QAAA,EAAEwG;oBAAO;sBAAAnG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNtB,OAAA,CAAC3B,MAAM;kBACLqD,KAAK,EAAE4F,OAAQ;kBACf2E,QAAQ,EAAE1E,UAAW;kBACrB8J,GAAG,EAAE,CAAE;kBACPC,GAAG,EAAE,GAAI;kBACTC,IAAI,EAAE,IAAK;kBACXG,KAAK,EAAE;oBACL,CAAC,EAAE,GAAG;oBACN,GAAG,EAAE,KAAK;oBACV,GAAG,EAAE,KAAK;oBACV,GAAG,EAAE;kBACP;gBAAE;kBAAAvQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAGL4D,YAAY,KAAK,QAAQ,iBACxBlF,OAAA,CAACvB,GAAG;QAACwC,KAAK,EAAE;UAAEmB,SAAS,EAAE;QAAG,CAAE;QAAAtB,QAAA,eAC5Bd,OAAA,CAACtB,GAAG;UAAC8C,IAAI,EAAE,EAAG;UAAAV,QAAA,eACZd,OAAA,CAACvC,IAAI;YACHuD,IAAI,EAAC,OAAO;YACZS,KAAK,eACHzB,OAAA,CAAChC,KAAK;cAAA8C,QAAA,gBACJd,OAAA,CAACnB,aAAa;gBAAAsC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACjBtB,OAAA,CAACE,IAAI;gBAAC2C,MAAM;gBAAA/B,QAAA,EAAC;cAAM;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB,CACR;YAAAR,QAAA,eAEDd,OAAA,CAACpC,KAAK;cACJ8D,KAAK,EAAEyE,YAAa;cACpB8F,QAAQ,EAAGkE,CAAC,IAAK3I,eAAe,CAAC2I,CAAC,CAACC,MAAM,CAAC1O,KAAK,CAAE;cACjDiP,WAAW,EAAC,4BAAkB;cAC9B3P,IAAI,EAAC,OAAO;cACZiQ,MAAM,eAAEjR,OAAA,CAACnB,aAAa;gBAAAsC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC,eAGPtB,OAAA,CAACvC,IAAI;MAACwS,SAAS,EAAC,eAAe;MAACxO,KAAK,EAAC,sCAAQ;MAAAX,QAAA,eAC5Cd,OAAA,CAAChC,KAAK;QAAC+C,SAAS,EAAC,UAAU;QAACC,IAAI,EAAC,QAAQ;QAACC,KAAK,EAAE;UAAEC,KAAK,EAAE;QAAO,CAAE;QAAAJ,QAAA,eACjEd,OAAA;UAAAc,QAAA,gBACEd,OAAA,CAACE,IAAI;YAAC2C,MAAM;YAAA/B,QAAA,EAAC;UAAS;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC7BtB,OAAA;YAAKiB,KAAK,EAAE;cAAEmB,SAAS,EAAE;YAAE,CAAE;YAAAtB,QAAA,eAC3Bd,OAAA,CAAC1B,QAAQ;cACPqT,OAAO,EAAElK,oBAAqB;cAC9BwE,QAAQ,EAAGkE,CAAC,IAAKzI,uBAAuB,CAACyI,CAAC,CAACC,MAAM,CAACuB,OAAO,CAAE;cAAA7Q,QAAA,EAC5D;YAED;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC,eACNtB,OAAA;YAAKiB,KAAK,EAAE;cAAEmB,SAAS,EAAE;YAAE,CAAE;YAAAtB,QAAA,eAC3Bd,OAAA,CAACE,IAAI;cAAC2D,IAAI,EAAC,WAAW;cAAC5C,KAAK,EAAE;gBAAE6O,QAAQ,EAAE;cAAO,CAAE;cAAAhP,QAAA,EAAC;YAGpD;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAGPtB,OAAA,CAACvC,IAAI;MAACwS,SAAS,EAAC,eAAe;MAACxO,KAAK,EAAC,0BAAM;MAAAX,QAAA,eAC1Cd,OAAA,CAAChC,KAAK;QAAC+C,SAAS,EAAC,UAAU;QAACC,IAAI,EAAC,QAAQ;QAACC,KAAK,EAAE;UAAEC,KAAK,EAAE;QAAO,CAAE;QAAAJ,QAAA,gBACjEd,OAAA;UAAAc,QAAA,gBACEd,OAAA,CAACE,IAAI;YAAC2C,MAAM;YAAA/B,QAAA,EAAC;UAAO;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC3BtB,OAAA,CAACtC,KAAK,CAACwS,KAAK;YACVxO,KAAK,EAAEkH,gBAAiB;YACxBqD,QAAQ,EAAGkE,CAAC,IAAKtH,mBAAmB,CAACsH,CAAC,CAACC,MAAM,CAAC1O,KAAK,CAAE;YACrDT,KAAK,EAAE;cAAEmB,SAAS,EAAE;YAAE,CAAE;YAAAtB,QAAA,gBAExBd,OAAA,CAACtC,KAAK;cAACgE,KAAK,EAAE,IAAK;cAAAZ,QAAA,eACjBd,OAAA,CAAChC,KAAK;gBAAA8C,QAAA,GAAC,kDAEL,eAAAd,OAAA,CAACE,IAAI;kBAAC2D,IAAI,EAAC,WAAW;kBAAC5C,KAAK,EAAE;oBAAE6O,QAAQ,EAAE;kBAAG,CAAE;kBAAAhP,QAAA,EAAC;gBAEhD;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACRtB,OAAA,CAACtC,KAAK;cAACgE,KAAK,EAAE,KAAM;cAAAZ,QAAA,eAClBd,OAAA,CAAChC,KAAK;gBAAA8C,QAAA,GAAC,0BAEL,eAAAd,OAAA,CAACE,IAAI;kBAAC2D,IAAI,EAAC,WAAW;kBAAC5C,KAAK,EAAE;oBAAE6O,QAAQ,EAAE;kBAAG,CAAE;kBAAAhP,QAAA,EAAC;gBAEhD;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX,CAAC,EAELsH,gBAAgB,iBACf5I,OAAA,CAACxB,KAAK;UACJN,OAAO,EAAC,sCAAQ;UAChBsG,WAAW,eACTxE,OAAA;YAAAc,QAAA,GAAK,8JAEH,eAAAd,OAAA;cAAAmB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,mFACQ,eAAAtB,OAAA;cAAAc,QAAA,EAAQ;YAAY;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,2DAC7C;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CACN;UACDuC,IAAI,EAAC,MAAM;UACXiB,QAAQ;QAAA;UAAA3D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CACF;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAGPtB,OAAA,CAACvC,IAAI;MAACwS,SAAS,EAAC,eAAe;MAAAnP,QAAA,gBAC7Bd,OAAA,CAAClC,MAAM;QACL+F,IAAI,EAAC,SAAS;QACd7C,IAAI,EAAC,OAAO;QACZqP,IAAI,eAAErQ,OAAA,CAAClB,kBAAkB;UAAAqC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAC7BgP,OAAO,EAAE/C,mBAAoB;QAC7BsD,OAAO,EAAElJ,QAAS;QAClBmJ,QAAQ,EAAE,CAAClB,WAAW,CAAC,CAAE;QACzBK,SAAS,EAAC,eAAe;QAAAnP,QAAA,EAExB6G,QAAQ,GAAG,SAAS,GAAG;MAAQ;QAAAxG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1B,CAAC,EAGRqG,QAAQ,iBACP3H,OAAA;QAAKiQ,SAAS,EAAC,kBAAkB;QAAAnP,QAAA,gBAC/Bd,OAAA,CAACE,IAAI;UAAAY,QAAA,EAAC;QAAK;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAClBtB,OAAA,CAACzB,QAAQ;UAACqT,OAAO,EAAE/J,QAAS;UAAC8H,MAAM,EAAC;QAAQ;UAAAxO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5C,CACN,EAGAyG,eAAe,IAAIA,eAAe,CAACsC,OAAO,iBACzCrK,OAAA;QAAKiB,KAAK,EAAE;UAAEmB,SAAS,EAAE;QAAG,CAAE;QAAAtB,QAAA,eAC5Bd,OAAA,CAACxB,KAAK;UACJN,OAAO,EAAC,0BAAM;UACdsG,WAAW,eACTxE,OAAA;YAAAc,QAAA,gBACEd,OAAA;cAAAc,QAAA,EAAG;YAAS;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,EACfyG,eAAe,CAACuH,WAAW,iBAC1BtP,OAAA;cAAAc,QAAA,gBAAGd,OAAA;gBAAAc,QAAA,EAAQ;cAAO;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACyG,eAAe,CAACuH,WAAW;YAAA;cAAAnO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAC7D,EACAyG,eAAe,CAAC8J,aAAa,iBAC5B7R,OAAA;cAAKiB,KAAK,EAAE;gBAAEmB,SAAS,EAAE,CAAC;gBAAE0P,OAAO,EAAE,CAAC;gBAAEC,eAAe,EAAE,SAAS;gBAAEC,MAAM,EAAE,mBAAmB;gBAAEC,YAAY,EAAE;cAAE,CAAE;cAAAnR,QAAA,EAChHiH,eAAe,CAAC8J,aAAa,CAACK,kBAAkB,gBAC/ClS,OAAA;gBAAAc,QAAA,gBACEd,OAAA;kBAAGiB,KAAK,EAAE;oBAAEiB,KAAK,EAAE,SAAS;oBAAE6N,UAAU,EAAE;kBAAO,CAAE;kBAAAjP,QAAA,EAAC;gBAAW;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACnEtB,OAAA;kBAAAc,QAAA,gBAAGd,OAAA;oBAAAc,QAAA,EAAQ;kBAAK;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,KAAC,EAACyG,eAAe,CAAC8J,aAAa,CAACM,aAAa;gBAAA;kBAAAhR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC3EtB,OAAA;kBAAAc,QAAA,gBAAGd,OAAA;oBAAAc,QAAA,EAAQ;kBAAO;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,KAAC,EAACyG,eAAe,CAAC8J,aAAa,CAACO,kBAAkB;gBAAA;kBAAAjR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/E,CAAC,gBAENtB,OAAA;gBAAAc,QAAA,gBACEd,OAAA;kBAAGiB,KAAK,EAAE;oBAAEiB,KAAK,EAAE,SAAS;oBAAE6N,UAAU,EAAE;kBAAO,CAAE;kBAAAjP,QAAA,EAAC;gBAAU;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eAClEtB,OAAA;kBAAAc,QAAA,gBAAGd,OAAA;oBAAAc,QAAA,EAAQ;kBAAK;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,KAAC,EAACyG,eAAe,CAAC8J,aAAa,CAAC5G,KAAK;gBAAA;kBAAA9J,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChE;YACN;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CACN,EACAgJ,MAAM,CAAC+H,OAAO,CAACtK,eAAe,CAACsC,OAAO,CAAC,CAACrH,GAAG,CAAC,CAAC,CAACsP,GAAG,EAAE9R,MAAM,CAAgB,kBACxER,OAAA;cAAeiB,KAAK,EAAE;gBAAEmB,SAAS,EAAE;cAAE,CAAE;cAAAtB,QAAA,gBACrCd,OAAA;gBAAAc,QAAA,gBAAGd,OAAA;kBAAAc,QAAA,EAAQ;gBAAQ;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACgR,GAAG;cAAA;gBAAAnR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACtCtB,OAAA;gBAAAc,QAAA,GAAG,wCAAQ,EAACN,MAAM,CAAC+D,eAAe;cAAA;gBAAApD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACvCtB,OAAA;gBAAAc,QAAA,GAAG,oDAAU,EAACN,MAAM,CAACiE,kBAAkB;cAAA;gBAAAtD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA,GAHpCgR,GAAG;cAAAnR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAIR,CACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CACN;UACDuC,IAAI,EAAC,SAAS;UACdiB,QAAQ;UACR7D,KAAK,EAAE;YAAEmB,SAAS,EAAE;UAAG;QAAE;UAAAjB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACN,EAGFyG,eAAe,IAAIA,eAAe,CAACsC,OAAO,IAAIC,MAAM,CAACC,IAAI,CAACxC,eAAe,CAACsC,OAAO,CAAC,CAACpG,MAAM,GAAG,CAAC,iBAC5FjE,OAAA,CAACvC,IAAI;QAACgE,KAAK,EAAC,gFAAe;QAACwO,SAAS,EAAC,eAAe;QAAAnP,QAAA,eACnDd,OAAA,CAAChC,KAAK;UAAC+C,SAAS,EAAC,UAAU;UAACC,IAAI,EAAC,OAAO;UAACC,KAAK,EAAE;YAAEC,KAAK,EAAE;UAAO,CAAE;UAAAJ,QAAA,gBAChEd,OAAA;YAAAc,QAAA,gBACEd,OAAA,CAACE,IAAI;cAAC2C,MAAM;cAAA/B,QAAA,EAAC;YAAc;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAClCtB,OAAA,CAACnC,MAAM;cACL6D,KAAK,EAAEuG,iBAAkB;cACzBgE,QAAQ,EAAE/D,oBAAqB;cAC/BjH,KAAK,EAAE;gBAAEC,KAAK,EAAE,MAAM;gBAAEkB,SAAS,EAAE;cAAE,CAAE;cACvCuO,WAAW,EAAC,8DAAY;cAAA7P,QAAA,EAEvBwJ,MAAM,CAACC,IAAI,CAACxC,eAAe,CAACsC,OAAO,CAAC,CAACrH,GAAG,CAAEsP,GAAG,iBAC5CtS,OAAA,CAACI,MAAM;gBAAWsB,KAAK,EAAE4Q,GAAI;gBAAAxR,QAAA,EAC1BwR;cAAG,GADOA,GAAG;gBAAAnR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAER,CACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,EAEL2G,iBAAiB,IAAIF,eAAe,CAACsC,OAAO,CAACpC,iBAAiB,CAAC,iBAC9DjI,OAAA,CAACM,qBAAqB;YACpBC,SAAS,EAAE0H,iBAAkB;YAC7BzH,MAAM,EAAEuH,eAAe,CAACsC,OAAO,CAACpC,iBAAiB;UAAE;YAAA9G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpD,CACF;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CACP,EAGA6G,oBAAoB,IAAIA,oBAAoB,CAACkC,OAAO,IAAIC,MAAM,CAACC,IAAI,CAACpC,oBAAoB,CAACkC,OAAO,CAAC,CAACpG,MAAM,GAAG,CAAC,iBAC3GjE,OAAA,CAACvC,IAAI;QAACgE,KAAK,EAAC,4CAAS;QAACwO,SAAS,EAAC,eAAe;QAAAnP,QAAA,gBAC7Cd,OAAA,CAACxB,KAAK;UACJN,OAAO,EAAC,4CAAS;UACjBsG,WAAW,EAAE,QAAQ8F,MAAM,CAACC,IAAI,CAACpC,oBAAoB,CAACkC,OAAO,CAAC,CAACpG,MAAM,YAAa;UAClFJ,IAAI,EAAC,SAAS;UACdiB,QAAQ;UACR7D,KAAK,EAAE;YAAE+O,YAAY,EAAE;UAAG;QAAE;UAAA7O,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CAAC,eACFtB,OAAA,CAACpB,IAAI;UAAAkC,QAAA,EACFwJ,MAAM,CAAC+H,OAAO,CAAClK,oBAAoB,CAACkC,OAAO,CAAC,CAACrH,GAAG,CAAC,CAAC,CAACsI,QAAQ,EAAEiH,YAAY,CAAgB,kBACxFvS,OAAA,CAACK,OAAO;YAACmS,GAAG,EAAE,OAAOlH,QAAQ,EAAG;YAAAxK,QAAA,EAC7ByR,YAAY,CAACtH,KAAK,gBACjBjL,OAAA,CAACxB,KAAK;cACJN,OAAO,EAAC,0BAAM;cACdsG,WAAW,EAAE+N,YAAY,CAACtH,KAAM;cAChCpH,IAAI,EAAC,OAAO;cACZiB,QAAQ;YAAA;cAAA3D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,gBAEFtB,OAAA;cAAAc,QAAA,EAEGyR,YAAY,CAAClI,OAAO,IAAIC,MAAM,CAACC,IAAI,CAACgI,YAAY,CAAClI,OAAO,CAAC,CAACpG,MAAM,GAAG,CAAC,gBACnEjE,OAAA,CAAChC,KAAK;gBAAC+C,SAAS,EAAC,UAAU;gBAACC,IAAI,EAAC,OAAO;gBAACC,KAAK,EAAE;kBAAEC,KAAK,EAAE;gBAAO,CAAE;gBAAAJ,QAAA,gBAChEd,OAAA;kBAAAc,QAAA,gBACEd,OAAA,CAACE,IAAI;oBAAC2C,MAAM;oBAAA/B,QAAA,EAAC;kBAAc;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAClCtB,OAAA,CAACnC,MAAM;oBACL6D,KAAK,EAAE2G,sBAAsB,KAAKiD,QAAQ,GAAG/C,sBAAsB,GAAG,EAAG;oBACzE0D,QAAQ,EAAGvK,KAAK,IAAK;sBACnB4G,yBAAyB,CAACgD,QAAQ,CAAC;sBACnC9C,yBAAyB,CAAC9G,KAAK,CAAC;oBAClC,CAAE;oBACFT,KAAK,EAAE;sBAAEC,KAAK,EAAE,MAAM;sBAAEkB,SAAS,EAAE;oBAAE,CAAE;oBACvCuO,WAAW,EAAC,8DAAY;oBAAA7P,QAAA,EAEvBwJ,MAAM,CAACC,IAAI,CAACgI,YAAY,CAAClI,OAAO,CAAC,CAACrH,GAAG,CAAEsP,GAAG,iBACzCtS,OAAA,CAACI,MAAM;sBAAWsB,KAAK,EAAE4Q,GAAI;sBAAAxR,QAAA,EAC1BwR;oBAAG,GADOA,GAAG;sBAAAnR,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAER,CACT;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,EAEL+G,sBAAsB,KAAKiD,QAAQ,IAAI/C,sBAAsB,IAAIgK,YAAY,CAAClI,OAAO,CAAC9B,sBAAsB,CAAC,iBAC5GvI,OAAA,CAACM,qBAAqB;kBACpBC,SAAS,EAAEgI,sBAAuB;kBAClC/H,MAAM,EAAE+R,YAAY,CAAClI,OAAO,CAAC9B,sBAAsB;gBAAE;kBAAApH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtD,CACF;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC,gBAERtB,OAAA,CAACxB,KAAK;gBACJN,OAAO,EAAC,gCAAO;gBACfsG,WAAW,EAAC,4FAAiB;gBAC7BX,IAAI,EAAC,SAAS;gBACdiB,QAAQ;cAAA;gBAAA3D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT;YACF;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UACN,GAhDmCgK,QAAQ;YAAAnK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAiDrC,CACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACP,EAGAwI,sBAAsB,CAAC7F,MAAM,GAAG,CAAC,iBAChCjE,OAAA,CAACvC,IAAI;QAACgE,KAAK,EAAC,sCAAQ;QAACwO,SAAS,EAAC,eAAe;QAAChP,KAAK,EAAE;UAAEmB,SAAS,EAAE;QAAG,CAAE;QAAAtB,QAAA,gBACtEd,OAAA,CAACxB,KAAK;UACJN,OAAO,EAAC,4CAAS;UACjBsG,WAAW,EAAC,wMAAmC;UAC/CX,IAAI,EAAC,SAAS;UACdiB,QAAQ;UACR7D,KAAK,EAAE;YAAE+O,YAAY,EAAE;UAAG;QAAE;UAAA7O,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CAAC,eACFtB,OAAA,CAAChC,KAAK;UAAC+C,SAAS,EAAC,UAAU;UAACC,IAAI,EAAC,OAAO;UAACC,KAAK,EAAE;YAAEC,KAAK,EAAE;UAAO,CAAE;UAAAJ,QAAA,gBAEhEd,OAAA;YAAAc,QAAA,gBACEd,OAAA,CAACE,IAAI;cAAC2C,MAAM;cAAA/B,QAAA,EAAC;YAAO;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC3BtB,OAAA,CAACnC,MAAM;cACL6D,KAAK,EAAEkI,mBAAoB;cAC3BqC,QAAQ,EAAElC,qBAAsB;cAChC9I,KAAK,EAAE;gBAAEC,KAAK,EAAE,MAAM;gBAAEkB,SAAS,EAAE;cAAE,CAAE;cACvCuO,WAAW,EAAC,oEAAa;cAAA7P,QAAA,EAExBgJ,sBAAsB,CAAC9G,GAAG,CAAEmH,IAAI,iBAC/BnK,OAAA,CAACI,MAAM;gBAAoBsB,KAAK,EAAEyI,IAAI,CAACC,OAAQ;gBAAAtJ,QAAA,EAC5CqJ,IAAI,CAACC,OAAO,CAACwC,QAAQ,CAAC,GAAG,CAAC,GACzB,GAAGzC,IAAI,CAACC,OAAO,CAACvJ,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,IAAImI,IAAI,CAACmB,IAAI,CAACsI,UAAU,IAAItI,IAAI,CAACuI,UAAU,CAAC,CAACC,cAAc,CAAC,CAAC,GAAG,GAClG,MAAMxI,IAAI,CAACC,OAAO,CAACwI,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,QAAQ,IAAI5J,IAAI,CAACmB,IAAI,CAACsI,UAAU,IAAItI,IAAI,CAACuI,UAAU,CAAC,CAACC,cAAc,CAAC,CAAC;cAAG,GAHjGxI,IAAI,CAACC,OAAO;gBAAAjJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAKjB,CACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,EAGLkI,oBAAoB,IAAIA,oBAAoB,CAACqI,aAAa,iBACzD7R,OAAA;YAAKiB,KAAK,EAAE;cAAEmB,SAAS,EAAE,CAAC;cAAE0P,OAAO,EAAE,CAAC;cAAEC,eAAe,EAAE,SAAS;cAAEC,MAAM,EAAE,mBAAmB;cAAEC,YAAY,EAAE;YAAE,CAAE;YAAAnR,QAAA,EAChH0I,oBAAoB,CAACqI,aAAa,CAACK,kBAAkB,gBACpDlS,OAAA;cAAAc,QAAA,gBACEd,OAAA;gBAAGiB,KAAK,EAAE;kBAAEiB,KAAK,EAAE,SAAS;kBAAE6N,UAAU,EAAE;gBAAO,CAAE;gBAAAjP,QAAA,EAAC;cAAW;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACnEtB,OAAA;gBAAAc,QAAA,gBAAGd,OAAA;kBAAAc,QAAA,EAAQ;gBAAK;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACkI,oBAAoB,CAACqI,aAAa,CAACM,aAAa;cAAA;gBAAAhR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAChFtB,OAAA;gBAAAc,QAAA,gBAAGd,OAAA;kBAAAc,QAAA,EAAQ;gBAAO;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACkI,oBAAoB,CAACqI,aAAa,CAACO,kBAAkB;cAAA;gBAAAjR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpF,CAAC,gBAENtB,OAAA;cAAAc,QAAA,gBACEd,OAAA;gBAAGiB,KAAK,EAAE;kBAAEiB,KAAK,EAAE,SAAS;kBAAE6N,UAAU,EAAE;gBAAO,CAAE;gBAAAjP,QAAA,EAAC;cAAU;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAClEtB,OAAA;gBAAAc,QAAA,gBAAGd,OAAA;kBAAAc,QAAA,EAAQ;gBAAK;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACkI,oBAAoB,CAACqI,aAAa,CAAC5G,KAAK;cAAA;gBAAA9J,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrE;UACN;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CACN,EAGAkI,oBAAoB,IAAIA,oBAAoB,CAACa,OAAO,iBACnDrK,OAAA;YAAAc,QAAA,gBACEd,OAAA,CAACE,IAAI;cAAC2C,MAAM;cAAA/B,QAAA,EAAC;YAAc;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAClCtB,OAAA,CAACnC,MAAM;cACL6D,KAAK,EAAEgI,sBAAuB;cAC9BuC,QAAQ,EAAEtC,yBAA0B;cACpC1I,KAAK,EAAE;gBAAEC,KAAK,EAAE,MAAM;gBAAEkB,SAAS,EAAE;cAAE,CAAE;cACvCuO,WAAW,EAAC,8DAAY;cAAA7P,QAAA,EAEvBwJ,MAAM,CAACC,IAAI,CAACf,oBAAoB,CAACa,OAAO,CAAC,CAACrH,GAAG,CAAEsP,GAAG,iBACjDtS,OAAA,CAACI,MAAM;gBAAWsB,KAAK,EAAE4Q,GAAI;gBAAAxR,QAAA,EAC1BwR;cAAG,GADOA,GAAG;gBAAAnR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAER,CACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CACN,EAGAoI,sBAAsB,IAAIF,oBAAoB,IAAIA,oBAAoB,CAACa,OAAO,CAACX,sBAAsB,CAAC,iBACrG1J,OAAA,CAACM,qBAAqB;YACpBC,SAAS,EAAEmJ,sBAAuB;YAClClJ,MAAM,EAAEgJ,oBAAoB,CAACa,OAAO,CAACX,sBAAsB;UAAE;YAAAvI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9D,CACF;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CACP;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAAC2D,EAAA,CA/xCID,iBAA2B;EAAA,QAqD8CnF,cAAc;AAAA;AAAAgT,GAAA,GArDvF7N,iBAA2B;AAiyCjC,eAAeA,iBAAiB;AAAC,IAAAD,EAAA,EAAA8N,GAAA;AAAAC,YAAA,CAAA/N,EAAA;AAAA+N,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module"}
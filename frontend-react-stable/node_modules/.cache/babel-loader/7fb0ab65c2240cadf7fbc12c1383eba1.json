{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/frontend-react-stable/src/pages/LoginPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect } from 'react';\nimport { Form, Input, Button, Card, Typography, Alert, Space } from 'antd';\nimport { UserOutlined, LockOutlined } from '@ant-design/icons';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { useNavigate } from 'react-router-dom';\nimport { loginAsync, clearError } from '../store/slices/authSlice';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text\n} = Typography;\nconst LoginPage = () => {\n  _s();\n  const [form] = Form.useForm();\n  const dispatch = useDispatch();\n  const navigate = useNavigate();\n  const {\n    loading,\n    error,\n    isAuthenticated\n  } = useSelector(state => state.auth);\n  useEffect(() => {\n    if (isAuthenticated) {\n      navigate('/');\n    }\n  }, [isAuthenticated, navigate]);\n  useEffect(() => {\n    // 清除之前的错误信息\n    return () => {\n      dispatch(clearError());\n    };\n  }, [dispatch]);\n  const handleSubmit = async values => {\n    try {\n      await dispatch(loginAsync(values)).unwrap();\n      // 登录成功后会通过useEffect自动跳转\n    } catch (error) {\n      // 错误已经在store中处理\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      minHeight: '100vh',\n      background: 'linear-gradient(to bottom, #e6f3ff, #ffffff)',\n      display: 'flex',\n      alignItems: 'center',\n      justifyContent: 'center',\n      padding: '20px'\n    },\n    children: /*#__PURE__*/_jsxDEV(Card, {\n      style: {\n        width: '100%',\n        maxWidth: 400,\n        boxShadow: '0 4px 16px rgba(0, 0, 0, 0.1)',\n        borderRadius: '8px'\n      },\n      children: /*#__PURE__*/_jsxDEV(Space, {\n        direction: \"vertical\",\n        size: \"large\",\n        style: {\n          width: '100%',\n          textAlign: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Title, {\n            level: 2,\n            style: {\n              color: '#1890ff',\n              marginBottom: 8\n            },\n            children: \"AI\\u667A\\u80FD\\u6E05\\u6D17\\u7B56\\u7565\\u7CFB\\u7EDF\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 64,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Text, {\n            type: \"secondary\",\n            children: \"\\u57FA\\u4E8E\\u673A\\u5668\\u5B66\\u4E60\\u7684\\u7F51\\u7EDC\\u6D41\\u91CF\\u5F02\\u5E38\\u68C0\\u6D4B\\u7CFB\\u7EDF\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 67,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 63,\n          columnNumber: 11\n        }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n          message: \"\\u767B\\u5F55\\u5931\\u8D25\",\n          description: error,\n          type: \"error\",\n          showIcon: true,\n          closable: true,\n          onClose: () => dispatch(clearError())\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 73,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Form, {\n          form: form,\n          name: \"login\",\n          onFinish: handleSubmit,\n          autoComplete: \"off\",\n          size: \"large\",\n          children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"username\",\n            rules: [{\n              required: true,\n              message: '请输入用户名!'\n            }, {\n              min: 2,\n              message: '用户名至少2个字符!'\n            }],\n            children: /*#__PURE__*/_jsxDEV(Input, {\n              prefix: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 98,\n                columnNumber: 25\n              }, this),\n              placeholder: \"\\u8BF7\\u8F93\\u5165\\u7528\\u6237\\u540D\",\n              autoComplete: \"username\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 97,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 90,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"password\",\n            rules: [{\n              required: true,\n              message: '请输入密码!'\n            }],\n            children: /*#__PURE__*/_jsxDEV(Input.Password, {\n              prefix: /*#__PURE__*/_jsxDEV(LockOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 111,\n                columnNumber: 25\n              }, this),\n              placeholder: \"\\u8BF7\\u8F93\\u5165\\u5BC6\\u7801\",\n              autoComplete: \"current-password\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 110,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 104,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              type: \"primary\",\n              htmlType: \"submit\",\n              loading: loading,\n              style: {\n                width: '100%'\n              },\n              children: loading ? '登录中...' : '登录'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 118,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 117,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 83,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            textAlign: 'center'\n          },\n          children: /*#__PURE__*/_jsxDEV(Text, {\n            type: \"secondary\",\n            style: {\n              fontSize: '12px'\n            },\n            children: \"\\xA9 2024 AI\\u667A\\u80FD\\u6E05\\u6D17\\u7B56\\u7565\\u7CFB\\u7EDF. All rights reserved.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 130,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 129,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 62,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 54,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 46,\n    columnNumber: 5\n  }, this);\n};\n_s(LoginPage, \"LwZSv4ykzxgNbfBT9nQgidibcoU=\", false, function () {\n  return [Form.useForm, useDispatch, useNavigate, useSelector];\n});\n_c = LoginPage;\nexport default LoginPage;\nvar _c;\n$RefreshReg$(_c, \"LoginPage\");", "map": {"version": 3, "names": ["React", "useEffect", "Form", "Input", "<PERSON><PERSON>", "Card", "Typography", "<PERSON><PERSON>", "Space", "UserOutlined", "LockOutlined", "useDispatch", "useSelector", "useNavigate", "loginAsync", "clearError", "jsxDEV", "_jsxDEV", "Title", "Text", "LoginPage", "_s", "form", "useForm", "dispatch", "navigate", "loading", "error", "isAuthenticated", "state", "auth", "handleSubmit", "values", "unwrap", "style", "minHeight", "background", "display", "alignItems", "justifyContent", "padding", "children", "width", "max<PERSON><PERSON><PERSON>", "boxShadow", "borderRadius", "direction", "size", "textAlign", "level", "color", "marginBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "message", "description", "showIcon", "closable", "onClose", "name", "onFinish", "autoComplete", "<PERSON><PERSON>", "rules", "required", "min", "prefix", "placeholder", "Password", "htmlType", "fontSize", "_c", "$RefreshReg$"], "sources": ["/home/<USER>/frontend-react-stable/src/pages/LoginPage.tsx"], "sourcesContent": ["import React, { useEffect } from 'react';\nimport { Form, Input, Button, Card, Typography, Alert, Space } from 'antd';\nimport { UserOutlined, LockOutlined } from '@ant-design/icons';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { useNavigate } from 'react-router-dom';\nimport { RootState } from '../store/store';\nimport { loginAsync, clearError } from '../store/slices/authSlice';\n\nconst { Title, Text } = Typography;\n\ninterface LoginFormValues {\n  username: string;\n  password: string;\n}\n\nconst LoginPage: React.FC = () => {\n  const [form] = Form.useForm();\n  const dispatch = useDispatch();\n  const navigate = useNavigate();\n  \n  const { loading, error, isAuthenticated } = useSelector((state: RootState) => state.auth);\n\n  useEffect(() => {\n    if (isAuthenticated) {\n      navigate('/');\n    }\n  }, [isAuthenticated, navigate]);\n\n  useEffect(() => {\n    // 清除之前的错误信息\n    return () => {\n      dispatch(clearError());\n    };\n  }, [dispatch]);\n\n  const handleSubmit = async (values: LoginFormValues) => {\n    try {\n      await dispatch(loginAsync(values)).unwrap();\n      // 登录成功后会通过useEffect自动跳转\n    } catch (error) {\n      // 错误已经在store中处理\n    }\n  };\n\n  return (\n    <div style={{\n      minHeight: '100vh',\n      background: 'linear-gradient(to bottom, #e6f3ff, #ffffff)',\n      display: 'flex',\n      alignItems: 'center',\n      justifyContent: 'center',\n      padding: '20px',\n    }}>\n      <Card\n        style={{\n          width: '100%',\n          maxWidth: 400,\n          boxShadow: '0 4px 16px rgba(0, 0, 0, 0.1)',\n          borderRadius: '8px',\n        }}\n      >\n        <Space direction=\"vertical\" size=\"large\" style={{ width: '100%', textAlign: 'center' }}>\n          <div>\n            <Title level={2} style={{ color: '#1890ff', marginBottom: 8 }}>\n              AI智能清洗策略系统\n            </Title>\n            <Text type=\"secondary\">\n              基于机器学习的网络流量异常检测系统\n            </Text>\n          </div>\n\n          {error && (\n            <Alert\n              message=\"登录失败\"\n              description={error}\n              type=\"error\"\n              showIcon\n              closable\n              onClose={() => dispatch(clearError())}\n            />\n          )}\n\n          <Form\n            form={form}\n            name=\"login\"\n            onFinish={handleSubmit}\n            autoComplete=\"off\"\n            size=\"large\"\n          >\n            <Form.Item\n              name=\"username\"\n              rules={[\n                { required: true, message: '请输入用户名!' },\n                { min: 2, message: '用户名至少2个字符!' },\n              ]}\n            >\n              <Input\n                prefix={<UserOutlined />}\n                placeholder=\"请输入用户名\"\n                autoComplete=\"username\"\n              />\n            </Form.Item>\n\n            <Form.Item\n              name=\"password\"\n              rules={[\n                { required: true, message: '请输入密码!' },\n              ]}\n            >\n              <Input.Password\n                prefix={<LockOutlined />}\n                placeholder=\"请输入密码\"\n                autoComplete=\"current-password\"\n              />\n            </Form.Item>\n\n            <Form.Item>\n              <Button\n                type=\"primary\"\n                htmlType=\"submit\"\n                loading={loading}\n                style={{ width: '100%' }}\n              >\n                {loading ? '登录中...' : '登录'}\n              </Button>\n            </Form.Item>\n          </Form>\n\n          <div style={{ textAlign: 'center' }}>\n            <Text type=\"secondary\" style={{ fontSize: '12px' }}>\n              © 2024 AI智能清洗策略系统. All rights reserved.\n            </Text>\n          </div>\n        </Space>\n      </Card>\n    </div>\n  );\n};\n\nexport default LoginPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,SAASC,IAAI,EAAEC,KAAK,EAAEC,MAAM,EAAEC,IAAI,EAAEC,UAAU,EAAEC,KAAK,EAAEC,KAAK,QAAQ,MAAM;AAC1E,SAASC,YAAY,EAAEC,YAAY,QAAQ,mBAAmB;AAC9D,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,WAAW,QAAQ,kBAAkB;AAE9C,SAASC,UAAU,EAAEC,UAAU,QAAQ,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnE,MAAM;EAAEC,KAAK;EAAEC;AAAK,CAAC,GAAGb,UAAU;AAOlC,MAAMc,SAAmB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChC,MAAM,CAACC,IAAI,CAAC,GAAGpB,IAAI,CAACqB,OAAO,CAAC,CAAC;EAC7B,MAAMC,QAAQ,GAAGb,WAAW,CAAC,CAAC;EAC9B,MAAMc,QAAQ,GAAGZ,WAAW,CAAC,CAAC;EAE9B,MAAM;IAAEa,OAAO;IAAEC,KAAK;IAAEC;EAAgB,CAAC,GAAGhB,WAAW,CAAEiB,KAAgB,IAAKA,KAAK,CAACC,IAAI,CAAC;EAEzF7B,SAAS,CAAC,MAAM;IACd,IAAI2B,eAAe,EAAE;MACnBH,QAAQ,CAAC,GAAG,CAAC;IACf;EACF,CAAC,EAAE,CAACG,eAAe,EAAEH,QAAQ,CAAC,CAAC;EAE/BxB,SAAS,CAAC,MAAM;IACd;IACA,OAAO,MAAM;MACXuB,QAAQ,CAACT,UAAU,CAAC,CAAC,CAAC;IACxB,CAAC;EACH,CAAC,EAAE,CAACS,QAAQ,CAAC,CAAC;EAEd,MAAMO,YAAY,GAAG,MAAOC,MAAuB,IAAK;IACtD,IAAI;MACF,MAAMR,QAAQ,CAACV,UAAU,CAACkB,MAAM,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC;MAC3C;IACF,CAAC,CAAC,OAAON,KAAK,EAAE;MACd;IAAA;EAEJ,CAAC;EAED,oBACEV,OAAA;IAAKiB,KAAK,EAAE;MACVC,SAAS,EAAE,OAAO;MAClBC,UAAU,EAAE,8CAA8C;MAC1DC,OAAO,EAAE,MAAM;MACfC,UAAU,EAAE,QAAQ;MACpBC,cAAc,EAAE,QAAQ;MACxBC,OAAO,EAAE;IACX,CAAE;IAAAC,QAAA,eACAxB,OAAA,CAACZ,IAAI;MACH6B,KAAK,EAAE;QACLQ,KAAK,EAAE,MAAM;QACbC,QAAQ,EAAE,GAAG;QACbC,SAAS,EAAE,+BAA+B;QAC1CC,YAAY,EAAE;MAChB,CAAE;MAAAJ,QAAA,eAEFxB,OAAA,CAACT,KAAK;QAACsC,SAAS,EAAC,UAAU;QAACC,IAAI,EAAC,OAAO;QAACb,KAAK,EAAE;UAAEQ,KAAK,EAAE,MAAM;UAAEM,SAAS,EAAE;QAAS,CAAE;QAAAP,QAAA,gBACrFxB,OAAA;UAAAwB,QAAA,gBACExB,OAAA,CAACC,KAAK;YAAC+B,KAAK,EAAE,CAAE;YAACf,KAAK,EAAE;cAAEgB,KAAK,EAAE,SAAS;cAAEC,YAAY,EAAE;YAAE,CAAE;YAAAV,QAAA,EAAC;UAE/D;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACRtC,OAAA,CAACE,IAAI;YAACqC,IAAI,EAAC,WAAW;YAAAf,QAAA,EAAC;UAEvB;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,EAEL5B,KAAK,iBACJV,OAAA,CAACV,KAAK;UACJkD,OAAO,EAAC,0BAAM;UACdC,WAAW,EAAE/B,KAAM;UACnB6B,IAAI,EAAC,OAAO;UACZG,QAAQ;UACRC,QAAQ;UACRC,OAAO,EAAEA,CAAA,KAAMrC,QAAQ,CAACT,UAAU,CAAC,CAAC;QAAE;UAAAqC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CACF,eAEDtC,OAAA,CAACf,IAAI;UACHoB,IAAI,EAAEA,IAAK;UACXwC,IAAI,EAAC,OAAO;UACZC,QAAQ,EAAEhC,YAAa;UACvBiC,YAAY,EAAC,KAAK;UAClBjB,IAAI,EAAC,OAAO;UAAAN,QAAA,gBAEZxB,OAAA,CAACf,IAAI,CAAC+D,IAAI;YACRH,IAAI,EAAC,UAAU;YACfI,KAAK,EAAE,CACL;cAAEC,QAAQ,EAAE,IAAI;cAAEV,OAAO,EAAE;YAAU,CAAC,EACtC;cAAEW,GAAG,EAAE,CAAC;cAAEX,OAAO,EAAE;YAAa,CAAC,CACjC;YAAAhB,QAAA,eAEFxB,OAAA,CAACd,KAAK;cACJkE,MAAM,eAAEpD,OAAA,CAACR,YAAY;gBAAA2C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACzBe,WAAW,EAAC,sCAAQ;cACpBN,YAAY,EAAC;YAAU;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC,eAEZtC,OAAA,CAACf,IAAI,CAAC+D,IAAI;YACRH,IAAI,EAAC,UAAU;YACfI,KAAK,EAAE,CACL;cAAEC,QAAQ,EAAE,IAAI;cAAEV,OAAO,EAAE;YAAS,CAAC,CACrC;YAAAhB,QAAA,eAEFxB,OAAA,CAACd,KAAK,CAACoE,QAAQ;cACbF,MAAM,eAAEpD,OAAA,CAACP,YAAY;gBAAA0C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACzBe,WAAW,EAAC,gCAAO;cACnBN,YAAY,EAAC;YAAkB;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC,eAEZtC,OAAA,CAACf,IAAI,CAAC+D,IAAI;YAAAxB,QAAA,eACRxB,OAAA,CAACb,MAAM;cACLoD,IAAI,EAAC,SAAS;cACdgB,QAAQ,EAAC,QAAQ;cACjB9C,OAAO,EAAEA,OAAQ;cACjBQ,KAAK,EAAE;gBAAEQ,KAAK,EAAE;cAAO,CAAE;cAAAD,QAAA,EAExBf,OAAO,GAAG,QAAQ,GAAG;YAAI;cAAA0B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,eAEPtC,OAAA;UAAKiB,KAAK,EAAE;YAAEc,SAAS,EAAE;UAAS,CAAE;UAAAP,QAAA,eAClCxB,OAAA,CAACE,IAAI;YAACqC,IAAI,EAAC,WAAW;YAACtB,KAAK,EAAE;cAAEuC,QAAQ,EAAE;YAAO,CAAE;YAAAhC,QAAA,EAAC;UAEpD;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAAClC,EAAA,CA1HID,SAAmB;EAAA,QACRlB,IAAI,CAACqB,OAAO,EACVZ,WAAW,EACXE,WAAW,EAEgBD,WAAW;AAAA;AAAA8D,EAAA,GALnDtD,SAAmB;AA4HzB,eAAeA,SAAS;AAAC,IAAAsD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module"}
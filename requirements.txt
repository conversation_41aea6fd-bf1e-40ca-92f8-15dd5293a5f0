# AI智能清洗策略系统 - Python依赖包

# ===== 核心Web框架 =====
fastapi==0.104.1
uvicorn[standard]==0.24.0

# ===== 数据处理和科学计算 =====
pandas==2.1.3
numpy==1.24.3

# ===== 机器学习和深度学习 =====
torch==2.1.1
torchvision==0.16.1
torchaudio==2.1.1
scikit-learn==1.3.2
joblib==1.3.2

# ===== 认证和安全 =====
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6
bcrypt==4.1.2

# ===== 数据验证和模型 =====
pydantic==2.5.0

# ===== 文件处理和I/O =====
python-dateutil==2.8.2
openpyxl==3.1.2

# ===== 系统和环境 =====
python-dotenv==1.0.0
psutil==5.9.6

# ===== 生产环境部署 =====
gunicorn==21.2.0

# ===== 开发和测试工具 =====
pytest==7.4.3
pytest-asyncio==0.21.1
black==23.11.0
flake8==6.1.0

# GPU监控
# pynvml==11.5.0


# HTTP客户端 (如果需要外部API调用)
# requests==2.31.0
# httpx==0.25.2
